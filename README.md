

# Let's Play Group Lottery


## Setup Guide

### 1. Connect to GitHub

To connect to your GitHub repository, use the following commands:

**Set Remote URL:**
```bash
git remote set-url origin https://x-token-auth:ATCTT3xFfGN0p3jz8sM4Wd-Nl_2npukQ8Mxmytd7TPxNygt_WtlyuCKeTOVSpmUrga2nFlnbkqYKqZfo2vQf3KfHU2k4REqw6GBpSdydLox3vZKTZ7uuFaiVVJav1JSMM4x5r7rEXri5X-wVe18Q_GRbbSYF9fNED21sDxWkhSwcIRYS7Od_dnA=<EMAIL>/errol3491/letsplaygroup_latest_updated.git
```

**Clone Repository:**
```bash
git clone https://x-token-auth:ATCTT3xFfGN0p3jz8sM4Wd-Nl_2npukQ8Mxmytd7TPxNygt_WtlyuCKeTOVSpmUrga2nFlnbkqYKqZfo2vQf3KfHU2k4REqw6GBpSdydLox3vZKTZ7uuFaiVVJav1JSMM4x5r7rEXri5X-wVe18Q_GRbbSYF9fNED21sDxWkhSwcIRYS7Od_dnA=<EMAIL>/errol3491/letsplaygroup_latest_updated.git
```

**Push Code:**
```bash
git config user.email <EMAIL>
```

### 2. Steps to Access IONOS Server and Plesk ( `letsplaylotterygroup.com` )


1. After signing in into IONOS,On the IONOS Dashboard click on `Servers & Cloud`.
2. Click on `IONOS Virtual Server Cloud M` Associated with Domains `letsplaylotterygroup.com` Contract: `86206307 - IONOS Virtual Server Cloud M`.
3. Under Infrastructure, click on `Servers`.
4. Next you will see `letsplaygrouplotter.com` server.
5. Selecting the server will show you the server details.
6. Now you will see the `login data` section and `Plesk` section.
7. Click on Plesk `Admin area:` link visible.
8. Start by entering the below credentials:



### 3. Plesk Credentials

**Plesk Admin Interface:**
- **Email:** ```<EMAIL>```
- **Username:**  ```admin```
- **Password:** ```letsplay@007```

### 4. Setup under Plesk

1. Click on `Domains`
2. Click on `letsplaylotterygroup.com`
3. Now you will see the Dashboard
   1. The Dashboard contains Node.js, Databases, SSL/TLS Certificates, and other settings.
   
#### MySQL

1. **Access Plesk Control Panel**: Login using your Plesk credentials.
2. **Navigate to Databases**: Go to **Databases** and click **Add Database**.
3. **Create Database**: Enter the database name and create a new user with appropriate permissions.

#### Node.js

1. **Navigate to Node.js**: Go to **Node.js** under the domain settings.
2. **Install Node.js**: Choose the version you need and install it.

#### SSL Setup

1. **Access SSL/TLS Certificates**: Go to **SSL/TLS Certificates** in Plesk.
2. **Add Certificate**: Either upload a certificate or use Let’s Encrypt to secure your domain.
3. **Apply Certificate**: Apply the SSL certificate to your domain.

