include ./header.pug
// ======= Intro Section =======
section#intro.clearfix
  .container(data-aos='fade-up')
    .intro-img(data-aos='zoom-out' data-aos-delay='200')
// End Intro Section
main#main
  // ======= Testimonials Section =======
  section#contact
    .container-fluid(data-aos='fade-up')
      .row
        .col-lg-12
          .section-header
            h3 Payment details
          .form
            form(method='post',name="getSessionTokenForm")
              .form-row
                .form-group.col-lg-3
                  label First Name
                .form-group.col-lg-9
                  input#firstname.form-control(type='text' name='firstname' value=firstname placeholder='First Name')
                  .validate
                .form-group.col-lg-3
                  label Last Name
                  .validate
                .form-group.col-lg-9
                  input#lastname.form-control(type='text' name='lastname' value=lastname  placeholder='Last Name')
                  .validate
                .form-group.col-lg-3
                  label Transaction Amount
                  .validate
                .form-group.col-lg-9
                  input#amount.form-control(type='text' name='amount' value=amount   placeholder='Transaction Amount')
                .form-group.col-lg-3
                  label Card Number
                .form-group.col-lg-9
                  input#card.form-control(type='text' name='card' value=card  placeholder='Card')
                  .validate
                .form-group.col-lg-3
                  label Expiry Date
                .form-group.col-lg-9
                  input#exp.form-control(type='text' name='exp' value=exp placeholder='Expiry Date')
                  .validate
                .form-group.col-lg-3
                  label Merchant TXN ID
                .form-group.col-lg-9
                  input#merchanttxnid.form-control(type='text' name='merchanttxnid' placeholder='Merchant TXN ID' value=txid)
                .form-group.col-lg-3
                  label Zip code
                .form-group.col-lg-9
                    input#zip.form-control(type="text" name="zip" value="1A1A1")
                .form-group.col-lg-3
                  label Address
                .form-group.col-lg-9
                    input#address.form-control(type="text" name="address" value="Canada")
                .form-group.col-lg-3
                  label Status
                .form-group.col-lg-9
                    input#statusflg.form-control(type="text" name="statusflg" value=statusflg)
                .form-group.col-lg-3
                  label Message
                .form-group.col-lg-9
                      input#statusmessage.form-control(type="text" name="statusmessage" value=statusmessage)
                .form-group.col-lg-3
                  label Other error
                .form-group.col-lg-9
                      input#app_error1.form-control(type="text" name="app_error1" value=app_error1)
                .form-group.col-lg-3
                      input#cvv.form-control(type="hidden" name="cvv" value=cvv)
                      p| <input type='hidden' id='token' name='token' class="form-control"/><input type='hidden' id='gettoken' name='gettoken' value='y' class="form-control"/><input type='hidden' id='addtoken' name='addtoken' value='y' class="form-control"/><input type='hidden' id='zip' name='zip' value='1A1A1' class="form-control"/><input type='hidden' id='address' name='address' value='Canada' class="form-control"/>
  // End Contact Section
// End #main
include ./footer.pug
script.
  jQuery(document).ready(function() {
  if(document.getElementById('app_error1').value == ''){
    initiateCheckoutJS();
    //alert("wait");
  }else{
    alert("Lottery sold out completely !!! Please select another lottery.");
  }
  });