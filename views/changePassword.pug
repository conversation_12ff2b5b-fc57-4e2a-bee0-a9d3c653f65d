
include ./header.pug

block content
    .container.mt-5
        .row.p-spacer
            .col-lg-4
            .col-lg-4.col-md-8.col-sm-8.p-spacer
                div(style="display:block;box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.6)")
                   .card
                        .card-header.text-center
                            | Password Update
                        .card-body.bodyBgImage
                            form(method='post')
                                .form-group
                                    label(for='Email Address')  Email Address :- 
                                    input#email.form-control(type='email', name='emailAddress', placeholder='Enter the  Email')
                                button.btn.btn-lg.btn-primary.btn-block(type='submit') Log In

script.

    let searchParams = new URLSearchParams(window.location.search);
    if(searchParams.has('text')){
        alert(searchParams.get('text')) ;
    }