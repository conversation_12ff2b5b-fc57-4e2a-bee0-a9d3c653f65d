extends admin-layout

block content   
         section#contents.container
                div.add-section
                     h2.form-header Add Lottery 
                     form.form-section(action='lottery/add-lottery' method='POST' enctype="multipart/form-data")
                            .row
                                   .col-md-3
                                          label(for='inputLotteryName') Lottery Name
                                          select#inputLotteryName.form-control(name="inputLotteryName" required)
                                              option(value='') -- Select Lottery Name --
                                              option(value='lotto 6/49') lotto 6/49
                                              option(value='lotto max') lotto max
                                              option(value='sick kids lottery') sick kids lottery
                                              option(value='Heart and Stroke foundation') Heart and Stroke foundation
                                              option(value='Canadian Cancer Society') Canadian Cancer Society
                                              option(value='Princess Margaret') Princess Margaret
                                              option(value='Rotary') Rotary
                                              option(value='Ultimate Dream Home Lottery') Ultimate Dream Home Lottery
                                   .col-md-3
                                          label(for='inputStartDate') Lottery start Date
                                          input#inputStartDate.form-control(type='date',value='2024-09-18',placeholder='select Start day..',name="inputStartDate" required)
                                   .col-md-3
                                          label(for='inputEndDate') Lottery End Date
                                          input#inputEndDate.form-control(type='date',value='2024-10-10',placeholder='select End day..',name="inputEndDate" required)
                                   .col-md-3
                                          label(for="inputLastDateJoin") Last Date of Joining
                                          input#inputDrawDate.form-control(type='date',placeholder='select Draw day..',name="inputLastDateJoin" required)
                                   .col-md-3
                                          label(for='inputBumperPrice') Bumper Price
                                          input#inputBumperPrice.form-control(type='number',placeholder="Enter the bumper price..",name="inputBumperPrice" required)

                                   .col-md-3
                                          label(for='groupNumber') Group
                                          select#inputGroupNumber.form-control(name="inputGroupNumber" required)
                                              option(value='') -- Select Number of Groups --
                                              option(value='1') A (1 Group)
                                              option(value='2') A, B (2 Groups)
                                              option(value='3') A, B, C (3 Groups)
                                              option(value='4') A, B, C, D (4 Groups)
                                   .col-md-3
                                          label(for='personpergroup') Number of Person in Group
                                          input#inputPersonPerGroup.form-control(type='number',placeholder="Number of Group in Lottery..",name="inputPersonPerGroup")
                                   .col-md-3
                                          label(for='country') Country
                                          input#inputCountry.form-control(type='text',placeholder="Enter the Country..",name="inputCountry" required)
                                   .col-md-3
                                          label(for='state') State
                                          input#inputState.form-control(type='text',placeholder="Enter the State..",name="inputState" required)
                                   .col-md-3
                                          label(for='subscription') Registration Price
                                          input#inputState.form-control(type='number',placeholder="Enter the State..",name="inputSubscription" required) 
                                   .col-md-3
                                          label(for='inputLotterYPrice') Lottery Price
                                          input#inputWinningPrice.form-control(type='number',placeholder="Enter the Lottery price..",name="inputLotteryrice" required)
                                   .col-md-3
                                          label(for='IsEncore') Is Encore
                                          select#exampleFormControlSelect2.form-control( name="isEncore")
                                                 option(value='1') Yes
                                                 option(value='0') No
                                   .col-md-3
                                          label(for='quickPick') Is QuickPick
                                          select#exampleFormControlSelect2.form-control( name="quickpick")
                                                 option(value='1') Yes
                                                 option(value='0') No   
                                   .col-md-3
                                          label(for='quickPick') Customer Registration Number
                                          select#exampleFormControlSelect2.form-control( name="customeNumber")
                                                 option(value='1') Yes
                                                 option(value='0') No                                        
                                   .col-md-3
                                          label(for='subscription') Image File
                                          input#inputState.form-control(type='file',placeholder="upload banner image",name="imageFile" required) 
                                                                      
                                
                            .row
                                   button.btn.btn-primary Add Lottery

                div.view-sectiopn
                  table#lotterySection.display(style='width:100%')
                
                div
                       .modal-dialog.modal-dialog-centered.modal-lg(role='document')
                         .modal-content
                            .modal-header
                                   h2.form-header Update Lottery
                                   button.close(type='button' data-dismiss='modal' aria-label='Close')
                                          span(aria-hidden='true') &times;
                            .modal-body#baground_col
                                   .row
                                     form.form-section(action='lottery/update-lottery' method='POST')
                                          .row
                                                 .col-md-3
                                                        label(for='inputSelectLotteryId') Select Lottery Name
                                                        select#inputSelectLotteryId.form-control(name="selectedLotteryName")
                                                               option(value='') -- Select Lottery Name --
                                                               option(value='lotto 6/49') lotto 6/49
                                                               option(value='lotto max') lotto max
                                                               option(value='sick kids lottery') sick kids lottery
                                                               option(value='Heart and Stroke foundation') Heart and Stroke foundation
                                                               option(value='Canadian Cancer Society') Canadian Cancer Society
                                                               option(value='Princess Margaret') Princess Margaret
                                                               option(value='Rotary') Rotary
                                                               option(value='Ultimate Dream Home Lottery') Ultimate Dream Home Lottery
                                                        input#hiddenLotteryId(type='hidden' name="lotteryId")
                                                 .col-md-3
                                                        label(for='inputLotteryName') Lottery Name
                                                        input#lotteryName.form-control(type='text',placeholder='Enter the Lotter Name..',name="lotteryName")
                                                 .col-md-3
                                                        label(for='inputStartDate') Lottery start Date
                                                        input#lotteryStartDate.form-control(type='date',placeholder='select Start day..',name="lotteryStartDate")
                                                 .col-md-3
                                                        label(for='inputEndDate') Lottery End Date
                                                        input#lotteryEndDate.form-control(type='date',placeholder='select End day..',name="lotteryEndDate")
                                                 .col-md-3
                                                        label(for="inputLastDateJoin") Last Date of Joining
                                                        input#lotteryDrawDate.form-control(type='date',placeholder='select Draw day..',name="inputLastDateJoin")
                                                 .col-md-3
                                                        label(for='inputPrice') Price
                                                        input#lotteryPrice.form-control(type='number',placeholder='Enter the lottery ticket price..',name="lotteryPrice")
                                                 .col-md-3
                                                        label(for='inputBumperPrice') Bumper Price
                                                        input#lotteryBumperPrice.form-control(type='number',placeholder="Enter the wining price..",name="lotteryBumperPrice")
                                                 .col-md-3
                                                        label(for='groupNumber') Group
                                                        select#lotteryGroupNumber.form-control(name="lotteryGroupNumber")
                                                            option(value='') -- Select Number of Groups --
                                                            option(value='1') A (1 Group)
                                                            option(value='2') A, B (2 Groups)
                                                            option(value='3') A, B, C (3 Groups)
                                                            option(value='4') A, B, C, D (4 Groups)
                                                 .col-md-3
                                                        label(for='personpergroup') Number of Person in Group
                                                        input#lotteryPersonPerGroup.form-control(type='number',placeholder="Number of Group in Lottery..",name="lotteryPersonPerGroup")
                                                 .col-md-3
                                                        label(for='country') Country
                                                        input#lotteryCountry.form-control(type='text',placeholder="Enter the Country..",name="lotteryCountry")
                                                 .col-md-3
                                                        label(for='state') State
                                                        input#lotteryState.form-control(type='text',placeholder="Enter the State..",name="lotteryState")
                                                 .col-md-3
                                                        label(for='subscriptionEntryPrice') Registration Price
                                                        input#lotterysubscriptionEntryPrice.form-control(type='text',placeholder="Enter the State..",name="lotterysubscriptionEntryPrice")
                                                 .col-md-3
                                                        label(for='isStatus') IsDelted
                                                        ul.tg-list
                                                               li.tg-list-item
                                                                      input#isDeleted.tgl.tgl-light(type = "checkbox"  name="isDeleted")
                                                                      label.tgl-btn(for = "isDeleted")
                                                 .col-md-3
                                                        label(for='isEncore') Is Encore
                                                        ul.tg-list
                                                               li.tg-list-item
                                                                      input#isEncore.tgl.tgl-light(type = "checkbox"  name="isEncore")
                                                                      label.tgl-btn(for = "isEncore")
                                                 .col-md-3
                                                        label(for='QuickPick') QuickPick
                                                        ul.tg-list
                                                               li.tg-list-item
                                                                      input#QuickPick.tgl.tgl-light(type = "checkbox"  name="QuickPick")
                                                                      label.tgl-btn(for = "QuickPick")
                                                 .col-md-3
                                                        label(for='customerNumber') Customer Registration Number
                                                        ul.tg-list
                                                               li.tg-list-item
                                                                      input#customerNumber.tgl.tgl-light(type = "checkbox"  name="customerNumber")
                                                                      label.tgl-btn(for = "customerNumber")
                                                 .col-md-3
                                                        label(for='isActive') IsActive
                                                        ul.tg-list
                                                               li.tg-list-item
                                                                      input#isActive.tgl.tgl-light(type = "checkbox"  name="isActive")
                                                                      label.tgl-btn(for = "isActive")
                                                 //- .col-md-3
                                                 //-        label(for='dark_Color') Card Color Dark  
                                                 //-        input#cardcolordark.form-control(type='color',name="cardColorDark" required)

                                                 //- .col-md-3
                                                 //-        label(for='light_color') Card Color Light  
                                                 //-        input#cardcolorlight.form-control(type='color',name="cardColorLight" required)
                                          
                                          .row
                                          button.btn.btn-primary update Lottery
                                   
                            

                   
block script
         script(src='/js/lottery.js')
