extends admin-layout

block content
    section#contents.container-fluid
        .add-section
            h2.form-header All Registered Users
            span.badge.bg-light.text-dark.ms-2= totalUsers + ' Total'

            // Search Section
            .row.mb-3
                .col-md-6
                    .input-group
                        input#searchInput.form-control(type="text" placeholder="Search by name, email, phone, or IP address...")
                        button#searchBtn.btn.btn-outline-secondary(type="button")
                            i.fa.fa-search
                .col-md-6.text-end
                    button#refreshBtn.btn.btn-outline-primary
                        i.fa.fa-refresh.me-1
                        | Refresh

            // Users Table
            .table-responsive
                table#usersTable.table.table-striped.table-hover.table-sm
                    thead.table-dark
                        tr
                            th.text-center(style="width: 4%") ID
                            th(style="width: 10%") First Name
                            th(style="width: 10%") Last Name
                            th(style="width: 18%") Email
                            th(style="width: 10%") Phone
                            th(style="width: 8%") City
                            th.text-center(style="width: 6%") Province
                            th.text-center(style="width: 12%") IP Address
                            th.text-center(style="width: 12%") Registration Date
                            th.text-center(style="width: 10%") Actions
                    tbody
                        if users && users.length > 0
                            each user in users
                                tr(data-user-id=user.id)
                                    td.text-center= user.id
                                    td.text-truncate(style="max-width: 100px;" title=user.first_name || 'N/A')= user.first_name || 'N/A'
                                    td.text-truncate(style="max-width: 100px;" title=user.last_name || 'N/A')= user.last_name || 'N/A'
                                    td.text-truncate(style="max-width: 160px;" title=user.email || 'N/A')= user.email || 'N/A'
                                    td.text-truncate(style="max-width: 100px;" title=user.phone_number || 'N/A')= user.phone_number || 'N/A'
                                    td.text-truncate(style="max-width: 80px;" title=user.city || 'N/A')= user.city || 'N/A'
                                    td.text-center= user.province || 'N/A'
                                    td.text-center.small(title=user.ip_address || 'N/A')
                                        code.text-primary= user.ip_address || 'N/A'
                                    td.text-center.small= user.registration_date
                                    td.text-center
                                        button.btn.btn-danger.btn-sm.delete-user(data-user-id=user.id data-user-name=user.first_name + ' ' + user.last_name title="Delete User")
                                            i.fa.fa-trash
                        else
                            tr
                                td.text-center(colspan="10") No users found

        // Delete Confirmation Modal
        .modal.fade#deleteModal(tabindex="-1")
            .modal-dialog
                .modal-content
                    .modal-header.bg-danger.text-white
                        h5.modal-title
                            i.fa.fa-exclamation-triangle.me-2
                            | Confirm Delete
                        button.close(type='button' data-dismiss='modal' aria-label='Close')
                            span(aria-hidden='true') &times;
                    .modal-body
                        p Are you sure you want to delete this user?
                        .alert.alert-warning
                            strong User:
                            span#deleteUserName
                        p.text-danger.small
                            i.fa.fa-warning.me-1
                            | This action cannot be undone!
                    .modal-footer
                        button.btn.btn-secondary(data-dismiss="modal") Cancel
                        button#confirmDelete.btn.btn-danger
                            i.fa.fa-trash.me-1
                            | Delete User

block script
    script.
        $(document).ready(function() {
            let currentUserId = null;

            // Delete user button click
            $(document).on('click', '.delete-user', function() {
                currentUserId = $(this).data('user-id');
                const userName = $(this).data('user-name');
                $('#deleteUserName').text(userName);
                $('#deleteModal').modal('show');
            });

            // Confirm delete
            $('#confirmDelete').click(function() {
                if (currentUserId) {
                    $.ajax({
                        url: `/admin/all-registered-users/delete/${currentUserId}`,
                        method: 'POST',
                        success: function(response) {
                            if (response.success) {
                                // Remove row from table
                                $(`tr[data-user-id="${currentUserId}"]`).fadeOut(function() {
                                    $(this).remove();
                                    updateUserCount();
                                });
                                $('#deleteModal').modal('hide');
                                alert(response.message);
                            } else {
                                alert(response.message);
                            }
                        },
                        error: function(xhr) {
                            const response = xhr.responseJSON || {};
                            alert(response.message || 'Error deleting user');
                        }
                    });
                }
            });

            // Search functionality
            $('#searchBtn, #searchInput').on('click keyup', function(e) {
                if (e.type === 'click' || e.keyCode === 13) {
                    const searchTerm = $('#searchInput').val();
                    searchUsers(searchTerm);
                }
            });

            // Refresh button
            $('#refreshBtn').click(function() {
                location.reload();
            });

            // Search users function
            function searchUsers(searchTerm) {
                $.ajax({
                    url: '/admin/all-registered-users/search',
                    method: 'GET',
                    data: { q: searchTerm },
                    success: function(response) {
                        if (response.success) {
                            updateUsersTable(response.users);
                            updateUserCount(response.totalUsers);
                        }
                    },
                    error: function() {
                        alert('Error searching users');
                    }
                });
            }

            // Update users table
            function updateUsersTable(users) {
                const tbody = $('#usersTable tbody');
                tbody.empty();

                if (users.length > 0) {
                    users.forEach(function(user) {
                        const row = `
                            <tr data-user-id="${user.id}">
                                <td class="text-center">${user.id}</td>
                                <td class="text-truncate" style="max-width: 100px;" title="${user.first_name || 'N/A'}">${user.first_name || 'N/A'}</td>
                                <td class="text-truncate" style="max-width: 100px;" title="${user.last_name || 'N/A'}">${user.last_name || 'N/A'}</td>
                                <td class="text-truncate" style="max-width: 160px;" title="${user.email || 'N/A'}">${user.email || 'N/A'}</td>
                                <td class="text-truncate" style="max-width: 100px;" title="${user.phone_number || 'N/A'}">${user.phone_number || 'N/A'}</td>
                                <td class="text-truncate" style="max-width: 80px;" title="${user.city || 'N/A'}">${user.city || 'N/A'}</td>
                                <td class="text-center">${user.province || 'N/A'}</td>
                                <td class="text-center small" title="${user.ip_address || 'N/A'}">
                                    <code class="text-primary">${user.ip_address || 'N/A'}</code>
                                </td>
                                <td class="text-center small">${user.registration_date}</td>
                                <td class="text-center">
                                    <button class="btn btn-danger btn-sm delete-user"
                                            data-user-id="${user.id}"
                                            data-user-name="${user.first_name} ${user.last_name}"
                                            title="Delete User">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td class="text-center" colspan="10">No users found</td></tr>');
                }
            }

            // Update user count
            function updateUserCount(count) {
                if (count !== undefined) {
                    $('.badge.bg-light.text-dark').text(count + ' Total');
                } else {
                    const currentCount = $('#usersTable tbody tr').length;
                    $('.badge.bg-light.text-dark').text(currentCount + ' Total');
                }
            }
        });

    style.
        .table-sm th, .table-sm td {
            padding: 0.3rem;
            font-size: 1.4rem;
        }
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .table-responsive {
            border-radius: 0.375rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table thead th {
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            font-size: 1.3rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .btn-sm {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
        }
        .badge {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
        }
        .form-header {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .add-section {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }