extends ../layout

block content
  .container-fluid
    .row
      .col-12
        h2.mt-4.mb-4 Contact Messages
        .table-responsive
          table.table.table-striped
            thead.thead-dark
              tr
                th ID
                th Name
                th Email
                th Type
                th Message
                th Status
                th Date
                th Actions
            tbody
              each message in messages
                tr(data-id=message.id class=message.status === 'new' ? 'table-warning' : '')
                  td= message.id
                  td #{message.first_name} #{message.last_name}
                  td= message.email
                  td= message.inquiry_type
                  td= message.message
                  td
                    span.badge(class=message.status === 'new' ? 'badge-warning' : message.status === 'read' ? 'badge-info' : 'badge-success')
                      = message.status
                  td= new Date(message.created_at).toLocaleString()
                  td
                    .btn-group
                      if message.status === 'new'
                        button.btn.btn-sm.btn-info.mark-read(data-id=message.id) Mark Read
                      if message.status !== 'replied'
                        button.btn.btn-sm.btn-success.mark-replied(data-id=message.id) Mark Replied

block scripts
  script.
    $(document).ready(function() {
      $('.mark-read, .mark-replied').click(function() {
        const btn = $(this);
        const messageId = btn.data('id');
        const newStatus = btn.hasClass('mark-read') ? 'read' : 'replied';
        
        $.ajax({
          url: `/contact-us/admin/messages/${messageId}`,
          method: 'PUT',
          data: { status: newStatus },
          success: function(response) {
            if (response.success) {
              location.reload();
            } else {
              alert('Failed to update status: ' + response.message);
            }
          },
          error: function() {
            alert('Failed to update message status');
          }
        });
      });
    });
