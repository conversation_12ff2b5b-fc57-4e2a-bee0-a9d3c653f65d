extends admin-layout

block content   
         section#contents.container-fluid
                .add-section  
                    h2.form-header Registered Users
                    div#groupSelection.row
                            table#userList
                div
                     #exampleModalCenter.modal.fade(tabindex='-1' role='dialog' aria-labelledby='exampleModalCenterTitle' aria-hidden='true')
                       .modal-dialog.modal-dialog-centered.modal-lg(role='document')
                         .modal-content
                            .modal-header
                                   h1#LotterSection.modal-title Update User Details
                                   button.close(type='button' data-dismiss='modal' aria-label='Close')
                                          span(aria-hidden='true') &times;
                            .modal-body
                                   .row
                                     form.form-section(onSubmit="updateUser(event)")
                                          .row
                                                 .col-md-3
                                                        label(for='inputFirstName') First Name
                                                        input#inputFirstName.form-control(type='text',placeholder='Enter First Name..',name="first_name",disabled,required)
                                                 .col-md-3
                                                        label(for='inputLastName') Last Name
                                                        input#inputLastName.form-control(type='text',placeholder='Enter the Last Name..',name="last_name", disabled, required)
                                                 .col-md-3
                                                        label(for='inputProfession') Profession
                                                        input#inputProfession.form-control(type='text',placeholder='Enter Profession',name="profession")
                                                 .col-md-3
                                                        label(for='inputEmail') Email
                                                        input#inputEmail.form-control(type='email',placeholder='Enter email',name="email",disabled,required)
                                                 .col-md-3
                                                        label(fomr="inputMobile") Contact Number
                                                        input#inputMobile.form-control(type='tel',placeholder='Enter Mobile Number',name="phone_number",required)
                                                 .col-md-3
                                                        label(for='inputStreet') Appt/Street Name
                                                        input#inputStreet.form-control(type='text',placeholder='Enter Appt/Street Number',name="address",required)
                                                 .col-md-3
                                                        label(for='inputCity') City
                                                        input#inputCity.form-control(type='text',placeholder="Enter City Name",name="city",required)
                                                 .col-md-3
                                                        label(for='inputProvince') Province
                                                        input#inputProvince.form-control(type='text',placeholder="Enter Province Name",name="province",disabled)
                                                 .col-md-3
                                                        label(for='inputPostalcode') Postal Code
                                                        input#inputPostalcode.form-control(type='text',placeholder="Postal Code",name="postal_code",disabled)
                                          input#user-id(type="hidden",name="id")
                                          .row
                                                 button.btn.btn-primary(type="submit") Update User Details
                                                 button.btn.btn-danger.ml-3(type="button" onclick="archiveUser()") Archive User
                                                 button.btn.btn-warning.ml-3(type="button" onclick="reactivateUser()") Activate User
block script
           script(src='/js/registered-users.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/dataTables.buttons.min.js')
           script(src='https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/buttons.html5.min.js')