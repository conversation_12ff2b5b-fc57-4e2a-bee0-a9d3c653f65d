extends admin-layout

block content
         section#contents.container
                .add-section
                    h2.form-header Group Members Info

                    // Success/Error Messages
                    if success
                        .alert.alert-success.alert-dismissible.fade.show(role='alert' style='margin-bottom: 20px; font-size: 16px;')
                            i.fas.fa-check-circle(style='margin-right: 8px;')
                            strong Success!
                            | #{success}
                            button.close(type='button' data-dismiss='alert' aria-label='Close')
                                span(aria-hidden='true') &times;

                    if error
                        .alert.alert-danger.alert-dismissible.fade.show(role='alert' style='margin-bottom: 20px; font-size: 16px;')
                            i.fas.fa-exclamation-triangle(style='margin-right: 8px;')
                            strong Error!
                            | #{error}
                            button.close(type='button' data-dismiss='alert' aria-label='Close')
                                span(aria-hidden='true') &times;
                    .row#user-section
                        .col-md-3
                            select#lotteryList.form-control.custom-select.mr-sm-2(onchange='getval(this);')
                                    option(value='') --select--
                                    option(value='lotto 6/49' groupcount='4') lotto 6/49
                                    option(value='lotto max' groupcount='4') lotto max
                                    option(value='sick kids lottery' groupcount='4') sick kids lottery
                                    option(value='Heart and Stroke foundation' groupcount='4') Heart and Stroke foundation
                                    option(value='Canadian Cancer Society' groupcount='4') Canadian Cancer Society
                                    option(value='Princess Margaret' groupcount='4') Princess Margaret
                                    option(value='Rotary' groupcount='4') Rotary
                                    option(value='Ultimate Dream Home Lottery' groupcount='4') Ultimate Dream Home Lottery
                        .col-md-3
                            select#groupList.form-control.custom-select.mr-sm-2.hidden
                                
                        .col-md-3
                            button(class="btn btn-primary w-100" onclick="submit()") Submit

                        .col-md-3
                            button#emailbtn.btn.btn-primary.w-100(type='button' data-toggle='modal' data-target='#exampleModal' disabled = "disabled") Email

                        
                    div#groupSelection.row
                            table#userListd

                    #exampleModal.modal.fade(tabindex='-1' role='dialog' aria-labelledby='exampleModalLabel' aria-hidden='true')
                        .modal-dialog(role='document')
                            .modal-content
                            .modal-header
                                h5#exampleModalLabel.modal-title Ticket
                                button.close(type='button' data-dismiss='modal' aria-label='Close')
                                  span(aria-hidden='true') &times;
                            .modal-body
                            form.form-section(action='email/send-email' method='POST' enctype="multipart/form-data")
                         
                                input#lotteryid.form-control.hidden(type='text',name="lotteryID" ) 
                                input#groupid.form-control.hidden(type='text',name="groupId" ) 
                                input#lotteryName.form-control.hidden(type='text',name="lotteryName" ) 
                                label Message
                                input#message.form-control(type='text',name="message" ) 
                                input#groupName.form-control.hidden(type='text',name="groupName" ) 
                                label(for='Ticket') Attach Ticket Image (Optional)
                                input#imageFile.form-control(type='file',placeholder="Upload ticket image to send with email",name="imageFile", accept="image/*", onchange="return fileValidation()")
                                button#sendEmailBtn.btn.btn-primary(type='submit')
                                    span#sendBtnText Send
                                    span#sendBtnSpinner.spinner-border.spinner-border-sm(role='status' aria-hidden='true' style='display: none; margin-left: 8px;')
block script
           script(src='/js/user-list.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/dataTables.buttons.min.js')
           script(src='https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/buttons.html5.min.js')
           script.
               // Handle email form submission with loading state
               document.addEventListener('DOMContentLoaded', function() {
                   const emailForm = document.querySelector('form[action="email/send-email"]');
                   const sendBtn = document.getElementById('sendEmailBtn');
                   const sendBtnText = document.getElementById('sendBtnText');
                   const sendBtnSpinner = document.getElementById('sendBtnSpinner');

                   if (emailForm && sendBtn) {
                       emailForm.addEventListener('submit', function(e) {
                           // Show loading state
                           sendBtn.disabled = true;
                           sendBtnText.textContent = 'Sending...';
                           sendBtnSpinner.style.display = 'inline-block';
                       });
                   }

                   // Auto-hide alerts after 10 seconds
                   const alerts = document.querySelectorAll('.alert');
                   alerts.forEach(function(alert) {
                       setTimeout(function() {
                           if (alert && alert.parentNode) {
                               alert.style.transition = 'opacity 0.5s';
                               alert.style.opacity = '0';
                               setTimeout(function() {
                                   if (alert.parentNode) {
                                       alert.parentNode.removeChild(alert);
                                   }
                               }, 500);
                           }
                       }, 10000);
                   });
               });
        
     
           


