extends admin-layout

block content   
  section#contents.container    

    .add-section.mt-4  
      h2.form-header Send Newsletter to Selected Customers
      
      if error
        .alert.alert-danger #{error}
      
      if success
        .alert.alert-success #{success}
      
      form.form-section(action='/admin/newsletter-broadcast/send' method='POST')
        .row
          .col-md-12.mb-3
            label(for='subject') Subject
            input#subject.form-control(type='text', name='subject', value=subject || '', placeholder='Enter newsletter subject', required)
          
          .col-md-12.mb-3
            label(for='message') Message
            textarea#tinymce-editor.form-control(name='message', rows='10', placeholder='Enter your message here...', required) #{message || ''}
            p.text-muted.mt-2 Write your announcement here (e.g., "The lottery will begin in 3 months")
          
          .col-md-12.text-center.mt-4.mb-3
            button.btn.btn-primary.btn-lg(type='submit') 
              i.fa.fa-paper-plane.mr-2
              | Send Newsletter to All Subscribers

block script
  script.
    $(function() {
      // Initialize TinyMCE Editor
      tinymce.init({
        selector: '#tinymce-editor',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        menubar: false,
        height: 300,
        promotion: false,
        branding: false,
        skin: 'oxide',
        content_css: 'default',
        setup: function(editor) {
          editor.on('change', function() {
            editor.save();
          });
        }
      });
    });
