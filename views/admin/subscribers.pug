extends admin-layout

block content
  style.
    .subscriber-container {
      margin-left: 290px;
      padding: 20px;
      background-color: #2a2b3d;
      min-height: 100vh;
      color: #ccc;
    }
    .subscriber-list {
      margin-top: 20px;
    }
    .subscriber-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #3a3b4d;
      background-color: #252636;
      margin-bottom: 5px;
      border-radius: 3px;
    }
    .subscriber-email {
      word-break: break-all;
      padding-right: 15px;
      font-size: 14px;
      flex-grow: 1;
    }
    .btn-custom {
      min-width: 70px;
      margin-left: 5px;
    }
    .header-actions {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .genuine-email {
      background-color: rgba(92, 184, 92, 0.2);
    }
    .test-email {
      background-color: rgba(217, 83, 79, 0.2);
    }
    .email-type {
      margin-right: 10px;
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 3px;
    }
    .genuine-tag {
      background-color: #5cb85c;
      color: white;
    }
    .test-tag {
      background-color: #d9534f;
      color: white;
    }
    .confirmed-tag {
      background-color: #5cb85c;
      color: white;
    }
    .pending-tag {
      background-color: #f0ad4e;
      color: white;
    }
  
  .subscriber-container
    .header-actions
      h3 Subscribers (#{subscribers ? subscribers.length : 0})
      .actions
        if subscribers && subscribers.length > 0
          button.btn.btn-success.btn-sm.export-csv(type="button" style="margin-right: 10px;")
            i.fa.fa-file-excel-o
            |  Export
          a.btn.btn-warning.btn-sm(href="/admin/subscribers/cleanup" style="margin-right: 10px;" onclick="return confirm('This will keep only genuine emails and delete test/dummy ones. Continue?')")
            i.fa.fa-broom
            |  Clean Up
          a.btn.btn-danger.btn-sm(href="/admin/subscribers/delete-all" style="margin-right: 10px;" onclick="return confirm('⚠️ WARNING: This will DELETE ALL subscribers permanently! Are you absolutely sure?')")
            i.fa.fa-trash
            |  Delete All
        a.btn.btn-primary.btn-sm(href="/admin")
          i.fa.fa-arrow-left
          |  Back
    
    .subscriber-list
      if subscribers && subscribers.length > 0
        each subscriber in subscribers
          - var isGenuine = subscriber.email.includes('@gmail.com') || subscriber.email.includes('@yahoo.com') || subscriber.email.includes('@hotmail.com') || subscriber.email.includes('@outlook.com') || subscriber.email.includes('@icloud.com')
          - var isConfirmed = subscriber.is_confirmed == 1
          - var itemClass = isConfirmed ? (isGenuine ? 'subscriber-item genuine-email' : 'subscriber-item') : 'subscriber-item test-email'
          div(class=itemClass)
            .subscriber-email
              span(class=isConfirmed ? 'email-type confirmed-tag' : 'email-type pending-tag')= isConfirmed ? 'Confirmed' : 'Pending'
              span(class=isGenuine ? 'email-type genuine-tag' : 'email-type test-tag' style="margin-left: 5px;")= isGenuine ? 'Genuine' : 'Suspicious'
              | #{subscriber.email}
            .actions
              a.btn.btn-danger.btn-sm.btn-custom(href=`/admin/subscribers/delete/${subscriber.id}` onclick="return confirm('Are you sure you want to delete this subscriber?')")
                i.fa.fa-trash
                |  Delete
      else
        p No subscribers found.
    
    // Hidden table for CSV export
    table#export-table(style="display: none;")
      thead
        tr
          th #
          th Email
          th Subscribed Date
          th Type
      tbody
        if subscribers && subscribers.length > 0
          each subscriber, index in subscribers
            - var isGenuine = subscriber.email.includes('@gmail.com') || subscriber.email.includes('@yahoo.com') || subscriber.email.includes('@hotmail.com') || subscriber.email.includes('@outlook.com') || subscriber.email.includes('@icloud.com')
            tr
              td= index + 1
              td= subscriber.email
              td= new Date(subscriber.created_at).toLocaleDateString()
              td= isGenuine ? 'Genuine' : 'Test'

block script
  script.
    $(document).ready(function() {
      $('.export-csv').on('click', function() {
        // Get the table data
        var csv = [];
        var rows = document.querySelectorAll("#export-table tr");
        
        for (var i = 0; i < rows.length; i++) {
          var row = [], cols = rows[i].querySelectorAll("td, th");
          
          for (var j = 0; j < cols.length; j++) {
            // Clean the text content and wrap with quotes if it contains commas
            var data = cols[j].textContent.replace(/\r?\n/g, '').trim();
            if (data.includes(',')) {
              data = '"' + data + '"';
            }
            row.push(data);
          }
          csv.push(row.join(","));
        }
        
        // Download CSV file
        downloadCSV(csv.join("\n"), 'subscribers.csv');
      });
      
      function downloadCSV(csv, filename) {
        var csvFile;
        var downloadLink;
        
        // Create CSV file
        csvFile = new Blob([csv], {type: "text/csv"});
        
        // Create download link
        downloadLink = document.createElement("a");
        
        // File name
        downloadLink.download = filename;
        
        // Create link to file
        downloadLink.href = window.URL.createObjectURL(csvFile);
        
        // Hide download link
        downloadLink.style.display = "none";
        
        // Add link to DOM
        document.body.appendChild(downloadLink);
        
        // Click download link
        downloadLink.click();
        
        // Remove link from DOM
        document.body.removeChild(downloadLink);
      }
    });

        // Hidden table for CSV export
        table#export-table.d-none
          thead
            tr
              th #
              th Email
              th Subscribed Date
          tbody
            if subscribers && subscribers.length > 0
              each subscriber, index in subscribers
                tr
                  td= index + 1
                  td= subscriber.email
                  td= new Date(subscriber.created_at).toLocaleString()

block script
  script.
    $(document).ready(function() {
      // Handle export to CSV
      $('.export-csv').on('click', function() {
        // Convert hidden table data to CSV
        let csv = [];
        let rows = document.querySelectorAll('#export-table tr');
        
        for (let i = 0; i < rows.length; i++) {
          let row = [], cols = rows[i].querySelectorAll('td, th');
          
          for (let j = 0; j < cols.length; j++) {
            // Replace any commas in the cell text
            let text = cols[j].innerText.replace(/,/g, ' ');
            row.push(text);
          }
          
          csv.push(row.join(','));
        }
        
        // Download CSV file
        let csvContent = csv.join('\n');
        let blob = new Blob([csvContent], { type: 'text/csv' });
        let link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = 'subscribers_' + new Date().toISOString().slice(0, 10) + '.csv';
        link.click();
      });
    });
