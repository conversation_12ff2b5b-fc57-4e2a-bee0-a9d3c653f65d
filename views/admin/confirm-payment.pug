extends admin-layout

block content
         style
            | .card { border: none; border-radius: 10px; }
            | .table-responsive { border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            | #paymentList { font-size: 14px; }
            | #paymentList thead th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 8px; font-weight: 600; text-align: center; }
            | #paymentList tbody td { padding: 10px 8px; vertical-align: middle; border-bottom: 1px solid #e9ecef; }
            | #paymentList tbody tr:hover { background-color: #f8f9fa; }
            | .btn-group-vertical .btn { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
            | .badge { font-size: 11px; padding: 4px 8px; }
            | .dataTables_wrapper .dataTables_filter input { border-radius: 20px; border: 1px solid #ddd; padding: 5px 15px; }
         section#contents.container-fluid
                .add-section
                    h2.form-header Confirm Payment
                    .row
                        .col-12
                            .card.shadow-sm
                                .card-body
                                    .table-responsive
                                        table#paymentList.table.table-striped.table-bordered.table-hover
                                            thead.thead-dark
                div
                     #exampleModalCenter.modal.fade(tabindex='-1' role='dialog' aria-labelledby='exampleModalCenterTitle' aria-hidden='true')
                       .modal-dialog.modal-dialog-centered.modal-lg(role='document')
                         .modal-content
                            .modal-header
                                   h1#LotterSection.modal-title Confirm Payment
                                   button.close(type='button' data-dismiss='modal' aria-label='Close')
                                          span(aria-hidden='true') &times;
                            .modal-body
                                   .row
                                     form.form-section(onSubmit="updatePaymentInfo(event)")
                                          .row.mb-3
                                                 .col-md-3
                                                        label(for='inputPaymentId', style="font-weight: 600; color: #495057;") 🆔 Payment ID
                                                        input#inputPaymentId.form-control(type='text',placeholder='Enter Payment ID',name="payment_id",disabled,required, style="background-color: #f8f9fa; border: 2px solid #e9ecef; font-weight: 500;")
                                                 .col-md-3
                                                        label(for='inputPaymentAmount', style="font-weight: 600; color: #495057;") 💰 Payment Amount
                                                        .input-group
                                                               .input-group-prepend
                                                                      span.input-group-text(style="background-color: #28a745; color: white; border: none;") $
                                                               input#inputPaymentAmount.form-control(type='number',placeholder='Enter Payment Amount',name="payment_amount", disabled, required, style="background-color: #f8f9fa; border: 2px solid #e9ecef; font-weight: 500;")
                                                 .col-md-3
                                                        label(for='inputDateReceived', style="font-weight: 600; color: #495057;") 📅 Date Started
                                                        input#inputDateReceived.form-control(type='date',name="date_added", disabled, required, style="background-color: #f8f9fa; border: 2px solid #e9ecef; font-weight: 500;")
                                                 .col-md-3
                                                        label(for='inputPaymentEmailId', style="font-weight: 600; color: #495057;") 📧 Payment Email ID
                                                        input#inputPaymentEmailId.form-control(type='email',placeholder="Enter payer's email",name="payment_email_id" , disabled, required, style="background-color: #f8f9fa; border: 2px solid #e9ecef; font-weight: 500;")
                                          .row.mb-4
                                                 .col-md-3
                                                        label(for="inputMobile", style="font-weight: 600; color: #495057;") 📱 Phone Number
                                                        input#inputMobile.form-control(type='tel',placeholder='Enter Mobile Number',name="payment_phone_number", disabled,required, style="background-color: #f8f9fa; border: 2px solid #e9ecef; font-weight: 500;")
                                          .row
                                                 .col-md-12
                                                        .row
                                                               h5(style="color: #495057; font-weight: 600; margin-bottom: 15px; padding: 10px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; border-left: 4px solid #007bff;") 🎫 Associated Tickets
                                                        .row
                                                               table#ticketlist.table.table-bordered.table-striped(style="background-color: white; color: black; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);")
                                                                      thead(style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;")
                                                                             tr
                                                                                    th(style="color: white; font-weight: bold; padding: 15px; text-align: center; border: none;") 🎲 Lottery Name
                                                                                    th(style="color: white; font-weight: bold; padding: 15px; text-align: center; border: none;") 👤 Player Name
                                                                                    th(style="color: white; font-weight: bold; padding: 15px; text-align: center; border: none;") 🎂 Age
                                                                                    th(style="color: white; font-weight: bold; padding: 15px; text-align: center; border: none;") 🎯 Numbers/Quick Pick
                                                                      tbody#ticket-table(style="background-color: white; color: black;")
                                                 
                                          input#payment-id(type="hidden",name="id")
                                          input#tickets(type="hidden",name="tickets")
                                          .row.mt-4
                                                 .col-md-12.text-center
                                                        button.btn.btn-success.btn-lg(type="submit", style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 12px 40px; border-radius: 25px; font-weight: 600; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); transition: all 0.3s ease;")
                                                               i.fas.fa-check-circle(style="margin-right: 8px;")
                                                               | Confirm Payment & Send Email


block script
           script(src='/js/confirm-payment.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/dataTables.buttons.min.js')
           script(src='https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/buttons.html5.min.js')