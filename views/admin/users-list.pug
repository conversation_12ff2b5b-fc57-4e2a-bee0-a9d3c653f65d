extends admin-layout

block content
         // Add DataTables CSS
         link(rel='stylesheet' href='https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css')
         link(rel='stylesheet' href='https://cdn.datatables.net/buttons/1.6.1/css/buttons.bootstrap4.min.css')
         style
            | .card { border: none; border-radius: 10px; }
            | .table-responsive { border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            | #userList { font-size: 14px; }
            | #userList thead th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 8px; font-weight: 600; text-align: center; }
            | #userList tbody td { padding: 10px 8px; vertical-align: middle; border-bottom: 1px solid #e9ecef; }
            | #userList tbody tr:hover { background-color: #f8f9fa; }
            | .badge { font-size: 11px; padding: 4px 8px; }
            | .dataTables_wrapper .dataTables_filter input { border-radius: 20px; border: 1px solid #ddd; padding: 5px 15px; }
            | .filter-section { background: #f8f9fa; border-radius: 10px; padding: 20px; margin-bottom: 20px; }
         section#contents.container-fluid
                .add-section
                    h2.form-header Paid Customer List
                    .filter-section
                        .row#user-section
                            .col-md-4
                                label.font-weight-bold(for='lotteryList') Select Lottery:
                                select#lotteryList.form-control.custom-select.mr-sm-2(onchange='getval(this);')
                                        option(value='') --Select Lottery--
                                        option(value='lotto 6/49' groupcount='4') lotto 6/49
                                        option(value='lotto max' groupcount='4') lotto max
                                        option(value='sick kids lottery' groupcount='4') sick kids lottery
                                        option(value='Heart and Stroke foundation' groupcount='4') Heart and Stroke foundation
                                        option(value='Canadian Cancer Society' groupcount='4') Canadian Cancer Society
                                        option(value='Princess Margaret' groupcount='4') Princess Margaret
                                        option(value='Rotary' groupcount='4') Rotary
                                        option(value='Ultimate Dream Home Lottery' groupcount='4') Ultimate Dream Home Lottery
                            .col-md-4
                                label.font-weight-bold(for='groupList') Select Group:
                                select#groupList.form-control.custom-select.mr-sm-2.hidden
                                    option(value='') --Select Group--

                            .col-md-4.d-flex.align-items-end
                                button.btn.btn-primary.btn-lg.w-100(onclick="submit()")
                                    i.fas.fa-search.mr-2
                                    | Search Customers
                    .row
                        .col-12
                            .card.shadow-sm
                                .card-body
                                    .table-responsive
                                        div#groupSelection
                                            table#userList.table.table-striped.table-bordered.table-hover
                                                thead.thead-dark

block script
           // Add required dependencies
           script(src='https://code.jquery.com/jquery-3.6.0.min.js')
           script(src='https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js')
           script(src='https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/dataTables.buttons.min.js')
           script(src='https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js')
           script(src='https://cdn.datatables.net/buttons/1.6.1/js/buttons.html5.min.js')

           script(src='/js/user-list.js')
        
     
           


