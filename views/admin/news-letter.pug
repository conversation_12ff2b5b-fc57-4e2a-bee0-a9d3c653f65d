extends admin-layout

block content   
         section#contents.container    
                .add-section  
                    h2.form-header Send Email
                    form.form-section(method='post' )
                        .row
                            .col-12.h-100
                                label(for='inputLotteryName') TextMessage
                                textarea#tinymce-editor(name="send")
                            .col-12.h-100.hidden
                                label(for='inputLotteryName') TextMessage
                                input#sendData(type='text' name="senddata")
                            .col-3 
                                button.btn.btn-primary.mt-3(type='submit') save


block script
    
        script.
            $(function() {
                tinymce.init({
                    selector: '#tinymce-editor',
                    plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
                    toolbar: 'undo redo | blocks | bold italic underline | numlist bullist | link image | save',
                    menubar: false,
                    height: 300,
                    promotion: false,
                    branding: false,
                    skin: 'oxide',
                    content_css: 'default',
                    setup: function(editor) {
                        // Add a custom save button
                        editor.ui.registry.addButton('save', {
                            icon: 'save',
                            tooltip: 'Save and Send Email',
                            onAction: function () {
                                const data = editor.getContent();
                                $('#sendData').val(data);
                                // You can optionally submit the form here
                                // editor.formElement.submit();
                            }
                        });
                        
                        editor.on('change', function() {
                            editor.save();
                        });
                    }
                })
            });
 

