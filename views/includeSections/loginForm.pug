.card
    .card-header.text-center
        | User Login
    .card-body.bodyBgImage
        form#loginForm(method='post' onkeydown="preventEnter(event)")
            .form-group
                label(for='email') Email address
                input#email.form-control(type='email', name='email', placeholder='Email')
            .form-group
                label(for='password') Password
                input#password.form-control(type='password', name='password', placeholder='Password')
            .form-group
                label(for='relative') Relative
                select#relative.form-control(name='relative')
                    option(value='0') --Select--
                    option(value='1') Self
                    option(value='2') Spouse
                    option(value='3') Father
                    option(value='4') Mother
                    option(value='5') Brother
                    option(value='6') Sister
                label(for='privacyAndCookies') By clicking to Register you agree to our
                a(href='/terms-conditions')  Terms-Conditions,
                label(for='andFor') and
                a(href='/privacy-policy')  Privacy Policy
                label(for='dotWord') .
            button.btn.btn-lg.btn-primary.btn-block(type='submit') Log In

        script.
            function preventEnter(e) {
                if (e.key === "Enter") {
                    e.preventDefault();
                }
            }
            document.getElementById('loginForm').addEventListener('submit', async function(event) {
                event.preventDefault();
                debugger
                const form = event.target;
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());

                try {
                    const response = await fetch(form.action, {
                        method: form.method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();
                    if (result.status === 200) {
                        window.location.href = `/verification?token=${result.data}`;
                    } else {
                        alert(result.msg);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                }
            });