.card
    .card-header.text-center
        | User Registration
    .card-body.bodyBgImage
        form(method='post')
            .form-group
                label(for='exampleInputEmail1') Email address
                input#exampleInputEmail1.form-control(type='email' , name='email', aria-describedby='emailHelp', placeholder='Enter email')
                small#emailHelp.form-text.text-muted We'll never share your email with anyone else.
            .form-group
                label(for='firstName') First Name
                input#firstName.form-control(type='text', name='firstName', placeholder='First Name')
            .form-groupRegister
            
                label(for='lastName') Last Name
                input#lastName.form-control(type='text', name='lastName' , placeholder='Last Name')
            .form-group
                label(for='password') Password
                input#password.form-control(type='text', name='password', placeholder='Password')
            .form-group
                label(for='profession') Profession
                input#profession.form-control(type='text', name='profession', aria-describedby='profession', placeholder='Enter Profession')
            .form-group
                label(for='telephone') Telephone
                input#telephone.form-control(type='text', name='telephone', placeholder='Telephone')
            .form-group
                label(for='address') Address
                input#address.form-control(type='text', name='address', placeholder='Address')
            .form-group
                label(for='city') City/Town
                input#city.form-control(type='text', name='city', placeholder='City')
            .form-group
                label(for='province') Province 
                select#province.form-control(name='province')
                        option(value='AB' style="font-weight: bold") Alberta
                        option(value='BC' style="font-weight: bold") British Columbia
                        option(value='MB' style="font-weight: bold") Manitoba
                        option(value='NB' style="font-weight: bold") New Brunswick
                        option(value='NL' style="font-weight: bold") Newfoundland and Labrador
                        option(value='NS' style="font-weight: bold") Nova Scotia
                        option(value='ON' style="font-weight: bold") Ontario
                        option(value='PE' style="font-weight: bold") Prince Edward Island
                        option(value='QC' style="font-weight: bold") Quebec
                        option(value='SK' style="font-weight: bold") Saskatchewan
                        option(value='NT' style="font-weight: bold") Northwest Territories
                        option(value='NU' style="font-weight: bold") Nunavut
                        option(value='YT' style="font-weight: bold") Yukon
            .form-group
                label(for='postal') Postal Code
                input#postal.form-control(type='text', name='postal' , placeholder='Postal Code')
            .from-group
                label(for="privacyAndCookies") By clicking to Register you agree to our
                a(href='/terms-conditions')  Terms-Conditions, 
                label(for='andFor') and 
                a(href='/privacy-policy')  Privacy Policy
                label(for='dotWord') .
            
                
            button.btn.btn-lg.btn-primary.btn-block(type='submit') Register