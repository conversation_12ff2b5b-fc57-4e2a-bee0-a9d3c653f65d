
.card.bodyBgImage
    .card-header.text-center
        | Select Lottery type
    .card-body

    //p !{lotteries}
    //p !{lotteries.local}
    each val, key in lotteries
        //p !{key} : !{val.local} : !{val.desc}
        - var nameVal = val.name
        - var imageVal = val.image
        - var priceVal = val.price
        - var onchangeVal = 'handleChange(this,' + priceVal + ')'
        - var imagepathVal = '../assets/images/logo/' + imageVal;

        .row.border.m-2.rounded
            .col-sm-2.border-right.p-3
                input.m-2#exampleCheck1.form-check-input(type="checkbox" name=nameVal onchange=onchangeVal)
            .col-sm-3.border-right.p-3
                img.w-100(src=imagepathVal)
            .col-sm-7.text-center.p-3
                p #{nameVal}            

                small.form-text.text-muted $!{priceVal}
                small.form-text.text-muted Encore included
                input#quickpick(type='radio' name='quickpick' onclick="javascript:quickpickyesnoCheck()")
                |  Quick Pick  
                h3#quickpicknumbers
                input#choosenumbers(type='radio' onclick='javascript:choosenumbersyesnoCheck();' name='choosenumbers')
                |  Choose Numbers  
                br
                #ifYes(style='visibility:hidden') 
                    input#yes(type='text' name='ln1' class='qp' maxlength='2') 
                    input#yes(type='text' name='ln2' class='qp' maxlength='2')
                    input#yes(type='text' name='ln3' class='qp' maxlength='2')
                    input#yes(type='text' name='ln4' class='qp' maxlength='2')
                    input#yes(type='text' name='ln5' class='qp' maxlength='2')
                    input#yes(type='text' name='ln6' class='qp' maxlength='2')            
            .col#groupMax
