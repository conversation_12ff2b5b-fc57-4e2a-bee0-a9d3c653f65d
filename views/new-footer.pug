// ======= Footer =======
footer#footer
  //- .footer-top
    //- .container
      .row
        .col-lg-2.col-md-6.footer-contact
          h4 CALL
          p
            | +1-800-234-2345
            br

        .col-lg-3.col-md-6.footer-links
          h4 HELP
          ul
            li
              a(href='/faq') FAQ
            li
              a(href='/terms-conditions') Terms and Conditions
            li
              a(href='/privacy-policy') Privacy Policy
        .col-lg-2.col-md-6.footer-links
          h4 ABOUT
          ul
            li
              a(href='/about') About Us
            li
              a(href='/contact-us') Contact Us
        .col-lg-4.col-md-6.footer-links
          h4 Subscribe to our email
          ul
            li
              form(method='post' action='/subscribe')
                // Honeypot field - hidden from users, visible to bots
                input(type="text" name="website" style="display:none !important; visibility:hidden !important; position:absolute; left:-9999px;" tabindex="-1" autocomplete="off")
                input.subscribe(type="email" name="subEmail" placeholder="Enter your email" required)
                button.btn.subscribebtn(type="submit") Go
            p don't miss out a chance to play as a group
        .col-lg-12.col-md-12.footertext
            p Gambling is supposed to be fun. If it’s not,Connex Ontario can help. 1-866-531-2600	
  .container
    .copyright
      | &copy; Copyright 
      | Let&apos;s Play Group Lottery
      | . All Rights Reserved
// End Footer
a.back-to-top(href='#')
  i.fa.fa-chevron-up
// Vendor JS Files

script(src='/vendor/bootstrap/js/bootstrap.bundle.min.js')
script(src='/vendor/jquery.easing/jquery.easing.min.js')
script(src='/vendor/counterup/counterup.min.js')
script(src='/vendor/owl.carousel/owl.carousel.min.js')
// Template Main JS File
script(src='/js/main.js')
script(src='/js/index.js')
script(src='https://api.demo.convergepay.com/hosted-payments/Checkout.js')
script(type='text/javascript').
    for (var i = 0; i < document.links.length; i++) {
    if (document.links[i].href == document.URL) {
    document.links[i].className = 'active';
    }
    }
script.
  jQuery(document).ready(function() {
  var cookie = false;
  var cookieContent = $('.cookie-disclaimer');
  checkCookie();
  if (cookie === true) {
  cookieContent.hide();
  }
  function setCookie(cname, cvalue, exdays) {
  var d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  var expires = "expires=" + d.toGMTString();
  document.cookie = cname + "=" + cvalue + "; " + expires;
  }
  function getCookie(cname) {
  var name = cname + "=";
  var ca = document.cookie.split(';');
  for (var i = 0; i < ca.length; i++) {
  var c = ca[i].trim();
  if (c.indexOf(name) === 0) return c.substring(name.length, c.length);
  }
  return "";
  }
  function checkCookie() {
  var user = getCookie("username");
  if (user !== "") {
  cookie = true;
  } else {
  if (user !== "" && user !== null) {
  setCookie("cvalue", "cvalue", 365);
  }
  }
  }
  function deleteCookie() {
  setCookie('cname', '', -1);
  }
  //set cookie on click
  $('.accept-cookie').click(function () {
  setCookie("cname", "cvalue", 365);
  //- alert("cookie accepted!");
  cookieContent.hide();
  $('.blackbg').hide();
  });
  //delete cookie on click
  $('.decline-cookie').click(function () {
  //- alert("cookie declined!");
  cookieContent.hide();
  deleteCookie();
  });
  $('.ce-dismiss').click(function () {
  cookieContent.hide();
  //- $('.blackbg').hide();
  });
  });