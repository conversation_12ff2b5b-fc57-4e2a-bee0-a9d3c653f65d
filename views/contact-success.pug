extends layout

block content
  style.
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      background-color: #fff;
      border: none;
    }

    .success-icon {
      margin: 20px auto;
    }

    .success-icon img {
      width: 120px;
      height: 120px;
    }

    .contact-info {
      margin-top: 30px;
      color: #666;
    }

    .contact-info p {
      margin-bottom: 5px;
    }

    h2 {
      color: #333;
      font-weight: 600;
    }

    h4 {
      color: #444;
      font-weight: 500;
    }

    p {
      color: #555;
    }

    .card-body {
      padding: 40px 30px;
    }
  .container
    .row.justify-content-center
      .col-md-8.col-lg-6
        .card.mt-5.mb-5
          .card-body.text-center.py-5
            h2.mb-4 Contact Page
            
            .success-icon.mb-4
              img(src="/assets/images/success-check.svg", alt="Success", width="120")
            
            h4.mb-3 We received your email
            
            p.mb-3 We will get in touch shortly
            
            p.mb-4 Good luck! Do spread the word
            
            p.mb-2 Thanks one again,
            p.mb-4 
              em Letsplaygrouplotter Team
            
            .contact-info.mt-4
              p Email: <EMAIL>
              p Tel: **************
              
            .mt-4
              a.btn.btn-primary(href="/") Back to Home
