// ======= Intro Section =======
include ./header.pug
section#contactintro.clearfix
  .container(data-aos='fade-up')
    .intro-img(data-aos='zoom-out' data-aos-delay='200')
// End Intro Section
main#main
  // ======= Testimonials Section =======
  section#contact
    .container-fluid(data-aos='fade-up')
      h2.p-spacer Contact Us
      hr.clearfix.w-100
      #alert-container
      form#contact-form.p-spacer(method='post' action='/contact-us/submit' role='form')
        .messages
        .controls
          .row
            .col-lg-6
              .form-group
                //label(for='form_name') Firstname *
                input#form_name.form-control(type='text' name='name' placeholder=' Firstname *' required='required' data-error='Firstname is required.')
                .help-block.with-errors
            .col-lg-6
              .form-group
                //label(for='form_lastname') Lastname *
                input#form_lastname.form-control(type='text' name='surname' placeholder='Lastname *' required='required' data-error='Lastname is required.')
                .help-block.with-errors
          .row
            .col-lg-6
              .form-group
                //label(for='form_email') Email *
                input#form_email.form-control(type='email' name='email' placeholder='Email *' required='required' data-error='Valid email is required.')
                .help-block.with-errors
            .col-lg-6
              .form-group
                //label(for='form_need') Please specify your need *
                select#form_need.form-control(name='need' required='required' data-error='Please specify your need.')
                  option(value='')
                  option(value='Registration - Changes' style="font-weight: bold") Registration - Changes
                  option(value='Lucky Numbers' style="font-weight: bold") Lucky Numbers
                  option(value='Payment' style="font-weight: bold") Payment
                  option(value='Ticket Issues' style="font-weight: bold") Ticket Issues
                  option(value='Suggestions' style="font-weight: bold") Suggestions
                  option(value='Other' style="font-weight: bold") Other
                .help-block.with-errors
          .row
            .col-lg-12
              .form-group
                //label(for='form_message') Message *
                textarea#form_message.form-control(name='message' placeholder='Message *' rows='4' required='required' data-error='Please, leave us a message.')
                .help-block.with-errors
            .col-md-12.text-center
              input.btn.btn-success.btn-send(type='submit' value='Send message')
  // End Contact Section
include ./home-footer.pug
include ./new-footer
style. 
  .text-dark {
    color: #000!important;
  }
  #footer .footer-top h4, #footer .footer-top .footer-links ul a {
    color: #fff;
  }
  #footer.bg-themedarkblue {
    margin-top: 4.5rem!important;
    padding-bottom: 0px!important;
  }
  @media (max-width: 500px) {
    #contact {
      width: 90%;
    }
  }
// End #main

block scripts
  script(src='/js/contact-form.js')