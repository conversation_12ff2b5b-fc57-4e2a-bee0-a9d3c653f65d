doctype html
html
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    style.
      body {
        font-family: 'Segoe UI', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .header {
        background-color: #1a237e;
        color: white;
        text-align: center;
        padding: 30px 20px;
      }
      .header h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .content {
        padding: 30px;
        background: #ffffff;
      }
      .content p {
        margin: 0 0 15px;
        font-size: 16px;
      }
      .signature {
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        color: #666;
        font-style: italic;
      }
      .footer {
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
      }
      .footer p {
        margin: 0;
        font-size: 13px;
        color: #666;
      }
      @media only screen and (max-width: 600px) {
        .container {
          width: 100% !important;
          border-radius: 0;
        }
        .content {
          padding: 20px;
        }
      }
  body
    .container
      .header
        h1= subject
      .content
        | !{content}
        .signature
          p Best regards,
          p Let's Play Group Lottery Team
      .footer
        p This email was sent by Let's Play Group Lottery. If you no longer wish to receive these emails, you can unsubscribe by clicking the link in your account settings.
