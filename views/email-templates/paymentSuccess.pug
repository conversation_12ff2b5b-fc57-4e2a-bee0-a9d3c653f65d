- var tickets = [{"lottery_name":"lotto 649","lottery_id":"1","group_no":4,"quickpick":1,"custom_number":"Quick Pick","playerName":"yash1","person_in_group":1,"created_date":"2021-02-04T13:27:01.215Z","ticket_id":54},{"lottery_name":"lotto 649","lottery_id":"1","group_no":4,"quickpick":0,"custom_number":"1,2,3,4,5,6","playerName":"yash2","person_in_group":2,"created_date":"2021-02-04T13:27:01.275Z","ticket_id":55},{"lottery_name":"sick kids lottery","lottery_id":"3","group_no":1,"quickpick":1,"custom_number":"N/A","playerName":"yash3","person_in_group":6,"created_date":"2021-02-04T13:27:01.355Z","ticket_id":56}]

  div.p-5(style='background-color:#fff8ee;')
    div.jumbotron.container.mt-5.mb-1.bg-white.py-5
        h1.display-4.text-center.mb-3 Thank you for your order
        p Dear <strong>{{name}}</strong>, 
        p Thanks for participating. We've received your payment of CA&dollar; {{total}}.
        //- p Following are order details : 
        table.table.table-striped
            thead
             tr
              th(scope="col")   #
              th(scope="col")   Lottery Name
              th(scope="col")   Player Name
              th(scope="col")   Lucky Number
              th(scope="col")   Group
              th(scope="col")   Person
            tbody
                each val, index in tickets
                  tr
                    td(style="width:8%;")  #{index+1} 
                    td  #{val.lottery_name}
                    td  #{val.playerName}
                    td  #{val.quickpick === 1 ? "Quick Pick" : val.custom_number}
                    td(style="width:12%;") #{String.fromCharCode(64+val.group_no)}
                    td(style="width:12%;") #{val.person_in_group}
        p 2 weeks prior to the lottery start, the list of members/ lucky numbers and the group you belong too will be sent to you. <br> <strong>Good luck!</strong>  Do spread the word.
        //- p For any discrepancies, mail us at <a href="mailto:<EMAIL>" rel="nofollow" target="_blank"><EMAIL></a>.

        i Thanks once again, <br> Letsplaygrouplottery Team

link(rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous")