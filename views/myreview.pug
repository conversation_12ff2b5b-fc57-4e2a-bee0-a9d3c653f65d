doctype html
html(lang="en") 
 head
  meta(charset='utf-8')
  meta(http-equiv="X-UA-Compatible", content="IE=edge")
  meta(content='width=device-width, initial-scale=1.0' name='viewport')
  title Review - Lets play group lottery
  meta(content='' name='descriptison')
  meta(content='' name='keywords')
  // Favicons
  link(href='/assets/img/favicon.png' rel='icon')
  link(href='/assets/img/apple-touch-icon.png' rel='apple-touch-icon')
  // Google Fonts
  link(rel="preconnect" href="https://fonts.gstatic.com")
  link(href="https://fonts.googleapis.com/css2?family=Assistant:wght@500&family=Thasadith&display=swap" rel="stylesheet")
  
  link(href='https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Montserrat:300,400,500,700' rel='stylesheet')
  // Vendor CSS Files
  link(href='/vendor/bootstrap/css/bootstrap.min.css' rel='stylesheet')
  link(href='/vendor/font-awesome/css/font-awesome.min.css' rel='stylesheet')
  link(href='/vendor/owl.carousel/assets/owl.carousel.min.css' rel='stylesheet')
  link(href='/css/style.css' rel='stylesheet')
  script(src='/vendor/jquery/jquery.min.js')
  //- script(src="https://sandbox.web.squarecdn.com/v1/square.js")
  script(src="https://web.squarecdn.com/v1/square.js")
  script(src="/node_modules/sweetalert2/dist/sweetalert2.all.min.js")
 style.
  .swal2-title {
    margin: 0px !important;
  }
  .swal2-actions button{
    background-color : #17a2b8;
    outline:none;
  }
 body  
  // ======= Header =======
  header#header.fixed-top11
   .top-bar.d-flex
    .logo.float-left
      a.d-flex.ml-3(href='/' style="display:flex;margin-left:1rem;")
        img.img-fluid(src='/assets/img/LOGO_1.png' alt='Lets Play Lottery Group')
        h5.ml-3.my-auto(style="font-family: 'Thasadith';color:#fff;") Lets Play Group Lottery
   .bottom-bar.d-none.d-lg-flex
    .logo.d-flex.mr-auto
      a(href='#')
        i.fa.fa-instagram
      a(href='#')
        i.fa.fa-facebook-square
      a(href='#')
        i.fa.fa-twitter
      span.d-flex.my-auto 
        | I do not sell lottery tickets/Not a retailer just form a group play

    nav.main-nav.ml-auto.d-none.d-lg-flex
      ul 
        if isLogged
          li
            a(href='/logout')  Logout  
        else 
          li
            a(href='/login')  Login
          li
            a(href='/registration')  Register
            
        li
          a(href='/lotteries') Lotteries
        li
          a(href='/play') Let&apos;s Play
        li
          a(href='/about') About Us
        li.d-flex.d-lg-none 
          a(href='#')
            i.fa.fa-instagram
          a(href='#')
            i.fa.fa-facebook-square
          a(href='#')
            i.fa.fa-twitter
          
          
  // #header

  main#bg.container.mt-5.pt-5
    h1.m-5.text-center Please review your order
    h3.mb-5.text-center Once you place your order, you will receive an email to confirm your order
    form(action="/payment/success", method="post",id="paymentForm")
      div.row
        div.col-12.col-sm-4
            .row
             .col-12.mb-5
                h2.text-primary.pb-3.border-primary(style="border-bottom: 5px solid;") Your Information
                h2.pb-2.mb-1(style="border-bottom: 2px solid;") Personal details
                div.text-dark.mb-4.ml-5
                  div #{user.first_name+' '+user.last_name}
                  div #{user.email_id} 
                  div #{user.phone_number}

                h2.pb-2.mb-1(style="border-bottom: 2px solid;") 
                  | Mailing details
                  span.font_sixteen.ml-2(data-toggle="modal" data-target="#editaddressModal" style="cursor:pointer") Edit
                div.text-dark.ml-5
                  div#user_name #{user.first_name+' '+user.last_name}
                  div#user_email #{user.email_id} 
                  div#user_phone #{user.phone_number}
            

        div.col-12.col-sm-8
            h2.text-primary.pb-3.border-primary(style="border-bottom: 5px solid;") Order Summary
            div.row
                .col-12
                    h3 
                        | Review your Order
                        span.font_sixteen.ml-2(data-toggle="modal" data-target="#editOrderModal" style="cursor:pointer") Edit
                    
                    table.table.table-striped
                        thead
                         tr
                          th(scope="col")   #
                          th(scope="col")   Lottery Name
                          th(scope="col")   Player Name
                          th(scope="col")   Lucky Number
                          th(scope="col")   Price
                          
                        tbody#mainTable
                         each val, index in tickets
                          tr(class="index "+parseInt(index+1))
                           td(style="width:70px;")
                            input.form-control(type="text" name="index" value=index+1 disabled)
                           td
                            input.form-control(type="text" name="lotteryName" value=val.lotteryName disabled)
                           td
                            input.form-control(type="text" name="playerName" class="playerName "+parseInt(index+1) value=val.playerName disabled)
                           if val.chooseNo && (val.lotteryName == 'lotto 649' || val.lotteryName == 'lotto max')
                            td 
                             input.form-control(type="text" name="chooseNo" class="chooseNo "+parseInt(index+1) value=val.chooseNo disabled)
                           else if (val.lotteryName == 'lotto 649' || val.lotteryName == 'lotto max')
                            td 
                             input.form-control(type="text" name="chooseNo" value="Quick Pick" disabled) 
                           else
                            td 
                             input.form-control(type="text" name="chooseNo" value="N/A" disabled) 
                           td(style="width:12%;")
                            input.form-control(type="text" name="price" value=val.price disabled)

                      



                 
        div(class="modal fade" id="editOrderModal" tabindex="-1" aria-labelledby="editOrderModalLabel" aria-hidden="true")
         div(class="modal-dialog" style="max-width:750px;")
          div(class="modal-content")
           div(class="modal-header")
            h5(class="modal-title" id="editOrderModalLabel") Edit order details
            button(type="button" class="close" data-dismiss="modal" aria-label="Close")
             span(aria-hidden="true") &times;
           div(class="modal-body")
                table.table.table-striped
                        thead
                         tr
                          th(scope="col")   #
                          th(scope="col")   Lottery Name
                          th(scope="col")   Player Name
                          th(scope="col")   Lucky Number
                          th(scope="col")   Price
                          
                        tbody
                         each val, index in tickets
                          tr(class="index "+parseInt(index+1))
                           td(style="width:70px;")
                            input.form-control(type="text" name="index[]" value=index+1 readonly)
                           td
                            input.form-control(type="text" name="lotteryName[]" class="lotteryName " value=val.lotteryName readonly)
                            input(type="hidden", name="lotteryId[]" value=val.lotteryId)
                           td
                            input.form-control(type="text" name="playerName[]" value=val.playerName onchange="$('.playerName."+parseInt(index+1)+"').val($(this).val())")
                           if val.chooseNo && (val.lotteryName == 'lotto 649' || val.lotteryName == 'lotto max')
                            td 
                             input.form-control(type="text" name="chooseNo[]" value=val.chooseNo onchange="$('.chooseNo."+parseInt(index+1)+"').val($(this).val())")
                           else if (val.lotteryName == 'lotto 649' || val.lotteryName == 'lotto max')
                            td 
                             input.form-control(type="text" name="chooseNo[]" value="Quick Pick" readonly) 
                           else
                            td 
                             input.form-control(type="text" name="chooseNo[]" value="N/A" readonly)
                           td(style="width:12%;")
                            input.form-control.price(type="text" name="price[]" value=val.price readonly)
                           td
                            i.fa.fa-trash(onclick="$('.index."+parseInt(index+1)+"').remove();updatePrice();")
                        input.form-control(type="hidden" name="paymentReceipt" id="paymentReceipt" value="")
                        input.form-control(type="hidden" name="paymentId" id="paymentId" value="")

        div(class="modal fade" id="editaddressModal" tabindex="-1" aria-labelledby="editaddressModalLabel" aria-hidden="true")
         div(class="modal-dialog")
          div(class="modal-content")
           div(class="modal-header")
            h5(class="modal-title" id="editaddressModalLabel") Edit mailing details
            button(type="button" class="close" data-dismiss="modal" aria-label="Close")
             span(aria-hidden="true") &times;
           div(class="modal-body")
              div
                div.form-group
                  label(for="user_name") Name
                  input.form-control(type="text" name="user_name" value=user.first_name+' '+user.last_name )  
                div.form-group
                  label(for="user_email") Email
                  input.form-control(type="text" name="user_email" value=user.email_id) 
                  input.form-control(type="hidden" name="user_id" value=user.id) 
                div.form-group
                  label(for="user_phone") Phone
                  input.form-control(type="text" name="user_phone" value=user.phone_number)  

           div(class="modal-footer")
            button(type="button" class="btn btn-secondary" data-dismiss="modal") Close
            button(type="button" class="btn btn-primary m-0 w-auto" onclick="updateUserData()") Save changes


      // Payment Instructions Section
      .form-group.mt-4
        h3.text-primary Payment Instructions
        .alert.alert-info
          h5 📧 Interac e-Transfer Payment:
          p
            strong Send payment to:
            span.text-primary.h5 <EMAIL>
          p
            strong Amount: $#{pay_det.amount}
          p
            strong Include in message:
            span#paymentIdText Your Payment ID (will be provided after confirming order)
          p
            small ⏰ Your tickets will be confirmed within 1-2 business days after payment is received
          p
            small 📧 You will receive a confirmation email once payment is verified

      // Submit button for the form
  button.btn.btn-success.btn-lg.d-block.ml-auto.mr-auto.mt-5.mb-4.w-25(type="button" onclick="confirmOrder(event)") Confirm Order
      
  script.
   var a = '!{JSON.stringify(tickets)}';
  include ./new-footer.pug

  script.

   function updateUserData() {
    $('#user_name').text($('input[name="user_name"]').val());
    $('#user_email').text($('input[name="user_email"]').val());
    $('#user_phone').text($('input[name="user_phone"]').val());
    $('#editaddressModal').modal('hide');
   }
   
   $(document).ready(() => {
    let myC = `<tr><td></td><td></td><td colspan="2" class="text-right">Registration Fees</td><td id="regFees" class="text-center"></td></tr><tr style="border-top: 2px solid;"><td></td><td></td><td></td><td><b>Total</b></td><td id="total" class="text-center"></td></tr>`;
    $('#mainTable').append(myC);
    let ticketCount = '#{tickets.length}';
    let registrationFees = ticketCount * 4; // $4 per ticket
    $('#regFees').text(registrationFees);
    $('#total').text('#{pay_det.amount}');

    // No need for Square payments anymore - using email transfer only
    console.log('Email transfer payment system ready');
   });
   
    function confirmOrder(event) {
      console.log('confirmOrder function called');

      if (event) {
        event.preventDefault();
      }

      // Show loading message
      alert('Processing your order... Please wait.');

      // Generate unique payment ID
      const timestamp = Date.now();
      const paymentId = `LPG-${new Date().getFullYear()}-${String(timestamp).slice(-6)}`;

      console.log('Generated payment ID:', paymentId);

      // Prepare order data for email transfer
      const orderData = {
        user_id: '#{user.id}',
        user_email: '#{user.email_id}',
        lotteryId: [],
        lotteryName: [],
        playerName: [],
        chooseNo: [],
        price: ['#{pay_det.amount}'],
        payment_id: paymentId,
        payment_method: 'email_transfer'
      };

      // Collect lottery data from the form
      try {
        const tickets = !{JSON.stringify(tickets)};
        console.log('Tickets data:', tickets);

        tickets.forEach(ticket => {
          orderData.lotteryId.push(ticket.lotteryId);
          orderData.lotteryName.push(ticket.lotteryName);
          orderData.playerName.push(ticket.playerName);
          orderData.chooseNo.push(ticket.chooseNo || 'Quick Pick');
        });

        console.log('Order data prepared:', orderData);

        // Submit order for email transfer
        $.ajax({
          url: '/email-transfer/confirm',
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(orderData),
          success: function(response) {
            console.log('Server response:', response);
            if (response.success) {
              // Redirect to instructions page with payment details
              window.location.href = `/instructions?payment_id=${paymentId}&total=${orderData.price[0]}`;
            } else {
              alert('Error confirming order. Please try again.');
            }
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            console.error('Status:', status);
            console.error('Response:', xhr.responseText);
            alert('Error confirming order. Please try again.');
          }
        });
      } catch (err) {
        console.error('Error in confirmOrder:', err);
        alert('Error processing order. Please try again.');
      }

      return false;
    }

   function updatePrice() {
    let a = $('.price');
    let sum=0;
    for(let i=0;i<a.length;i++) {
      sum += parseInt(a[i].value);
    }
    let registrationFees = a.length * 4; // $4 per ticket
    sum += registrationFees;
    $('#regFees').text(registrationFees);
    $('#total').text(sum);
    $('#amount_input').val(sum);
   }


    