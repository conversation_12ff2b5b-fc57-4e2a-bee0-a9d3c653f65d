// ======= Intro Section =======
include ./header.pug
//- section#signupintro.clearfix
  .container(data-aos='fade-up')
    .intro-img(data-aos='zoom-out' data-aos-delay='200')

main(style="")
    //- .pt-5
    .px-sm-5.m-sm-5
        .card#bg.m-auto.p-5(style="max-width:650px;background-color:#fff8ee;")
            .card-body.text-center
                h2.mx-0.mb-5 Log In
                .form#loginForm
                    form(method='post' action='/login')
                        .form-row
                            .form-group.col-12.col-md-9.col-lg-6.mx-auto
                                //- .fa.fa-user
                                input#email.form-control.pdl1(type='email', name='email', placeholder='Email'  required='required' data-error='Email is required.')
                                .validate

                        .form-group.p-0.col-12.col-md-9.col-lg-6.mx-auto
                            //- .fa.fa-lock
                            input#password.form-control.pdl1(type='password' name='password' placeholder='Password'  required='required' data-error='Password is required.')
                            .validate
                            .text-center
                            p.font_201.mt-4.mb-3(style="font-size:16px;")
                                <a href="/changePassword"  class="myA">Forgot Password</a>
                            button.btn.btn-primary.bg-themeblue.btn-lg.mt-4(type='submit') Login
                            //- hr.bdrblue
                            p.font_201.mt-4.mb-3(style="font-size:16px;")
                                | Don't have an account ? <a href="#" onclick='myRegistration()' class="myA">Register Here</a>
                            p.font_201.m-0(style="font-size:16px;")
                                | By clicking to Login you agree to our
                                a.myA(href='/terms-conditions') Terms-Conditions
                                | ,
                                a.myA(href='/faq') FAQ
                                |  and
                                a.myA(href='/privacy-policy') Privacy Policy
                                | .


    // End Contact Section
include ./new-footer.pug
// End #main

style. 
    .form-control {
        border-color: #8b8b8b;
    }

    .myA {
        color: #60a6ce;
        text-decoration: underline;
    }


script.
    document.addEventListener('DOMContentLoaded', function() {
        const error = !{JSON.stringify(error_msg)};
        if (error) {
           Swal.fire({
           title: 'Error',
           text: error,
           icon: 'error',
           confirmButtonText: 'Ok',
           reverseButtons: true
           }).then((result) => {
           })
        }
      });
    function sendLogin() {
        let d = {
            email: $('#email').val(),
            password: $('#password').val()
        };
        // $.ajax({
        //     url: '/login',
        //     type: 'POST',
        //     contentType: 'application/json',
        //     data: JSON.stringify(d),
        //     success: (r) => {
        //         if (r.status == 200) {
        //             location = '/verification?token=' + r.data + '&type=login';
        //         } else if (r.status == 500) {
        //             alert(r.msg);
        //         }
        //     }
        // });
        $('#loginForm').on('submit', function (e) {
            e.preventDefault();

            //- $.ajax({
            //- url: "/registration",
            //- dataType:'json',
            //- data: JSON.stringify( $('#registerForm').serializeArray() );
            //- beforeSend: function(result){
            //-   $('#register_button').prop('disabled', true);
            //-   $("#div1").html("<span style='color:blue'>Please wait</span>");
            //- },
            //- success: function(result){
            //-   $('#register_button').prop('disabled', false);
            //-   $("#div1").html(result);
            //- }});
            e.target.submit();
        })
    }