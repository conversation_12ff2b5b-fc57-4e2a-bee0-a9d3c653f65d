// ======= Intro Section =======
include ./header.pug
section#signupintro.clearfix
  .container(data-aos='fade-up')
    .intro-img(data-aos='zoom-out' data-aos-delay='200')
// End Intro Section
main#main
  // ======= Testimonials Section =======
  section#contact
    .container-fluid(data-aos='fade-up')
      .row
        .col-lg-12
          .section-header
            h3 Sign In
          .form
            form(method='post')
              .form-row

              .form-group.p-0.col-lg-12
                input#newpass.form-control.pdl(type='password' name='newPassword' placeholder='New Password'  required='required' data-error='New Password is required.')
                .validate
              .form-group.p-0.col-lg-12
                input#cnewpass.form-control.pdl(type='password' name='confirmNewPassword' placeholder='Confirm New Password'  required='required' data-error='Confirm New Password is required.')
                .validate
              .text-center
                button.btn.btn-primary.lightblue(type='button' onclick="sendPass()") Change Password
                hr.bdrblue
                //- p.font_20
                 |Don't have a account? <a href="#" onclick='myRegistration()' class="font_sixteen">REGISTER HERE</a>
                p.font_20
                 | By clicking to Register you agree to our 
                 a.font_sixteen(href='/terms-conditions') Terms-Conditions
                 | , 
                 a.font_sixteen(href='/faq') FAQ
                 |  and 
                 a.font_sixteen(href='/privacy-policy') Privacy Policy
                 | .		

				 
  // End Contact Section
include ./new-footer.pug
// End #main

script.
    var d;
    function sendPass() {
     let url = location.search; 
      let split_url = url.split('?');
      let token = split_url[1].split("=");
        
         d = {
            password: $('#pass').val(),
            oldPassword: $('#newpass').val(),
            confirmOldPassword: $('#cnewpass').val(),
            token:token[1]
        }
        if(d.oldPassword == d.confirmOldPassword) {
            $.ajax({
                url: '/password-update',
                type: 'POST',
                data: d,
                success: (r) => {
                    alert(r.msg);
                    if(r.status == 200) {
                        location = '/login';
                    }
                }
            });
        } else {
            alert("New Password and Confirm new password don't match.");
        }
    }     