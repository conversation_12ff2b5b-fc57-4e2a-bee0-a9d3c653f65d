extends layout

block content
  main
    .container.mt-5
      .row.justify-content-center
        .col-md-6
          .card.bg-light
            .card-body
              h2.text-center.mb-4 Register
              if message
                .alert.alert-danger= message
              form(method='post' action='/registration')
                .form-group
                  input#firstName.form-control(type='text' name='firstName' placeholder='First Name' required)
                .form-group
                  input#lastName.form-control(type='text' name='lastName' placeholder='Last Name' required)
                .form-group
                  input#email.form-control(type='email' name='email' placeholder='Email address' required)
                .form-group
                  input#password.form-control(type='password' name='password' placeholder='Password' required)
                .form-group
                  input#confirm-password.form-control(type='password' name='cpassword' placeholder='Confirm Password' required)
                .form-group
                  .input-group
                    select#countryCode.form-control.w-25(name='countryCode')
                      option(value='1' selected) Canada (+1)
                    input#phone.form-control.w-75(type='text' name='phone' placeholder='Telephone' maxlength='13' required)
                .form-group
                  input#city.form-control(type='text' name='city' placeholder='City' required)
                .form-group
                  select#province.form-control(name='province')
                    option(value='AB') Alberta
                    option(value='BC') British Columbia
                    option(value='MB') Manitoba
                    option(value='NB') New Brunswick
                    option(value='NL') Newfoundland and Labrador
                    option(value='NS') Nova Scotia
                    option(value='ON') Ontario
                    option(value='PE') Prince Edward Island
                    option(value='QC') Quebec
                    option(value='SK') Saskatchewan
                    option(value='NT') Northwest Territories
                    option(value='NU') Nunavut
                    option(value='YT') Yukon
                .form-group
                  input#postal.form-control(type='text' name='postal' placeholder='Postal Code' required)
                .form-group.d-flex.align-items-center
                  input#under18.mr-2(type='checkbox' name='under18' required)
                  label.mb-0 Are you 18 year old or above?
                .form-group.text-center.mt-4
                  button#register_button.btn.btn-primary.btn-lg(type='submit') Register
                .text-center.mt-3
                  small.text-muted By clicking Register you agree to our 
                    a(href='/terms-conditions') Terms & Conditions
                    | , 
                    a(href='/faq') FAQ
                    |  and 
                    a(href='/privacy-policy') Privacy Policy
                    | .
                .text-center.mt-3
                  | Already have an account? 
                  a(href='/login') Login Here

              script.
                let searchParams = new URLSearchParams(window.location.search);
                if(searchParams.has('text')){
                  alert(searchParams.get('text'));
                }
