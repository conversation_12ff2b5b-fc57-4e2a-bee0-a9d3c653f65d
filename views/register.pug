include ./header.pug

block content
  .container
    h2.text-center.mb-4 Register
    .row.justify-content-center
      .col-md-8
        .bodyBgImage.p-4.rounded
          if message
            .alert.alert-danger= message
          form(method='post' action='/registration')
            .row
              .col-md-6.mb-3
                input#firstName.form-control(type='text' name='firstName' placeholder='First Name' required)
              .col-md-6.mb-3
                input#email.form-control(type='email' name='email' placeholder='Email address' required)
            .row
              .col-md-6.mb-3
                input#lastName.form-control(type='text' name='lastName' placeholder='Last Name' required)
              .col-md-6.mb-3
                input#password.form-control(type='password' name='password' placeholder='Password' required)
            .row
              .col-md-6.mb-3
                .input-group
                  select#countryCode.form-control(name='countryCode' style='width: 100px;')
                    option(value='1' selected) Canada (+
                  input#phone.form-control(type='text' name='phone' placeholder='Telephone' maxlength='13' required)
              .col-md-6.mb-3
                input#confirm-password.form-control(type='password' name='cpassword' placeholder='Confirm Password' required)
            .row
              .col-md-6.mb-3
                input#city.form-control(type='text' name='city' placeholder='City' required)
              .col-md-6.mb-3
                select#province.form-control(name='province')
                  option(value='AB') Alberta
                  option(value='BC') British Columbia
                  option(value='MB') Manitoba
                  option(value='NB') New Brunswick
                  option(value='NL') Newfoundland and Labrador
                  option(value='NS') Nova Scotia
                  option(value='ON') Ontario
                  option(value='PE') Prince Edward Island
                  option(value='QC') Quebec
                  option(value='SK') Saskatchewan
                  option(value='NT') Northwest Territories
                  option(value='NU') Nunavut
                  option(value='YT') Yukon
            .row
              .col-md-6.mb-3
                input#postal.form-control(type='text' name='postal' placeholder='Postal Code' required)
              .col-md-6.mb-3.d-flex.align-items-center
                input#under18.mr-2(type='checkbox' name='under18' required)
                label.mb-0 Are you 18 year old or above?
            .text-center.mt-3
              button#register_button.btn.btn-info.px-4.py-2(type='submit') Register
            .text-center.mt-3
              small.text-muted By clicking to Register you agree to our 
                a(href='/terms-conditions') Terms-Conditions
                | , 
                a(href='/faq') FAQ
                |  and 
                a(href='/privacy-policy') Privacy Policy
                | .
            .text-center.mt-2
              small Already have an account? 
                a(href='/login') Login Here

          script.
            let searchParams = new URLSearchParams(window.location.search);
            if(searchParams.has('text')){
              alert(searchParams.get('text'));
            }
