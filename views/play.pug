include ./header.pug
// ======= Intro Section =======
section#playintro2.clearfix
  .container(data-aos='fade-up')
    .intro-img(data-aos='zoom-out' data-aos-delay='200')
  //
    div class="col-lg-4 float-right" style="position:absolute;top:135px;right:0px;">
    <div class="col-lg-12 text-center">
    <img src="assets/img/graph2.png" alt="" class="img-fluid">
    <img src="assets/img/graphtext.png" alt="" class="img-fluid">
    </div>
    </div
// End Intro Section
main#main.container.mt-5
  // ======= Testimonials Section =======
  section#bg
    div.py-5.text-center.light-pinkbg
      p.m-0 Ready to change lives together?
      hr.border-dark.mx-3.mx-sm-auto(style="max-width:700px")
      p.m-0 Playing as a group increase our chances to -----WIN BIG
    div.py-5(style="background-color:#80b8bc;")
      h1.p-spacer Let's Play
      ul.text-dark 
        li 6/49 lottery  52 draws x $ 3 = $ 156 Encore 52 x $ 1 = $ 52  Total is $ 208 ---- Approximately 5.5 months
        li Lotto max lottery 52 draws x $ 5 = $ 260 Encore 52 x $ 1 = $ 52  Total is $ 312 ---- same as above
        li Winnings will be emailed to each member of that lottery and group by the captain.
        li You are only a member of a group you have paid for as some lottery. have more groups check your group.
        //- li The tickets range from 20$ to 50$ depending on the type of lottery.

    .py-5.bg-themeblue 
      h1 How to play / buy a ticket
      ul 
            li Enter the number of tickets and click on the white square on the top left hand corner to begin playing
            li Enter your name/luckynumbers/quick pick/payment and accept----its simple
            li Remember your only in the group you have paid for at the time of purchasing
            li We email you your group and list of players in your group
            li Please check your personnel information and ticket number any changes to be made let us know 2 weeks in advance
            li Quick pick is number generated by the computer of the organising lottery
            li Package is one per person that includes 1 main lottery, plus smallest pack of 50/50 plus smallest pack of calendar or any lottery
            //- li Enter the number of tickets and click on the white square  on the top left hand corner to begin playing
            //- li We will inform you of all winning by email
            //- li Quick pick is number generated by the computer of the organising lottery
            //- li Package is one per person that includes 1 main lottery, plus smallest pack of 50/50 plus smallest pack of calendar or any lottery

      //- ul.row.border-0.nav.nav-tabs#myTab(role="tablist")
        li.nav-item.col-12.col-sm
          a.nav-link.active#tab1(data-toggle="tab" href="#pane1" role="tab" aria-controls="pane1" aria-selected="true") How to buy a ticket
        //- li.nav-item.col-12.col-sm
        //-   a.nav-link#tab2(data-toggle="tab" href="#pane2" role="tab" aria-controls="pane2" aria-selected="true") Operation Highlights
        //- li.nav-item.col-12.col-sm
        //-   a.nav-link#tab3(data-toggle="tab" href="#pane3" role="tab" aria-controls="pane3" aria-selected="true") Terms and Conditions

      //- .tab-content#myTabContent.pt-5
        .tab-pane.fade.show.active#pane1(role="tabpanel" aria-labelledby="tab1")
          ul 
            li Enter the number of tickets and click on the white square  on the top left hand corner to begin playing
            li We will inform you of all winning by email
            li Quick pick is number generated by the computer of the organising lottery
            li Package is one per person that includes 1 main lottery, plus smallest pack of 50/50 plus smallest pack of calendar or any lottery
        .tab-pane.fade#pane2(role="tabpanel" aria-labelledby="tab2")
          ul
            li The captain leads the group and maintains the website. 
            li All tickets purchased in TRUST only  
            li Captain will collect funds to purchase the tickets and send a copy to each member 
            li With the list of members and lucky numbers 3 weeks before the lottery starts
            li All winnings and communication will be emailed by the captain to each group
            li By calling the 1800 number of the lottery you can get more information on winning.
            li Captain will divide th money to the winners who's name is in thewinning group only 
            li Suggestion is welcome
        .tab-pane.fade#pane3(role="tabpanel" aria-labelledby="tab3")
          ul
            li By accecpting these terms and conditions,
            li You also accept and agree to be bound by the terms and conditions and policies
            li (ncluding but not limited to the Privacy Policy and the Frequent Asked Questions)
            li As may be posted on the website from time to time
            li Please read the terms and condition FAQ and policy

    .py-5.bg-themedarkblue   
      h1 Buy a ticket    
      form#form1.col-md-12.float-left(method='post' action='/review')
        .row.p-spacer
          .col-lg-8.col-md-7.col-sm-12.p-spacer.order-1.order-sm-0
            .card.cardnew
              .card-header.text-center.cardheadernew.font-weight-normal.text-white
                | Select Lottery type
      
              .card-body
               //.row.m-2.rounded.h375.cardbodyleft
                //.col-sm-4.p-3

              //p !{lotteries}
              //p !{lotteries.local}
              each val, index in lotteries
               //p !{key} : !{val.local} : !{val.desc}
               - var nameVal = val.lottery_name
               - var imageVal = val.image
               - var priceVal = val.lottery_price
               - var lotteryIdVal = val.id
               - var personnoVal = val.person_per_group
               - var groupnoVal = val.no_of_groups
               - var onchangeVal = 'handleChange(this,' + priceVal + ',' + lotteryIdVal + ',' + index + ')'
               - var onchangeTval = ''
               - var imagepathVal = '../assets/images/logo/' + imageVal;
               - var timerid = 'timer' + lotteryIdVal;
               - var qpid = 'quickpick' + lotteryIdVal;
               - var cnid = 'choosenumbers' + lotteryIdVal;
               - var ifyesid = 'ifYes' + lotteryIdVal;		
               - var op1id = 'op1_' + lotteryIdVal + '_tn_' ;
               - var op2id = 'op2_' + lotteryIdVal + '_tn_' ;
               - var op3id = 'op3_' + lotteryIdVal + '_tn_' ;
               - var op4id = 'op4_' + lotteryIdVal + '_tn_' ;
               - var op5id = 'op5_' + lotteryIdVal + '_tn_' ;
               - var op6id = 'op6_' + lotteryIdVal + '_tn_' ;
               - var op7id = 'op7_' + lotteryIdVal + '_tn_' ;
               - var lotteryVal = nameVal + '_' + lotteryIdVal + '_' + priceVal
               - var box_color_dark = val.card_color_dark || '#f5f5f5'
               - var box_color_light = val.card_color_light || '#fff'

    
                 .row.text-dark.border.m-2.rounded.h375.cardbodyleft.position-relative(id="box"+lotteryIdVal style=`background-color:${box_color_dark}`)
                  .col-12 
                    .row 
                      .col-1.p-3
                        input.m-2.form-check-input(id="main_checkbox_"+lotteryIdVal type="checkbox" name="lotteryNames[]" data-lname=nameVal onchange=onchangeVal value=lotteryVal)
                      .col-6.col-sm-3.p-3
                        img.w-100.newpos(src=imagepathVal)
                      .col.col-sm-3.d-flex.order-lg-2.ml-auto
                        //- p.title #{nameVal}
                        //- if nameVal == "lotto 649" || nameVal == "lotto max"
                          small.form-text.pull-right.bold.font_21 Encore  included
                          small.form-text.pull-right.bluetext.clear Total Draw <span class="total"><input type="text" name="totaldraw" onchange="calcTotalPriceDraw(this);" class="form-control bdrradius w160"/></span>
                        span.bluetext.ml-auto.mr-2.flex-column.d-flex.my-auto
                          span(style="font-size: 2.5rem;font-weight: 600;line-height:3.8rem;") #{priceVal}$
                          span(style="font-weight: 900") Each ticket
                      .col-12.col-sm-5.order-lg-1
                        .w-100.h-100.p-4.text-white.text-center(style="background-color: rgba(0, 0, 0, 0.6);display: none;" id="pop3"+lotteryIdVal)
                          .m-auto Only <span class="text-danger bg-light px-1 font-weight-bold">2</span> tickets left in this group!!
                  .col-12.lottery-name.bluetext(style=`background-color:${box_color_light}`)
                    .row.mx-0.text-center
                      .col-12.col-sm   	
                        small.form-text.bluetext Lottery start date
                        small.form-text.margin_btm25 #{new Date(val.start_date).getDate()}/#{new Date(val.start_date).getMonth() + 1}/#{new Date(val.start_date).getFullYear()}
                      .col-12.col-sm
                         small.form-text.bluetext Last date to join*
                         small.form-text.margin_btm25 #{new Date(val.date_of_joining).getDate()}/#{new Date(val.date_of_joining).getMonth() + 1}/#{new Date(val.date_of_joining).getFullYear()}
                   
                      .col-12.col-sm 
                        small.form-text.bluetext Lottery end date
                        small.form-text.margin_btm25 #{new Date(val.end_date).getDate()}/#{new Date(val.end_date).getMonth() + 1}/#{new Date(val.end_date).getFullYear()}
                  .col-12.d-flex 
                    small.mx-auto.text-center.bluetext(style="font-size:2rem;font-weight:bold;" id="lotteryTimer"+lotteryIdVal) 0
                    script.
                     $(document).ready(() => {
                      //- var a = #{new Date(val.start_date).getDate()};
                      var countDownDate = #{new Date(val.date_of_joining).getTime()};
                      //- var countDownDate = #{new Date(val.date_of_joining).getTime()};
                      var x = setInterval(function() {
                        var now = new Date().getTime();
                        var distance = countDownDate - now;
                        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        var seconds = Math.floor((distance % (1000 * 60)) / 1000);
                        //- document.getElementById("lotteryTimer"+#{lotteryIdVal}).innerHTML = days + "d:" + hours + "h:"+ minutes + "m";
                        document.getElementById("lotteryTimer"+#{lotteryIdVal}).innerHTML = days + " : " + hours + " : "+ minutes + " : "+ seconds;

                        if (distance < 0) {
                          clearInterval(x);
                          document.getElementById("lotteryTimer"+#{lotteryIdVal}).innerHTML = "EXPIRED";
                          $("#box"+#{lotteryIdVal}).append('<div class="text-center text-danger width100 position-absolute h-100 w-100 d-flex" style="top:0;left:0;background-color: rgba(0, 0, 0, 0.6);z-index:19;border-radius: 12px;"><span class="m-auto bg-themedarkblue py-3 px-4" style="border-radius:8px;"><span style="font-size:36px;font-weight: 700;">LOTTERY EXPIRED <br> </span><span style="font-size:16px;color:white;">Signup to our newsletter and we will remind you of the next draw.</span></span></div>');

                        }
                      }, 1000);
                        
                     });
                     $(document).ready(() => {
                      $.ajax({
                        type: 'GET',
                        url: '/api/get-lottery-group-count/#{lotteryIdVal}',
                        success: (r) => {
                          
                          document.getElementById("GroupNo"+#{lotteryIdVal}).innerHTML = String.fromCharCode(64 + r.currentGroup);
                          document.getElementById("PersonNo"+#{lotteryIdVal}).innerHTML = r.personCount;
                          
                           if(r.personCount > 45) {
                            $("#pop3"+#{lotteryIdVal}).addClass('d-flex');
                            $("#pop3"+#{lotteryIdVal} + " .text-danger").text(50-r.personCount);
                            }
                        }
                      })
                     }); 
                  .col-12.lottery-name.bluetext.text-center(style=`background-color:${box_color_light}`) 
                    .row 
                      .col-6
                        small.form-text.grp-no Group
                          small.form-text.bluetext(id="GroupNo"+lotteryIdVal) 0
                      .col-6 
                        small.form-text.grp-no People in the group*
                          small.form-text.bluetext(id="PersonNo"+lotteryIdVal) 0
                  
                  
                  
                  div(class="pt-4 col-sm-7 offset-sm-3 text-center select_wrapper "+lotteryIdVal style="font-weight: bold")
                   | How Many tickets ?
                   select(class="ml-3 col-3 form-control d-inline" name="tickets_no"+lotteryIdVal required onchange='changeTicketNo(this, ' + lotteryIdVal + ')')
                    option(value="1" selected=true style="font-weight: bold")  1
                    option(value="2" style="font-weight: bold")  2
                    option(value="3" style="font-weight: bold")  3
                    option(value="4" style="font-weight: bold")  4
                    option(value="5" style="font-weight: bold")  5
                  - var tn = 1;
                  while tn < 6
                   div(class="col-sm-12 h287 "+lotteryIdVal + ' t_'+tn style={display: 'none'})
                    .col-sm-12.p-2.lottery-name
                     label(for="player_name_"  +lotteryIdVal+"_tn_"+tn++) Player Name : 
                     input(type="text", name="player_name_"+lotteryIdVal+"_tn_"+(tn-1), class="col-12 col-sm-5 d-inline form-control ml-sm-4 req")
                    // Database-driven number selection options (replaces hardcoded name checks)
                    if val.quickpick == 1 || val.custom_number == 1
                     // Show Quick Pick option if enabled in database
                     if val.quickpick == 1
                      .col-sm-10.p-2.lottery-name
                       input.req(type='radio' style="font-weight: bold" name='quickorchoose_'+lotteryIdVal+'_tn_'+(tn-1) data-name=(tn-1)+'_'+lotteryIdVal value="quickpick" data-value=qpid onclick="javascript:quickpickyesnoCheck(this)")
                       |  Quick Pick&nbsp;&nbsp;
                       // h3#quickpicknumbers

                     // Show Choose Numbers option if enabled in database
                     if val.custom_number == 1
                      .col-sm-12.p-2.lottery-name.bluebg.mlr9
                       input.req(type='radio' style="font-weight: bold" onclick='javascript:choosenumbersyesnoCheck(this);' name='quickorchoose_'+lotteryIdVal+'_tn_'+(tn-1) data-name=(tn-1)+'_'+lotteryIdVal value="chooseno" data-valuevalue=(tn-1)+cnid)
                       |  Choose Numbers
                       br
                       #ifYes(name=ifyesid style='display:none;visibility:hidden;background:#76d4f8;position: relative;left: -8px;padding: 0 2%;border-bottom-right-radius: 8px;border-bottom-left-radius: 8px;height: auto;width: calc(100% + 16px);')
                        input.qp.req1.fin(type='text' name=op1id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 0)")
                        input.qp.req1.fin(type='text' name=op2id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 1)")
                        input.qp.req1.fin(type='text' name=op3id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 2)")
                        input.qp.req1.fin(type='text' name=op4id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 3)")
                        input.qp.req1.fin(type='text' name=op5id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 4)")
                        input.qp.req1.fin(type='text' name=op6id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 5)" data-max="49")
                        // 7th number for Lotto Max (keep existing logic for special case)
                        if nameVal == "lotto max"
                         input.qp.req1.fin(type='text' name=op7id+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"', 6)" data-max="50")
                        #groupMax.col

                        |  Verify Numbers
                        br
                        input#yes.qp.req1.lin(type='text' name='cop1_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 0)")
                        input#yes.qp.req1.lin(type='text' name='cop2_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 1)")
                        input#yes.qp.req1.lin(type='text' name='cop3_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 2)")
                        input#yes.qp.req1.lin(type='text' name='cop4_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 3)")
                        input#yes.qp.req1.lin(type='text' name='cop5_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 4)")
                        input#yes.qp.req1.lin(type='text' name='cop6_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 5)" data-max="49")
                        // 7th verification number for Lotto Max
                        if nameVal == "lotto max"
                         input#yes.qp.req1.lin(type='text' name='cop7_'+lotteryIdVal+'_tn_'+(tn-1) maxlength='2' onchange="chooseNoOnChange(this, '"+lotteryIdVal + '_' + (tn-1)+"c', 6)" data-max="50")


          .col-lg-4.col-md-5.col-sm-8.p-spacer
            .card.cardnew
              .card-header.text-center.cardheadernew.font-weight-normal.text-white Payment
              .card-body.row.m-2.cardbodyright#paySide(style="display:none;")
                
                #lotteryInvoiceDetails
                #lotteryInvoiceTotal
                

                div#card-container
                 p| 
                  <div class="whitebg">Please check your lucky numbers and personal information any changes let us know 2 weeks before the lottery starts</div>
                  <br><br>
                 <div class="text-center">
                   button.btn-primary.reviewbtn(name='reviewbtn') Review order</div>

                    input#txn_status.form-control(type='hidden' name='txn_status' placeholder='txn_status')

                    input#txn_response.form-control(type='hidden' name='txn_response' placeholder='txn_response')  
  

    
// End Contact Section
// End #main
div.container 
    include ./home-footer.pug 

include ./new-footer.pug
style. 
  #myTab .nav-item {
    margin: auto;

  }
  #myTab .nav-link {
    color: #fff;
    filter: brightness(0.82);
    border-bottom: 10px solid #0b7087;
    text-align: center;
  }
  #myTab .nav-link.active {
    background-color: inherit;
    border: 0;
    border-bottom: 10px solid;
    filter: brightness(1);
  }
  .bg-themedarkblue .card,.bg-themedarkblue form, .bg-themedarkblue .card-body  {
    background-color:inherit;
    background-color: rgb(22, 74, 104)!important;
  }
  .text-dark {
    color: #000!important;
  }
  #footer .footer-top h4, #footer .footer-top .footer-links ul a {
    color: #fff;
  }
script.
  
  let chooseNoMap = {};
  function chooseNoOnChange(elem, id, indexx) {
    
    if(elem.value > elem.parentElement.lastElementChild.dataset.max) {
      alert("Only number from 1 to "+elem.parentElement.lastElementChild.dataset.max+" allowed!!");
      elem.value = "";
      return 0;
    }
    if(elem.value.length <2) {
      elem.value = "0" + elem.value;
    }
    
    if(chooseNoMap[id] && chooseNoMap[id].includes(elem.value)) {
      
      alert('Duplicate numbers not allowed!!'); 
      elem.value = "";
      return 0;
      
    }
    
    chooseNoMap[id] = [];
    $(elem).parent().children('input' + '.' + $(elem).attr('class').replaceAll(' ', '.')).each(function () {
      chooseNoMap[id].push(this.value);
    });
    
    if(id.includes('c')){
      if(chooseNoMap[id.replace('c', '')] && chooseNoMap[id.replace('c', '') + 'c'] && chooseNoMap[id.replace('c', '')][indexx] != chooseNoMap[id.replace('c', '') + 'c'][indexx]) {

        alert('Verify Numbers should be same!!'); 
        elem.value = "";
        
        chooseNoMap[id] = [];
        $(elem).parent().children('input' + '.' + $(elem).attr('class').replaceAll(' ', '.')).each(function () {
          chooseNoMap[id].push(this.value);
        });
        
      }
    }
  }