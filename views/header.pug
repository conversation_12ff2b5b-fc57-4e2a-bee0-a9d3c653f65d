doctype html
head
  meta(charset='utf-8')
  meta(content='width=device-width, initial-scale=1.0' name='viewport')
  title Lets play group lottery
  meta(content='' name='descriptison')
  meta(content='' name='keywords')
  // Favicons
  link(href='/assets/img/favicon.png' rel='icon')
  link(href='/assets/img/apple-touch-icon.png' rel='apple-touch-icon')
  // Google Fonts
  link(rel="preconnect" href="https://fonts.gstatic.com")
  link(href="https://fonts.googleapis.com/css2?family=Assistant:wght@500&family=Thasadith&display=swap" rel="stylesheet")
  //- link(rel="preconnect" href="")
  link(href='https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Montserrat:300,400,500,700' rel='stylesheet')
  // Vendor CSS Files
  link(href='/vendor/bootstrap/css/bootstrap.min.css' rel='stylesheet')
  link(href='/vendor/font-awesome/css/font-awesome.min.css' rel='stylesheet')
  link(href='/vendor/owl.carousel/assets/owl.carousel.min.css' rel='stylesheet')
  link(href='/css/style.css' rel='stylesheet')
  script(src='/vendor/jquery/jquery.min.js')
  script(src="/node_modules/sweetalert2/dist/sweetalert2.all.min.js")
style.
  .swal2-title {
    margin: 0px !important;
  }
  .swal2-actions button{
    background-color : #17a2b8;
    outline:none;
  }
  
// ======= Header =======
header#header.fixed-top11
  .top-bar.d-flex
    .logo.float-left
      a.d-flex.ml-3(href='/' style="display:flex;margin-left:1rem;")
        img.img-fluid(src='/assets/img/LOGO_1.png' alt='Lets Play Lottery Group')
        h5.ml-3.my-auto(style="font-family: 'Thasadith';color:#fff;") Lets Play Group Lottery
  .bottom-bar.d-none.d-lg-flex
    .logo.d-flex.mr-auto
      a(href='#')
        i.fa.fa-instagram
      a(href='#')
        i.fa.fa-facebook-square
      a(href='#')
        i.fa.fa-twitter
      span.d-flex.my-auto 
        | I do not sell lottery tickets/Not a retailer just form a group play

      //- img.img-fluid(src='/assets/img/logo.png' alt='')
    nav.main-nav.ml-auto.d-none.d-lg-flex
      ul 
        if isLogged
          li
            a(href='/logout')  Logout  
        else 
          li
            a(href='/login')  Login
          li
            a(href='/registration')  Register
            
        li
          a(href='/lotteries') Lotteries
        li
          a(href='/play') Let&apos;s Play
        li
          a(href='/about') About Us
          
        li
          a(href='/demo') Demo Page
            
        li.d-flex.d-lg-none 
          a(href='#')
            i.fa.fa-instagram
          a(href='#')
            i.fa.fa-facebook-square
          a(href='#')
            i.fa.fa-twitter
           
          
// #header
