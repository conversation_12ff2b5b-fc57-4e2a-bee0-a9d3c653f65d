include ./header.pug

block content
  .container.mt-5
    .row.justify-content-center
      .col-md-8.col-lg-6
        .card.border-danger.shadow-lg
          .card-header.bg-danger.text-white.text-center.py-4
            .mb-3
              i.fas.fa-flag.fa-3x(style="color: #ff0000;")
              span.ms-2 🇨🇦
            h3.mb-0 Canada Residents Only
          
          .card-body.p-5.text-center
            .alert.alert-info.mb-4
              h5.alert-heading.mb-3
                i.fas.fa-info-circle.me-2
                | Registration Restricted
              
              p.mb-0 
                | Thank you for your interest in 
                strong Let's Play Group Lottery!
            
            .mb-4
              p.lead.text-muted
                | Unfortunately, our lottery services are currently available 
                strong exclusively to Canadian residents 
                | due to regulatory requirements and provincial gaming laws.
            
            .row.mb-4
              .col-12
                .bg-light.p-4.rounded
                  h6.text-primary.mb-3
                    i.fas.fa-gavel.me-2
                    | Legal Compliance Notice
                  p.small.text-muted.mb-0
                    | This restriction is in accordance with Canadian lottery regulations 
                    | and provincial gaming laws that govern online lottery participation.
            
            .mb-4
              h6.text-dark.mb-3 If you are a Canadian resident:
              .text-start
                ul.list-unstyled
                  li.mb-2
                    i.fas.fa-plane.text-info.me-2
                    | Currently traveling abroad
                  li.mb-2
                    i.fas.fa-wifi.text-info.me-2
                    | Using a VPN or proxy service
                  li.mb-2
                    i.fas.fa-question-circle.text-info.me-2
                    | Believe this message is displayed in error
            
            .alert.alert-warning.mb-4
              h6.mb-2
                i.fas.fa-envelope.me-2
                | Need Assistance?
              p.mb-2 Please contact our support team:
              p.mb-1
                strong
                  i.fas.fa-envelope.me-2
                  | <EMAIL>
              p.mb-0.small.text-muted
                | We'll be happy to assist with eligibility verification.
            
            if locationData && locationData.country && locationData.country !== 'Unknown'
              .mb-4
                .bg-light.p-3.rounded
                  h6.text-muted.mb-2
                    i.fas.fa-map-marker-alt.me-2
                    | Detected Location
                  p.mb-1
                    strong= locationData.city + ', ' + locationData.country
                  p.small.text-muted.mb-0
                    | IP: #{locationData.ip}
            
            .mt-4
              a.btn.btn-primary.btn-lg.me-3(href="/")
                i.fas.fa-home.me-2
                | Return to Homepage
              
              a.btn.btn-outline-info(href="/contact-us")
                i.fas.fa-envelope.me-2
                | Contact Support
            
            .mt-4.pt-3.border-top
              p.small.text-muted.mb-0
                | Thank you for your understanding and interest in our services.

block scripts
  script.
    // Add some interactive elements
    document.addEventListener('DOMContentLoaded', function() {
      // Add fade-in animation
      const card = document.querySelector('.card');
      card.style.opacity = '0';
      card.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        card.style.transition = 'all 0.5s ease-in-out';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
      }, 100);
      
      // Log the blocked attempt for analytics (client-side)
      console.log('🚫 Registration blocked - Non-Canadian IP detected');
    });

style.
  .card {
    border-radius: 15px;
    overflow: hidden;
  }
  
  .card-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  }
  
  .fas.fa-flag {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }
  
  .alert-info {
    border-left: 4px solid #17a2b8;
  }
  
  .alert-warning {
    border-left: 4px solid #ffc107;
  }
  
  .bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
  }
  
  .btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
  }
  
  .lead {
    font-size: 1.15rem;
    line-height: 1.6;
  }
  
  .list-unstyled li {
    padding: 5px 0;
  }
  
  @media (max-width: 768px) {
    .container {
      padding: 0 15px;
    }
    
    .card-body {
      padding: 2rem 1.5rem !important;
    }
    
    .btn-lg {
      width: 100%;
      margin-bottom: 10px;
    }
  }
