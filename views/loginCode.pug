
include ./header.pug

block content
    .container.mt-5
        .row.p-spacer
            .col-lg-4
            .col-lg-4.col-md-8.col-sm-8.p-spacer
                div(style="display:block;box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.6)")
                    include ./includeSections/loginCodeForm.pug

script.
    function checkVerification() {
        let urlParams = new URLSearchParams(window.location.search);
        let token = urlParams.get('token');
        let type = urlParams.get('type');

        let d = {
            verificationCode: $('#verificationCode').val(),
            token: token,
            type: type
        };

            $.ajax({
                url: '/verification',
                type: 'POST',
                data: d,
                success: (r) => {
                    //- alert(r.msg);
                    if(r.status == 200) {
                        Swal.fire({
                        title: 'Success',
                        text: r.msg,
                        icon: 'success',
                        //- showCancelButton: true,
                        confirmButtonText: 'Ok',
                        //- cancelButtonText: 'No, cancel!',
                        reverseButtons: true
                        }).then((result) => {
                            location = '/play';
                        })
                    }else{
                        Swal.fire({
                        title: 'Error',
                        text: r.msg,
                        icon: 'error',
                        //- showCancelButton: true,
                        confirmButtonText: 'Ok',
                        //- cancelButtonText: 'No, cancel!',
                        reverseButtons: true
                        }).then((result) => {
                            //- location = '/play';
                        })
                    }
                }
            });
    }   