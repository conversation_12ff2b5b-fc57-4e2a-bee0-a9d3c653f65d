const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
    host: '*************',
    user: 'errol',
    password: 'qwby:123eweD',
    database: 'errol'
};

async function cleanupFakeUsers() {
    let connection;
    
    try {
        console.log('Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        
        // First, let's see what tables exist
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('Available tables:');
        tables.forEach(table => {
            console.log('- ' + Object.values(table)[0]);
        });

        // Check if user table exists (might be named differently)
        const userTableName = tables.find(table =>
            Object.values(table)[0].toLowerCase().includes('user')
        );

        if (!userTableName) {
            console.log('No user table found!');
            return;
        }

        const tableName = Object.values(userTableName)[0];
        console.log(`\nUsing table: ${tableName}`);

        // First, let's see how many users we have total
        const [totalUsers] = await connection.execute(`SELECT COUNT(*) as total FROM ${tableName}`);
        console.log(`Total users in database: ${totalUsers[0].total}`);
        
        // Delete users with clearly fake/generated data
        console.log('\nDeleting fake users...');
        
        const deleteQuery = `
            DELETE FROM ${tableName}
            WHERE 
                -- Delete users with random string names (longer than 8 chars with mixed case)
                (LENGTH(first_name) > 8 AND first_name REGEXP '[A-Z][a-z]*[A-Z]') OR
                (LENGTH(last_name) > 8 AND last_name REGEXP '[A-Z][a-z]*[A-Z]') OR
                
                -- Delete users with random string names (all caps or weird patterns)
                first_name REGEXP '^[A-Z]{3,}[a-z]{3,}[A-Z]' OR
                last_name REGEXP '^[A-Z]{3,}[a-z]{3,}[A-Z]' OR
                
                -- Delete users with names containing numbers or special chars
                first_name REGEXP '[0-9]' OR
                last_name REGEXP '[0-9]' OR
                
                -- Delete users with very long random names
                LENGTH(first_name) > 15 OR
                LENGTH(last_name) > 15 OR
                
                -- Delete users with random city names
                LENGTH(city) > 15 OR
                city REGEXP '^[A-Z]{2,}[a-z]{2,}[A-Z]' OR
                
                -- Delete users with random postal codes
                LENGTH(postal_code) > 10 OR
                postal_code REGEXP '^[A-Za-z]{5,}' OR
                
                -- Delete users with test emails
                email_id LIKE '%test%' OR
                email_id LIKE '%dummy%' OR
                email_id LIKE '%fake%' OR
                email_id LIKE '%example%' OR
                
                -- Delete users with clearly fake names patterns
                first_name LIKE '%test%' OR
                last_name LIKE '%test%' OR
                first_name LIKE '%dummy%' OR
                last_name LIKE '%dummy%' OR
                
                -- Delete users with random string patterns (mixed case in middle)
                first_name REGEXP '^[a-z]+[A-Z]+[a-z]+' OR
                last_name REGEXP '^[a-z]+[A-Z]+[a-z]+' OR
                
                -- Delete users with names that are clearly random (no vowels pattern)
                (first_name NOT REGEXP '[aeiouAEIOU]' AND LENGTH(first_name) > 3) OR
                (last_name NOT REGEXP '[aeiouAEIOU]' AND LENGTH(last_name) > 3) OR
                
                -- Delete users with too many consonants in a row
                first_name REGEXP '[bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ]{4,}' OR
                last_name REGEXP '[bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ]{4,}' OR
                
                -- Delete users registered today (likely test data)
                DATE(registration_date) = CURDATE()
        `;
        
        const [deleteResult] = await connection.execute(deleteQuery);
        console.log(`Deleted ${deleteResult.affectedRows} fake users`);
        
        // Check remaining users
        const [remainingUsers] = await connection.execute(`SELECT COUNT(*) as total FROM ${tableName}`);
        console.log(`Remaining users: ${remainingUsers[0].total}`);

        // Show sample of remaining users
        const [sampleUsers] = await connection.execute(`
            SELECT id, first_name, last_name, email_id, city, registration_date
            FROM ${tableName}
            ORDER BY registration_date DESC
            LIMIT 10
        `);
        
        console.log('\nSample of remaining users:');
        sampleUsers.forEach(user => {
            console.log(`${user.id}: ${user.first_name} ${user.last_name} - ${user.email_id} - ${user.city} - ${user.registration_date}`);
        });
        
        console.log('\nCleanup completed successfully!');
        
    } catch (error) {
        console.error('Error during cleanup:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Run the cleanup
cleanupFakeUsers();
