const mysql = require('mysql2/promise');

async function fixAdmin() {
    const connection = await mysql.createConnection({
        host: '*************',
        user: 'errol',
        password: 'qwby:123eweD',
        database: 'errol'
    });

    try {
        // Clear admin table
        await connection.execute('DELETE FROM admin');
        console.log('✅ Cleared admin table');

        // Insert admin with exact password
        const password = 'CdQC$M<@By[d8;96c';
        await connection.execute(
            'INSERT INTO admin (name, surname, emailid, password) VALUES (?, ?, ?, ?)',
            ['errol', 'fereira', '<EMAIL>', password]
        );
        console.log('✅ Inserted admin user');

        // Verify
        const [result] = await connection.execute('SELECT * FROM admin');
        console.log('📋 Admin table contents:', result);

    } finally {
        await connection.end();
    }
}

fixAdmin().catch(console.error);
