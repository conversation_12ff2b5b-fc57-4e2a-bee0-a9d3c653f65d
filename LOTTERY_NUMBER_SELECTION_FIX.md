# Lottery Number Selection Fix

## Problem
The lottery number selection interface (Quick Pick/Choose Numbers) was not showing correctly according to the business requirements:
- Only lotto 6/49 and lotto max should show Quick Pick/Choose Numbers options
- Other lotteries (like sick kids lottery, Canadian Cancer Lottery) should NOT show number selection since organizers provide the numbers

## Root Causes Identified

### 1. Database Configuration Issues
- **sick kids lottery**: Had `custom_number = 1` (should be 0)
- **Canadian Cancer Lottery**: Had `custom_number = 1` (should be 0)  
- **lotto 6/49**: Was inactive (`is_active = 0`)

### 2. Missing Frontend Interface
- The play.pug template was missing the Quick Pick/Choose Numbers interface entirely
- Only had player name inputs, no number selection options

## Solution Implemented

### 1. Fixed Database Configuration
Updated `lottery_master` table to ensure correct settings:

**Correct Configuration:**
- **lotto 6/49**: `is_active = 1`, `quickpick = 1`, `custom_number = 1` ✅
- **lotto max**: `is_active = 1`, `quickpick = 1`, `custom_number = 1` ✅  
- **sick kids lottery**: `is_active = 1`, `quickpick = 0`, `custom_number = 0` ✅
- **Canadian Cancer Lottery**: `is_active = 1`, `quickpick = 0`, `custom_number = 0` ✅
- **All other lotteries**: `quickpick = 0`, `custom_number = 0` ✅

### 2. Added Missing Frontend Interface
**File**: `views/play.pug`

**Added Features:**
- Conditional rendering based on `val.quickpick` and `val.custom_number` database values
- Quick Pick radio button (when `quickpick = 1`)
- Choose Numbers radio button (when `custom_number = 1`)
- Number input fields for manual selection
- Different number ranges for different lotteries:
  - **lotto 6/49**: 6 numbers between 1-49
  - **lotto max**: 7 numbers between 1-50
- JavaScript toggle function to show/hide number inputs
- Form validation for required number inputs

### 3. JavaScript Functionality
**Function**: `toggleNumberSelection(radio, lotteryId, ticketNumber)`
- Shows/hides number selection inputs based on radio button selection
- Manages required field validation
- Clears number inputs when switching to Quick Pick

## Results

### ✅ Current Behavior (Correct)
- **lotto 6/49**: Shows Quick Pick and Choose Numbers options
- **lotto max**: Shows Quick Pick and Choose Numbers options  
- **sick kids lottery**: Shows NO number selection (organizer provides numbers)
- **Canadian Cancer Lottery**: Shows NO number selection (organizer provides numbers)

### ✅ User Experience
- Users can select Quick Pick for computer-generated numbers
- Users can choose their own numbers with proper validation
- Interface only appears for lotteries that support user number selection
- Clean, intuitive design with proper form validation

### ✅ Technical Implementation
- Database-driven conditional rendering
- Proper form field naming for backend processing
- Responsive design that works on all devices
- JavaScript validation and user interaction

## Files Modified
1. **Database**: `lottery_master` table - Fixed `quickpick` and `custom_number` values
2. **Template**: `views/play.pug` - Added conditional number selection interface
3. **JavaScript**: Added `toggleNumberSelection()` function for user interaction

## Testing
- ✅ lotto 6/49 and lotto max show number selection options
- ✅ Other lotteries do not show number selection options  
- ✅ Quick Pick/Choose Numbers toggle works correctly
- ✅ Form validation works for number inputs
- ✅ Backend processing expects correct field names
