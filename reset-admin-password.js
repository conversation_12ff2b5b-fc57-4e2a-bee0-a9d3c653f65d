const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
    host: '*************',
    user: 'errol',
    password: 'qwby:123eweD',
    database: 'errol'
};

async function resetAdminPassword() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        
        console.log('✅ Connected successfully!');
        
        // First, let's see current admin data
        console.log('\n🔍 Current admin data:');
        const [currentAdmin] = await connection.execute('SELECT * FROM admin');
        console.log(currentAdmin);
        
        // Set admin credentials as requested
        const newEmail = '<EMAIL>';
        const newPassword = 'CdQC$M<@By[d8;96c';
        
        console.log('\n🔄 Updating admin credentials...');
        console.log('New Email:', newEmail);
        console.log('New Password:', newPassword);
        
        // Update admin credentials
        const [updateResult] = await connection.execute(
            'UPDATE admin SET emailid = ?, password = ? WHERE id = 1',
            [newEmail, newPassword]
        );
        
        if (updateResult.affectedRows > 0) {
            console.log('✅ Admin credentials updated successfully!');
            
            // Verify the update
            console.log('\n🔍 Verifying updated credentials:');
            const [verifyAdmin] = await connection.execute('SELECT * FROM admin WHERE id = 1');
            console.log(verifyAdmin[0]);
            
            console.log('\n🎉 SUCCESS! You can now login with:');
            console.log('📧 Email: <EMAIL>');
            console.log('🔑 Password: CdQC$M<@By[d8;96c');
            console.log('🌐 URL: https://letsplaygrouplottery.com/admin/login');
            
        } else {
            console.log('❌ No admin record was updated');
        }
        
    } catch (err) {
        console.error('❌ Error:', err.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Database connection closed.');
        }
    }
}

// Run the reset
resetAdminPassword();
