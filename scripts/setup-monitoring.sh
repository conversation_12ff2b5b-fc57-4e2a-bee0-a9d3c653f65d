#!/bin/bash

# Setup script for database monitoring and backup automation

SCRIPT_DIR="/home/<USER>/errol3491-letsplaygroup_latest_updated-86613cc38e6c/scripts"
CRON_FILE="/tmp/lottery_cron"

echo "Setting up professional database monitoring and backup system..."

# Make scripts executable
chmod +x "$SCRIPT_DIR/backup-database.sh"
chmod +x "$SCRIPT_DIR/database-recovery.sh"

# Create directories
mkdir -p /home/<USER>/database-backups
mkdir -p /home/<USER>/logs

# Setup cron jobs
echo "# Let's Play Group Lottery - Database Monitoring & Backup" > "$CRON_FILE"
echo "# Daily backup at 2:00 AM" >> "$CRON_FILE"
echo "0 2 * * * $SCRIPT_DIR/backup-database.sh >> /home/<USER>/logs/backup-cron.log 2>&1" >> "$CRON_FILE"
echo "" >> "$CRON_FILE"
echo "# Health check every 6 hours" >> "$CRON_FILE"
echo "0 */6 * * * cd $SCRIPT_DIR/../ && node scripts/database-health-monitor.js >> /home/<USER>/logs/health-cron.log 2>&1" >> "$CRON_FILE"
echo "" >> "$CRON_FILE"
echo "# Weekly cleanup of old logs (keep 30 days)" >> "$CRON_FILE"
echo "0 3 * * 0 find /home/<USER>/logs -name '*.log' -mtime +30 -delete" >> "$CRON_FILE"

# Install cron jobs
crontab "$CRON_FILE"
rm "$CRON_FILE"

echo "✅ Cron jobs installed:"
echo "  - Daily backup at 2:00 AM"
echo "  - Health check every 6 hours"
echo "  - Weekly log cleanup"

# Create initial backup
echo "Creating initial backup..."
"$SCRIPT_DIR/backup-database.sh"

# Run initial health check
echo "Running initial health check..."
cd "$SCRIPT_DIR/../" && node scripts/database-health-monitor.js

echo ""
echo "🎉 Professional database protection system is now active!"
echo ""
echo "📋 What's now protected:"
echo "  ✅ Automated daily backups with 30-day retention"
echo "  ✅ Database health monitoring every 6 hours"
echo "  ✅ Email alerts for critical issues"
echo "  ✅ Data consistency checks"
echo "  ✅ Recovery system with emergency backups"
echo ""
echo "📁 Important locations:"
echo "  - Backups: /home/<USER>/database-backups/"
echo "  - Logs: /home/<USER>/logs/"
echo "  - Scripts: $SCRIPT_DIR/"
echo ""
echo "🔧 Manual commands:"
echo "  - Manual backup: $SCRIPT_DIR/backup-database.sh"
echo "  - Health check: node $SCRIPT_DIR/database-health-monitor.js"
echo "  - Recovery: $SCRIPT_DIR/database-recovery.sh"
echo ""
echo "📧 Alerts will be sent to: <EMAIL>"
