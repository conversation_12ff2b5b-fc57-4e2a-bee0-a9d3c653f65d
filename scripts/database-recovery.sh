#!/bin/bash

# Professional Database Recovery Script
# This script provides safe database recovery options

DB_HOST="*************"
DB_USER="errol"
DB_PASS="qwby:123eweD"
DB_NAME="errol"
BACKUP_DIR="/home/<USER>/database-backups"
LOG_FILE="/home/<USER>/recovery.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to list available backups
list_backups() {
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/lottery_backup_*.sql.gz | awk '{print NR". "$9" ("$5" bytes) - "$6" "$7" "$8}'
}

# Function to create emergency backup before recovery
create_emergency_backup() {
    local emergency_file="emergency_backup_$(date +%Y%m%d_%H%M%S).sql"
    log_message "Creating emergency backup before recovery: $emergency_file"
    
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/$emergency_file"
    
    if [ $? -eq 0 ]; then
        gzip "$BACKUP_DIR/$emergency_file"
        log_message "Emergency backup created successfully"
        return 0
    else
        log_message "ERROR: Emergency backup failed"
        return 1
    fi
}

# Function to restore from backup
restore_from_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        log_message "ERROR: Backup file not found: $backup_file"
        return 1
    fi
    
    log_message "Starting recovery from: $backup_file"
    
    # Create emergency backup first
    if ! create_emergency_backup; then
        log_message "ERROR: Cannot proceed without emergency backup"
        return 1
    fi
    
    # Decompress if needed
    if [[ "$backup_file" == *.gz ]]; then
        log_message "Decompressing backup file"
        gunzip -c "$backup_file" | mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME"
    else
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$backup_file"
    fi
    
    if [ $? -eq 0 ]; then
        log_message "Database recovery completed successfully"
        
        # Verify recovery
        verify_recovery
        return $?
    else
        log_message "ERROR: Database recovery failed"
        return 1
    fi
}

# Function to verify recovery
verify_recovery() {
    log_message "Verifying database recovery"
    
    # Check critical tables
    local tables=("admin" "user" "lottery_master" "ticket" "payment_ticket_details")
    
    for table in "${tables[@]}"; do
        local count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -se "SELECT COUNT(*) FROM $table" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            log_message "Table $table: $count records"
        else
            log_message "ERROR: Cannot verify table $table"
            return 1
        fi
    done
    
    # Check admin account exists
    local admin_count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -se "SELECT COUNT(*) FROM admin" 2>/dev/null)
    
    if [ "$admin_count" -eq 0 ]; then
        log_message "WARNING: No admin accounts found after recovery"
        
        # Recreate admin account
        log_message "Recreating admin account"
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        INSERT INTO admin (name, surname, emailid, password) 
        VALUES ('errol', 'fereira', '<EMAIL>', 'CdQC\$M<@By[d8;96c');"
        
        if [ $? -eq 0 ]; then
            log_message "Admin account recreated successfully"
        else
            log_message "ERROR: Failed to recreate admin account"
            return 1
        fi
    fi
    
    log_message "Database verification completed successfully"
    return 0
}

# Main recovery menu
show_menu() {
    echo "=================================="
    echo "DATABASE RECOVERY SYSTEM"
    echo "Let's Play Group Lottery"
    echo "=================================="
    echo "1. List available backups"
    echo "2. Restore from latest backup"
    echo "3. Restore from specific backup"
    echo "4. Verify current database"
    echo "5. Create manual backup"
    echo "6. Exit"
    echo "=================================="
}

# Main script logic
case "$1" in
    "auto-restore")
        # Automatic restore from latest backup
        latest_backup=$(ls -t "$BACKUP_DIR"/lottery_backup_*.sql.gz | head -1)
        if [ -n "$latest_backup" ]; then
            log_message "Auto-restore initiated from: $latest_backup"
            restore_from_backup "$latest_backup"
        else
            log_message "ERROR: No backups found for auto-restore"
            exit 1
        fi
        ;;
    "verify")
        # Verify database only
        verify_recovery
        ;;
    *)
        # Interactive mode
        while true; do
            show_menu
            read -p "Select option (1-6): " choice
            
            case $choice in
                1)
                    list_backups
                    ;;
                2)
                    latest_backup=$(ls -t "$BACKUP_DIR"/lottery_backup_*.sql.gz | head -1)
                    if [ -n "$latest_backup" ]; then
                        echo "Latest backup: $latest_backup"
                        read -p "Proceed with restore? (y/N): " confirm
                        if [[ $confirm =~ ^[Yy]$ ]]; then
                            restore_from_backup "$latest_backup"
                        fi
                    else
                        echo "No backups found"
                    fi
                    ;;
                3)
                    list_backups
                    read -p "Enter backup filename: " backup_name
                    if [ -f "$BACKUP_DIR/$backup_name" ]; then
                        restore_from_backup "$BACKUP_DIR/$backup_name"
                    else
                        echo "Backup file not found"
                    fi
                    ;;
                4)
                    verify_recovery
                    ;;
                5)
                    create_emergency_backup
                    ;;
                6)
                    echo "Exiting..."
                    exit 0
                    ;;
                *)
                    echo "Invalid option"
                    ;;
            esac
            
            echo
            read -p "Press Enter to continue..."
        done
        ;;
esac
