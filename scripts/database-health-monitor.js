const mysql = require('mysql2/promise');
const nodemailer = require('nodemailer');
const fs = require('fs').promises;

// Database configuration
const dbConfig = {
    host: '*************',
    user: 'errol',
    password: 'qwby:123eweD',
    database: 'errol'
};

// Email configuration
const emailConfig = {
    host: 'smtp.ionos.com',
    port: 587,
    secure: false,
    auth: {
        user: '<EMAIL>',
        pass: 'letz365ch34CR$8utx96c'
    }
};

class DatabaseHealthMonitor {
    constructor() {
        this.connection = null;
        this.transporter = nodemailer.createTransport(emailConfig);
        this.logFile = '/home/<USER>/database-health.log';
        this.alertEmail = '<EMAIL>';
    }

    async log(message) {
        const timestamp = new Date().toISOString();
        const logEntry = `${timestamp} - ${message}\n`;
        console.log(logEntry.trim());
        
        try {
            await fs.appendFile(this.logFile, logEntry);
        } catch (err) {
            console.error('Failed to write to log file:', err);
        }
    }

    async sendAlert(subject, message) {
        try {
            await this.transporter.sendMail({
                from: '<EMAIL>',
                to: this.alertEmail,
                subject: `🚨 LOTTERY SYSTEM ALERT: ${subject}`,
                html: `
                    <h2>Database Health Alert</h2>
                    <p><strong>System:</strong> Let's Play Group Lottery</p>
                    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Alert:</strong> ${subject}</p>
                    <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #dc3545;">
                        <pre>${message}</pre>
                    </div>
                    <p><em>This is an automated alert from your lottery system monitoring.</em></p>
                `
            });
            await this.log(`Alert sent: ${subject}`);
        } catch (err) {
            await this.log(`Failed to send alert: ${err.message}`);
        }
    }

    async connect() {
        try {
            this.connection = await mysql.createConnection(dbConfig);
            await this.log('Database connection established');
            return true;
        } catch (err) {
            await this.log(`Database connection failed: ${err.message}`);
            await this.sendAlert('Database Connection Failed', err.message);
            return false;
        }
    }

    async checkTableIntegrity() {
        const criticalTables = [
            'admin', 'user', 'lottery_master', 'ticket', 
            'payment_ticket_details', 'user_subscribe_list'
        ];

        const results = {};
        
        for (const table of criticalTables) {
            try {
                const [rows] = await this.connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
                results[table] = rows[0].count;
                await this.log(`Table ${table}: ${rows[0].count} records`);
            } catch (err) {
                results[table] = 'ERROR';
                await this.log(`Table ${table} check failed: ${err.message}`);
                await this.sendAlert(`Table Check Failed: ${table}`, err.message);
            }
        }

        return results;
    }

    async checkDataConsistency() {
        const checks = [];

        try {
            // Check for orphaned tickets
            const [orphanedTickets] = await this.connection.execute(`
                SELECT COUNT(*) as count FROM ticket t 
                LEFT JOIN user u ON t.user_id = u.id 
                WHERE u.id IS NULL
            `);
            
            if (orphanedTickets[0].count > 0) {
                checks.push(`⚠️ ${orphanedTickets[0].count} orphaned tickets found`);
            }

            // Check for tickets without lottery
            const [ticketsWithoutLottery] = await this.connection.execute(`
                SELECT COUNT(*) as count FROM ticket t 
                LEFT JOIN lottery_master l ON t.lottery_id = l.id 
                WHERE l.id IS NULL
            `);
            
            if (ticketsWithoutLottery[0].count > 0) {
                checks.push(`⚠️ ${ticketsWithoutLottery[0].count} tickets without valid lottery`);
            }

            // Check for payments without tickets
            const [paymentsWithoutTickets] = await this.connection.execute(`
                SELECT COUNT(*) as count FROM payment_ticket_details p 
                WHERE p.ticket_id IS NULL OR p.ticket_id = ''
            `);
            
            if (paymentsWithoutTickets[0].count > 0) {
                checks.push(`⚠️ ${paymentsWithoutTickets[0].count} payments without ticket references`);
            }

            // Check admin account
            const [adminCount] = await this.connection.execute(`SELECT COUNT(*) as count FROM admin`);
            if (adminCount[0].count === 0) {
                checks.push(`🚨 CRITICAL: No admin accounts found!`);
                await this.sendAlert('CRITICAL: No Admin Accounts', 'The admin table is empty. System access compromised.');
            }

        } catch (err) {
            checks.push(`❌ Data consistency check failed: ${err.message}`);
        }

        return checks;
    }

    async generateHealthReport() {
        await this.log('Starting database health check');

        if (!await this.connect()) {
            return;
        }

        try {
            const tableStats = await this.checkTableIntegrity();
            const consistencyIssues = await this.checkDataConsistency();

            const report = {
                timestamp: new Date().toISOString(),
                tables: tableStats,
                issues: consistencyIssues,
                status: consistencyIssues.length === 0 ? 'HEALTHY' : 'ISSUES_FOUND'
            };

            await this.log(`Health check completed. Status: ${report.status}`);

            // Send weekly detailed report
            if (new Date().getDay() === 0) { // Sunday
                const reportText = `
Database Health Report - ${new Date().toLocaleDateString()}

TABLE STATISTICS:
${Object.entries(tableStats).map(([table, count]) => `  ${table}: ${count} records`).join('\n')}

CONSISTENCY CHECKS:
${consistencyIssues.length === 0 ? '  ✅ All checks passed' : consistencyIssues.map(issue => `  ${issue}`).join('\n')}

OVERALL STATUS: ${report.status}
                `;

                await this.sendAlert('Weekly Database Health Report', reportText);
            }

            // Send immediate alert if critical issues found
            const criticalIssues = consistencyIssues.filter(issue => issue.includes('CRITICAL'));
            if (criticalIssues.length > 0) {
                await this.sendAlert('CRITICAL Database Issues Detected', criticalIssues.join('\n'));
            }

        } catch (err) {
            await this.log(`Health check failed: ${err.message}`);
            await this.sendAlert('Health Check Failed', err.message);
        } finally {
            if (this.connection) {
                await this.connection.end();
                await this.log('Database connection closed');
            }
        }
    }
}

// Run the health check
const monitor = new DatabaseHealthMonitor();
monitor.generateHealthReport().catch(err => {
    console.error('Monitor failed:', err);
    process.exit(1);
});
