#!/bin/bash

# Professional Database Backup Script for Let's Play Group Lottery
# This script creates automated backups with rotation and monitoring

# Configuration
DB_HOST="*************"
DB_USER="errol"
DB_PASS="qwby:123eweD"
DB_NAME="errol"
BACKUP_DIR="/home/<USER>/database-backups"
LOG_FILE="/home/<USER>/backup.log"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="lottery_backup_${DATE}.sql"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to send alert email
send_alert() {
    local subject="$1"
    local message="$2"
    
    # Send email using the existing nodemailer system
    node -e "
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransporter({
        host: 'smtp.ionos.com',
        port: 587,
        secure: false,
        auth: {
            user: '<EMAIL>',
            pass: 'letz365ch34CR\$8utx96c'
        }
    });
    
    transporter.sendMail({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: '$subject',
        text: '$message'
    }).then(() => console.log('Alert sent')).catch(err => console.error('Alert failed:', err));
    "
}

# Start backup process
log_message "Starting database backup process"

# Create backup
log_message "Creating backup: $BACKUP_FILE"
mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILE" 2>> "$LOG_FILE"

# Check if backup was successful
if [ $? -eq 0 ] && [ -s "$BACKUP_DIR/$BACKUP_FILE" ]; then
    # Compress backup
    gzip "$BACKUP_DIR/$BACKUP_FILE"
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/${BACKUP_FILE}.gz" | cut -f1)
    log_message "Backup successful: ${BACKUP_FILE}.gz (Size: $BACKUP_SIZE)"
    
    # Verify backup integrity
    gunzip -t "$BACKUP_DIR/${BACKUP_FILE}.gz" 2>> "$LOG_FILE"
    if [ $? -eq 0 ]; then
        log_message "Backup integrity verified"
    else
        log_message "ERROR: Backup integrity check failed"
        send_alert "CRITICAL: Backup Integrity Failed" "The database backup failed integrity check. Immediate attention required."
    fi
    
else
    log_message "ERROR: Backup failed"
    send_alert "CRITICAL: Database Backup Failed" "The automated database backup failed. Check the system immediately."
    exit 1
fi

# Clean up old backups (keep only last 30 days)
log_message "Cleaning up old backups (keeping last $RETENTION_DAYS days)"
find "$BACKUP_DIR" -name "lottery_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete 2>> "$LOG_FILE"

# Count remaining backups
BACKUP_COUNT=$(find "$BACKUP_DIR" -name "lottery_backup_*.sql.gz" | wc -l)
log_message "Backup cleanup complete. $BACKUP_COUNT backups retained"

# Create backup report
TOTAL_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
log_message "Backup process completed. Total backup storage: $TOTAL_SIZE"

# Weekly backup report (send every Sunday)
if [ $(date +%u) -eq 7 ]; then
    WEEKLY_REPORT="Weekly Backup Report:
    - Total backups: $BACKUP_COUNT
    - Storage used: $TOTAL_SIZE
    - Latest backup: ${BACKUP_FILE}.gz ($BACKUP_SIZE)
    - System status: Healthy"
    
    send_alert "Weekly Backup Report - Let's Play Lottery" "$WEEKLY_REPORT"
fi

log_message "Backup script completed successfully"
