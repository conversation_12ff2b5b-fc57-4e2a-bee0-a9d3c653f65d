const db = require('./lib/database');

async function addPaymentStatusColumn() {
    try {
        // Add payment_status column if it doesn't exist
        await db.pool.query('ALTER TABLE ticket ADD COLUMN payment_status VARCHAR(20) DEFAULT "pending" COMMENT "Status of payment (pending, paid, failed)"');
        
        // Update existing records to 'paid' status
        await db.pool.query(`
            UPDATE ticket 
            SET payment_status = 'paid' 
            WHERE payment_status IS NULL
        `);
        
        console.log('Successfully added payment_status column and updated existing records');
        process.exit(0);
    } catch (err) {
        console.error('Error:', err);
        process.exit(1);
    }
}

addPaymentStatusColumn();
