const {Recipient, Sender, EmailParams} = require("mailersend");
const {mailersend} = require("./mailer<PERSON>endAP<PERSON>");

/**
 * Sends an email using MailerSend
 * @param {string} fromEmail - The sender's email address.
 * @param {string} fromName - The sender's name.
 * @param {string} recipientEmail - The recipient's email address.
 * @param {string} recipientName - The recipient's name.
 * @param {string} subject - The subject of the email.
 * @param {string} htmlContent - The HTML content of the email.
 * @param {string} textContent - The plain text content of the email.
 */
 function sendWelcomeEmail(recipientEmail, recipientName) {
    const recipients = [new Recipient(recipientEmail, recipientName)];
    const sender = new Sender("<EMAIL>", "Lets Play Group Lottery");

    const personalization = [
        {
            email:recipientEmail,
            data: {
                name: recipientName,
                support_email: "<EMAIL>"
            },
        }
    ]

    const emailParams = new EmailParams({
        from: sender,
        to: recipients,
        subject: 'Welcome to Lets Play Group Lottery',
        template_id: "zr6ke4nmp23gon12",
        personalization: personalization
    }).setTemplateId("zr6ke4nmp23gon12");

    mailersend.email.send(emailParams)
        .then((response) => {
            console.log("Email sent successfully:", response);
        })
        .catch((error) => {
            console.error("Failed to send email:", error);
        });
}

exports.sendWelcomeEmail = sendWelcomeEmail;
// Example usage
