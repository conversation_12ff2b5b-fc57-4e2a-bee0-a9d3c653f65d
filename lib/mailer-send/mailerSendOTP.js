const {Email<PERSON><PERSON><PERSON>, Recipient, Sender} = require("mailersend");
const {mailersend} = require("./mailerSendAPI");

function sendOtpEmail(recipientEmail, recipientName, otpCode) {
    const recipients = [new Recipient(recipientEmail, recipientName)];
    const sender = new Sender("<EMAIL>", "Lets Play Group Lottery");

    const personalization = [
        {
            email: recipientEmail,
            data: {
                otp_code: otpCode, // Add the OTP code to the personalization
                full_name: recipientName,
                support_email: '<EMAIL>'
            },
        }
    ];

    const emailParams = new EmailParams({
        from: sender,
        to: recipients,
        subject: "Your OTP Code",
        personalization: personalization
    }).setTemplateId("vywj2lpw29qg7oqz");

    mailersend.email.send(emailParams)
        .then((response) => {
            console.log("OTP Email sent successfully:", response);
        })
        .catch((error) => {
            console.error("Failed to send OTP email:", error);
        });
}

exports.sendOtpEmail = sendOtpEmail;
