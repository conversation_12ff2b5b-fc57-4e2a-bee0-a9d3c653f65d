const { promises } = require('fs');
const nodemailer = require('nodemailer');

exports.sendMailByNodemailer = async ({ to, html, subject }) => {
    return new Promise((resolve, reject) => {
        let transporter = nodemailer.createTransport({
            host: "smtp.ionos.com",  // Outgoing SMTP server
            port: 587,  // Use 587 with TLS
            secure: false,  // Must be false for TLS (port 587)

            auth: {
                user: process.env.NODE_MAILER_USER,
                pass: process.env.NODE_MAILER_PASSWORD
            },
            tls: {
                rejectUnauthorized: false, // Bypass SSL issues (use only for debugging)
            },
        });
        // let transporter = nodemailer.createTransport({
        //     service: 'gmail',
        //     auth: {
        //         user: process.env.NODE_MAILER_USER,
        //         pass: process.env.NODE_MAILER_PASSWORD
        //     }
        // });
        const mailOptions = {
            from: process.env.NODE_MAILER_USER,
            to: to,
            subject: subject,
            html: html
        };
        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                reject(error);
            } else {
                resolve(info)
            }
        });
    })

}