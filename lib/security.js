/**
 * Security utilities and middleware
 */

// Input sanitization function
function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return input;
  }
  
  // Remove potentially dangerous characters
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Validate email format
function isValidEmail(email) {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
}

// Validate phone number (basic)
function isValidPhone(phone) {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Validate numeric ID
function isValidId(id) {
  return /^\d+$/.test(id) && parseInt(id) > 0;
}

// Rate limiting store (in-memory - upgrade to Redis for production)
const rateLimitStore = new Map();

// Rate limiting middleware
function rateLimit(maxAttempts = 5, windowMs = 15 * 60 * 1000) { // 5 attempts per 15 minutes
  return (req, res, next) => {
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
    const key = `${ip}:${req.route.path}`;
    const now = Date.now();
    
    if (!rateLimitStore.has(key)) {
      rateLimitStore.set(key, []);
    }
    
    const attempts = rateLimitStore.get(key);
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return res.status(429).json({
        error: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
    
    validAttempts.push(now);
    rateLimitStore.set(key, validAttempts);
    
    next();
  };
}

// Input sanitization middleware
function sanitizeBody(req, res, next) {
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        // Skip sanitization for password fields to preserve special characters
        if (key.toLowerCase().includes('password')) {
          continue; // Don't sanitize password fields
        }
        req.body[key] = sanitizeInput(req.body[key]);
      }
    }
  }
  next();
}

// CSRF token generation and validation
function generateCSRFToken() {
  return require('crypto').randomBytes(32).toString('hex');
}

function validateCSRFToken(req, token) {
  return req.session && req.session.csrfToken === token;
}

// Admin authentication middleware with better security
function requireAdmin(req, res, next) {
  if (!req.session || !req.session.admin) {
    return res.status(401).redirect('/admin/login');
  }
  
  // Check session timeout (additional security)
  if (req.session.lastActivity && Date.now() - req.session.lastActivity > 30 * 60 * 1000) {
    req.session.destroy();
    return res.status(401).redirect('/admin/login');
  }
  
  req.session.lastActivity = Date.now();
  next();
}

// User authentication middleware
function requireAuth(req, res, next) {
  if (!req.session || (!req.session.admin && !req.session.user)) {
    return res.status(401).redirect('/login');
  }
  
  // Check session timeout
  if (req.session.lastActivity && Date.now() - req.session.lastActivity > 30 * 60 * 1000) {
    req.session.destroy();
    return res.status(401).redirect('/login');
  }
  
  req.session.lastActivity = Date.now();
  next();
}

module.exports = {
  sanitizeInput,
  isValidEmail,
  isValidPhone,
  isValidId,
  rateLimit,
  sanitizeBody,
  generateCSRFToken,
  validateCSRFToken,
  requireAdmin,
  requireAuth
};
