var auth = function(req, res, next) {
    if (req.session  && req.session.admin)
       next();
    else if(req.session  && req.session.user){
       next();
    }else
       res.redirect("/login");
  };

  var adminAuth = function(req,res,next){
   if (req.session  && req.session.admin){
   next();
   }else
   res.redirect("/admin/login");
  }

  module.exports = auth;
  module.exports.adminAuth = adminAuth;