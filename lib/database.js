require('dotenv').config({ path: process.env.NODE_ENV === 'production' ? '.env.production' : '.env' });
const mysql = require('mysql2');
const express = require('express');
const EmailTemplate = require('email-templates');
const nodemailer = require('nodemailer');
const path = require('path');
const app = express();
const util = require('util');

// Configuration for database connection
const config = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'lottery',
    port: process.env.DB_PORT || 3306,
    // Connection pool settings
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    enableKeepAlive: true,
    keepAliveInitialDelay: 0
};

// Create connection pool with error handling
const pool = mysql.createPool(config);
const poolPromise = pool.promise();

// Handle pool errors
pool.on('error', (err) => {
    console.error('Unexpected error on idle connection', err);
    // Attempt to reconnect
    pool.getConnection((error, connection) => {
        if (error) {
            console.error('Error reconnecting:', error);
        } else {
            connection.release();
            console.log('Successfully reconnected');
        }
    });
});

// For backward compatibility with old code using direct connection
const connection = mysql.createConnection(config);

// Try to connect with a timeout
console.log('Attempting to connect to database at', config.host);

connection.connect((err) => {
  if (err) {
    console.error("Error connecting to database:", err);
    console.error("Connection details:", {
      host: config.host,
      user: config.user,
      database: config.database,
      // Not logging password for security reasons
    });
    return;
  } 
  console.log('Successfully connected to the database');
});

// Create promisified query function for backward compatibility
const myQuery = util.promisify(connection.query).bind(connection);

// Email transport setup
let transporter = nodemailer.createTransport({
  host: "smtp.ionos.com",  // Outgoing SMTP server
  port: 587,  // Use 587 with TLS
  secure: false,  // Must be false for TLS (port 587)
  auth: {
    user: process.env.NODE_MAILER_USER || '<EMAIL>',
    pass: process.env.NODE_MAILER_PASSWORD
  },
  tls: {
    rejectUnauthorized: false, // Bypass SSL issues (use only for debugging)
  },
});

/**
 * Send email function
 * @param {string} imagePath - Path to image directory
 * @param {string} templateDir - Email template directory
 * @param {string} file - Image file name
 * @param {string} from - Sender email
 * @param {string|array} to - Recipient email(s)
 * @param {object} templateData - Data to pass to the template
 * @param {boolean} groupMail - Whether this is a group email
 * @returns {Promise}
 */
function sendEmail(
  imagePath,
  templateDir,
  file = null,
  from,
  to,
  templateData,
  groupMail = false
) {
  const fs = require('fs');

  // Prepare attachments array
  let attachments = [];

  // Only add attachment if file exists
  if (file && imagePath) {
    const fullFilePath = path.join(imagePath, file);
    if (fs.existsSync(fullFilePath)) {
      attachments.push({
        filename: file,
        path: fullFilePath
      });
      console.log('Adding attachment:', fullFilePath);
    } else {
      console.log('File not found, sending email without attachment:', fullFilePath);
    }
  }

  // Set up email template engine
  const email = new EmailTemplate({
    message: {
      from: from || "<EMAIL>",
      attachments: attachments,
    },
    transport: transporter,
    send: true,
    preview: false,
    views: {
      root: path.join(app.get("views") || 'views', "email-templates"),
      options: {
        extension: 'pug'
      }
    },
    juice: true,
    juiceResources: {
      preserveImportant: true,
      webResources: {
        relativeTo: path.join(app.get("views") || 'views', "email-templates")
      }
    }
  });

  // Send email using template
  return email.send({
    template: templateDir,
    message: {
      to: groupMail ? to.join(",") : to,
      subject: templateData.subject || `${templateData.lotteryName} - Group ${templateData.groupLabel} Information`,
    },
    locals: {
      subject: templateData.subject || `${templateData.lotteryName} - Group ${templateData.groupLabel} Information`,
      content: templateData.content,
      ...templateData
    }
  })
    .then((res) => {
      console.log("Email sent successfully");
      return 1;
    })
    .catch(err => {
      console.error("Error sending email:", err);
      throw err;
    });
}

// Export all functionality
module.exports = {
  // Main connection pool (promise-based) - use this for all new code
  pool: poolPromise,
  execute: poolPromise.execute.bind(poolPromise),
  query: poolPromise.query.bind(poolPromise),
  
  // Backward compatibility with old code
  connection: connection,
  myQuery: myQuery,
  sendEmail: sendEmail,
  
  // Default export is the promise pool for ES6 import
  __esModule: true,
  default: poolPromise
};
