/**
 * IP Geolocation utility for Canada-only registration blocking
 * Uses free ip-api.com service (1000 requests/month limit)
 */

const axios = require('axios');

/**
 * Get user's IP address from request
 * @param {Object} req - Express request object
 * @returns {string} IP address
 */
function getUserIP(req) {
    // Get IP from various possible headers (for proxy/load balancer support)
    const ip = req.headers['x-forwarded-for'] || 
               req.headers['x-real-ip'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress ||
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               req.ip;
    
    // Handle IPv6 localhost and extract IPv4 if needed
    if (ip === '::1' || ip === '::ffff:127.0.0.1') {
        return '127.0.0.1'; // localhost
    }
    
    // Extract IPv4 from IPv6 mapped address
    if (ip && ip.startsWith('::ffff:')) {
        return ip.substring(7);
    }
    
    return ip || '127.0.0.1';
}

/**
 * Check if IP address is from Canada using multiple geolocation services
 * @param {string} ip - IP address to check
 * @returns {Promise<Object>} Location data with isCanada flag
 */
async function checkIPLocation(ip) {
    try {
        // Skip geolocation for localhost/private IPs (development)
        if (ip === '127.0.0.1' || ip === 'localhost' ||
            ip.startsWith('192.168.') || ip.startsWith('10.') ||
            ip.startsWith('172.')) {
            console.log('🏠 Localhost/Private IP detected, allowing access for development');
            return {
                isCanada: true,
                country: 'Canada',
                countryCode: 'CA',
                region: 'Development',
                city: 'Localhost',
                ip: ip,
                source: 'localhost'
            };
        }

        console.log(`🌍 Checking IP location for: ${ip}`);

        // Try multiple geolocation services for better accuracy
        const locationData = await tryMultipleGeoServices(ip);

        if (locationData) {
            const isCanada = locationData.countryCode === 'CA';

            console.log(`📍 IP Location Result:`, {
                ip: locationData.ip,
                country: locationData.country,
                countryCode: locationData.countryCode,
                region: locationData.region,
                city: locationData.city,
                isCanada: isCanada,
                source: locationData.source
            });

            return {
                isCanada: isCanada,
                country: locationData.country,
                countryCode: locationData.countryCode,
                region: locationData.region,
                city: locationData.city,
                ip: locationData.ip,
                source: locationData.source
            };
        } else {
            console.error('❌ All geolocation services failed');
            // On all services failing, allow access (fail-open for better user experience)
            return {
                isCanada: true,
                country: 'Unknown',
                countryCode: 'XX',
                region: 'Unknown',
                city: 'Unknown',
                ip: ip,
                source: 'all-services-failed',
                error: 'All geolocation services failed'
            };
        }

    } catch (error) {
        console.error('❌ Geolocation service error:', error.message);

        // On network/service error, allow access (fail-open)
        return {
            isCanada: true,
            country: 'Unknown',
            countryCode: 'XX',
            region: 'Unknown',
            city: 'Unknown',
            ip: ip,
            source: 'network-error-fallback',
            error: error.message
        };
    }
}

/**
 * Try multiple geolocation services for better accuracy
 * @param {string} ip - IP address to check
 * @returns {Promise<Object|null>} Location data or null if all fail
 */
async function tryMultipleGeoServices(ip) {
    const services = [
        // Service 1: ip-api.com (most accurate, free)
        async () => {
            const response = await axios.get(`http://ip-api.com/json/${ip}?fields=status,message,country,countryCode,region,city,query`, {
                timeout: 5000
            });
            const data = response.data;
            if (data.status === 'success') {
                return {
                    country: data.country,
                    countryCode: data.countryCode,
                    region: data.region,
                    city: data.city,
                    ip: data.query,
                    source: 'ip-api.com'
                };
            }
            return null;
        },

        // Service 2: ipinfo.io (backup)
        async () => {
            const response = await axios.get(`https://ipinfo.io/${ip}/json`, {
                timeout: 5000
            });
            const data = response.data;
            if (data.country) {
                return {
                    country: data.country === 'IN' ? 'India' :
                             data.country === 'CA' ? 'Canada' :
                             data.country === 'US' ? 'United States' : data.country,
                    countryCode: data.country,
                    region: data.region || 'Unknown',
                    city: data.city || 'Unknown',
                    ip: data.ip || ip,
                    source: 'ipinfo.io'
                };
            }
            return null;
        },

        // Service 3: geojs.io (another backup)
        async () => {
            const response = await axios.get(`https://get.geojs.io/v1/ip/geo/${ip}.json`, {
                timeout: 5000
            });
            const data = response.data;
            if (data.country) {
                return {
                    country: data.country,
                    countryCode: data.country_code,
                    region: data.region || 'Unknown',
                    city: data.city || 'Unknown',
                    ip: data.ip || ip,
                    source: 'geojs.io'
                };
            }
            return null;
        }
    ];

    // Try each service until one succeeds
    for (let i = 0; i < services.length; i++) {
        try {
            console.log(`🔍 Trying geolocation service ${i + 1}...`);
            const result = await services[i]();
            if (result) {
                console.log(`✅ Service ${i + 1} succeeded: ${result.source}`);
                return result;
            }
        } catch (error) {
            console.log(`❌ Service ${i + 1} failed: ${error.message}`);
            continue;
        }
    }

    return null; // All services failed
}

/**
 * Middleware to check if user is from Canada
 * Blocks non-Canadian users from accessing registration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function requireCanadianIP(req, res, next) {
    try {
        const userIP = getUserIP(req);
        const locationData = await checkIPLocation(userIP);
        
        // Store location data in request for logging
        req.userLocation = locationData;
        
        if (locationData.isCanada) {
            console.log(`✅ Canadian user allowed: ${locationData.city}, ${locationData.region}`);
            next(); // Allow access
        } else {
            console.log(`🚫 Non-Canadian user blocked: ${locationData.country} (${locationData.city})`);
            
            // Log blocked attempt for analytics
            console.log(`📊 BLOCKED REGISTRATION ATTEMPT:`, {
                ip: userIP,
                country: locationData.country,
                city: locationData.city,
                timestamp: new Date().toISOString(),
                userAgent: req.headers['user-agent']
            });
            
            // Render Canada-only page
            res.status(403).render('canada-only', {
                title: 'Canada Residents Only',
                locationData: locationData,
                isLogged: false
            });
        }
        
    } catch (error) {
        console.error('❌ Error in requireCanadianIP middleware:', error);
        // On error, allow access (fail-open)
        next();
    }
}

module.exports = {
    getUserIP,
    checkIPLocation,
    requireCanadianIP
};
