{"name": "lottery", "version": "0.0.0", "private": true, "scripts": {"start": "nodemon app.js", "start_old": "node ./bin/www"}, "engines": {"node": "12.14.1"}, "dependencies": {"@sendgrid/mail": "^7.4.4", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cache-pug-templates": "^2.0.1", "cookie-parser": "^1.4.4", "dateformat": "^3.0.3", "debug": "~2.6.9", "dotenv": "^10.0.0", "email-templates": "^7.0.4", "express": "~4.16.0", "express-session": "^1.17.0", "fs": "0.0.1-security", "http-errors": "~1.6.2", "jsonwebtoken": "^8.5.1", "mail": "^0.2.3", "moment": "^2.27.0", "morgan": "~1.9.0", "multer": "^1.4.2", "mysql": "^2.16.0", "nodemailer": "^5.1.1", "nodemailer-sendmail-transport": "^1.0.2", "nodemailer-smtp-transport": "^2.7.4", "nodemon": "^2.0.15", "pug": "^2.0.4", "q": "^1.5.1", "twilio": "^3.76.1", "uuid": "^8.3.2"}}