let lotteryTotal = 0;
let clickCounter = 0;
let subscribePrice = 0;
let useraction = false;
let lotterycount = 0;
let added_lotteries = {};
var suc = false;
const registration_price = 4;

// INSTANT RESPONSIVENESS HELPER FUNCTION
function forceInstantUpdate() {
    // Force immediate DOM refresh for instant visual updates
    const paymentSection = document.getElementById('paySide');
    if (paymentSection) {
        paymentSection.offsetHeight; // Force reflow
    }
}


function calcTotalPriceDraw(text) {
    document.getElementById("lotteryprice").value = text.value * registration_price;
    document.getElementById("lotterypricetotal").value = (text.value * registration_price) + subscribePrice;
}

function prettyDate(dateString) {
    alert("prettyDate");
    //if it's already a date object and not a string you don't need this line:
    var date = new Date(dateString);
    var d = date.getDate();
    var monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    var m = monthNames[date.getMonth()];
    var y = date.getFullYear();
    return d + ' ' + m + ' ' + y;
}

function handleChange(checkbox, price, lotteryId, index) {
    console.log('🔄 === HANDLE CHANGE FUNCTION CALLED ===');
    console.log('📊 RAW PARAMETERS:', {
        'checkbox': checkbox,
        'price': price,
        'lotteryId': lotteryId,
        'index': index
    });

    console.log('📊 DETAILED ANALYSIS:', {
        'Checkbox Element': checkbox,
        'Checkbox Checked': checkbox ? checkbox.checked : 'CHECKBOX IS NULL',
        'LotteryType': checkbox ? checkbox.getAttribute("data-lname") : 'CHECKBOX IS NULL',
        'Price (raw)': price,
        'Price (type)': typeof price,
        'LotteryId (raw)': lotteryId,
        'LotteryId (type)': typeof lotteryId,
        'Index': index,
        'Checkbox ID': checkbox ? checkbox.id : 'CHECKBOX IS NULL',
        'Checkbox Value': checkbox ? checkbox.value : 'CHECKBOX IS NULL'
    });

    // BULLETPROOF: Validate all required parameters
    if (!checkbox) {
        console.error('❌ CRITICAL ERROR: Checkbox element is null or undefined');
        return false;
    }

    if (!price || isNaN(price)) {
        console.error('❌ CRITICAL ERROR: Invalid price parameter:', price);
        return false;
    }

    if (!lotteryId || isNaN(lotteryId)) {
        console.error('❌ CRITICAL ERROR: Invalid lotteryId parameter:', lotteryId);
        return false;
    }

    // BULLETPROOF: Parse and validate parameters
    price = parseInt(price);
    index = parseInt(index);
    lotteryId = parseInt(lotteryId);

    const lotteryType = checkbox.getAttribute("data-lname");
    if (!lotteryType) {
        console.error('❌ CRITICAL ERROR: Lottery type not found in checkbox data-lname attribute');
        return false;
    }

    console.log('✅ PARAMETER VALIDATION PASSED');

    // BULLETPROOF: Get current ticket count from dropdown with multiple fallback strategies
    let ticketDropdown = null;
    let no_of_tickets = 1; // Default fallback

    // Try multiple selectors to find the ticket dropdown
    const dropdownSelectors = [
        `[name="tickets_no${lotteryId}"]`,
        `select[name="tickets_no${lotteryId}"]`,
        `.${lotteryId} select`,
        `#tickets_no${lotteryId}`,
        `select[data-lottery-id="${lotteryId}"]`
    ];

    for (let selector of dropdownSelectors) {
        ticketDropdown = document.querySelector(selector);
        if (ticketDropdown) {
            console.log('✅ FOUND TICKET DROPDOWN WITH SELECTOR:', selector);
            break;
        }
    }

    if (ticketDropdown && ticketDropdown.value) {
        no_of_tickets = parseInt(ticketDropdown.value) || 1;
        console.log('✅ TICKET COUNT FROM DROPDOWN:', no_of_tickets);
    } else {
        console.warn('⚠️ TICKET DROPDOWN NOT FOUND, USING DEFAULT:', no_of_tickets);
    }

    console.log('🎫 TICKET INFORMATION:', {
        'Dropdown Found': !!ticketDropdown,
        'Dropdown Value': ticketDropdown ? ticketDropdown.value : 'NOT FOUND',
        'Parsed Tickets': no_of_tickets,
        'Price Per Ticket': price,
        'Total Lottery Cost': price * no_of_tickets,
        'Registration Fee Per Ticket': registration_price,
        'Total Registration Fee': registration_price * no_of_tickets
    });

    // Payment section display is handled by showTotal function

    if (checkbox.checked == true) {
        console.log('✅ CHECKBOX CHECKED - Adding lottery:', lotteryType);
        console.log('🎯 CHECKBOX IS CHECKED! About to show payment section...');

        try {
            console.log('🔧 STEP 1: Setting required fields...');
            // BULLETPROOF: Set required fields for ticket forms with error handling
            for(let ticketIndex=1; ticketIndex <= no_of_tickets; ticketIndex++) {
                try {
                    // Escape CSS selector to handle special characters like hyphens
                    const escapedLotteryId = CSS.escape(lotteryId);
                    const selector = `.${escapedLotteryId}.t_${ticketIndex} input.req`;
                    console.log('🔍 USING SELECTOR:', selector);
                    const ticketInputs = document.querySelectorAll(selector);
                    console.log('🔍 FOUND TICKET INPUTS:', ticketInputs.length);
                    ticketInputs.forEach(input => {
                        if (input) input.required = true;
                    });
                } catch (error) {
                    console.error('❌ ERROR IN SETTING REQUIRED FIELDS:', error);
                    // Fallback: try alternative selector approach
                    const ticketInputs = document.querySelectorAll(`[class*="${lotteryId}"][class*="t_${ticketIndex}"] input.req`);
                    ticketInputs.forEach(input => {
                        if (input) input.required = true;
                    });
                }
            }
            console.log('✅ STEP 1 COMPLETE: Required fields set');
            console.log('✅ REQUIRED FIELDS SET FOR TICKET FORMS');

            console.log('🔧 STEP 2: Checking existing entries...');
            // BULLETPROOF: Remove existing entry if it exists (for re-checking)
            if(added_lotteries[lotteryType]) {
                console.log('🔄 REMOVING EXISTING ENTRY FOR:', lotteryType);
                try {
                    removeRowForTotal(lotteryType);
                    lotteryTotal -= added_lotteries[lotteryType].price*added_lotteries[lotteryType].no_of_tickets;
                    subscribePrice -= added_lotteries[lotteryType].no_of_tickets * registration_price;
                    console.log('✅ EXISTING ENTRY REMOVED SUCCESSFULLY');
                } catch (error) {
                    console.error('❌ ERROR REMOVING EXISTING ENTRY:', error);
                    console.log('❌ ERROR in step 2: ' + error.message);
                }
            }
            console.log('✅ STEP 2 COMPLETE: Existing entries handled');

            // BULLETPROOF: Create payment row immediately with error handling
            try {
                createRowForTotal(lotteryType, price*no_of_tickets, lotteryId, index);
                console.log('✅ PAYMENT ROW CREATED SUCCESSFULLY');
            } catch (error) {
                console.error('❌ ERROR CREATING PAYMENT ROW:', error);
                // Continue execution even if row creation fails
            }

            // BULLETPROOF: Update global lottery tracking
            added_lotteries[lotteryType] = {
                price : price,
                no_of_tickets: no_of_tickets,
                lotteryId: lotteryId,
                checkbox: checkbox,
                index: index
            };

            // BULLETPROOF: Update totals with validation
            const previousLotteryTotal = lotteryTotal;
            const previousSubscribePrice = subscribePrice;

            lotteryTotal = lotteryTotal + price*no_of_tickets;
            subscribePrice += no_of_tickets * registration_price;

            console.log('💰 UPDATED TOTALS:', {
                'Previous Lottery Total': previousLotteryTotal,
                'New Lottery Total': lotteryTotal,
                'Previous Subscribe Price': previousSubscribePrice,
                'New Subscribe Price': subscribePrice,
                'Added Amount': price*no_of_tickets,
                'Added Registration': no_of_tickets * registration_price
            });

            // IMMEDIATE PAYMENT SECTION DISPLAY - Force show with aggressive CSS overrides
            const paymentSection = document.getElementById('paySide');
            console.log('🔍 PAYMENT SECTION FOUND: ' + (paymentSection ? 'YES' : 'NO'));

            if (paymentSection) {
                console.log('🔍 PAYMENT SECTION BEFORE CHANGES:', {
                    'Current Style': paymentSection.getAttribute('style'),
                    'Computed Display': window.getComputedStyle(paymentSection).display,
                    'Computed Visibility': window.getComputedStyle(paymentSection).visibility
                });

                // AGGRESSIVE APPROACH: Remove ALL inline styles first
                paymentSection.removeAttribute('style');

                // Force immediate display with maximum specificity
                paymentSection.style.setProperty('display', 'block', 'important');
                paymentSection.style.setProperty('visibility', 'visible', 'important');
                paymentSection.style.setProperty('opacity', '1', 'important');
                paymentSection.style.setProperty('height', 'auto', 'important');
                paymentSection.style.setProperty('width', 'auto', 'important');

                // Additional backup method
                paymentSection.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; width: auto !important;';

                // Force immediate DOM update
                paymentSection.offsetHeight; // Trigger reflow

                // Force class-based approach as backup
                paymentSection.classList.remove('d-none', 'hidden');
                paymentSection.classList.add('d-block');

                console.log('💰 PAYMENT SECTION SHOULD NOW BE VISIBLE!');
                console.log('🔍 PAYMENT SECTION AFTER CHANGES:', {
                    'New Style': paymentSection.getAttribute('style'),
                    'Computed Display': window.getComputedStyle(paymentSection).display,
                    'Computed Visibility': window.getComputedStyle(paymentSection).visibility
                });

                console.log('🚀 PAYMENT SECTION FORCED TO SHOW WITH AGGRESSIVE CSS');
                console.log('📊 PAYMENT SECTION STATE:', {
                    'Element Found': !!paymentSection,
                    'Display Style': paymentSection.style.display,
                    'Visibility Style': paymentSection.style.visibility,
                    'Computed Display': window.getComputedStyle(paymentSection).display,
                    'Computed Visibility': window.getComputedStyle(paymentSection).visibility
                });
            } else {
                console.error('❌ PAYMENT SECTION #paySide NOT FOUND!');
            }

            // REMOVED: Direct innerHTML manipulation that was overwriting multiple lottery rows
            // Now ALL lottery rows go through createRowForTotal function for consistency
            console.log('✅ SKIPPING DIRECT INVOICE DETAILS MANIPULATION - Using createRowForTotal instead');

            // BULLETPROOF: Show payment section with comprehensive error handling
            try {
                showTotal(lotteryTotal, subscribePrice);
                console.log('✅ PAYMENT SECTION UPDATED SUCCESSFULLY');
            } catch (error) {
                console.error('❌ ERROR UPDATING PAYMENT SECTION:', error);
                // Ensure payment section stays visible even if showTotal fails
                if (paymentSection) {
                    paymentSection.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';
                    console.log('✅ PAYMENT SECTION KEPT VISIBLE AS FALLBACK');
                }
            }

        } catch (error) {
            console.error('❌ CRITICAL ERROR IN CHECKBOX CHECKED LOGIC:', error);
            // Ensure payment section is still shown even if there are errors
            const paymentSection = document.getElementById('paySide');
            if (paymentSection) {
                paymentSection.style.display = 'block';
                paymentSection.style.visibility = 'visible';
            }
        }

    } else if (checkbox.checked == false) {
        console.log('❌ CHECKBOX UNCHECKED - Removing lottery:', lotteryType);

        try {
            // BULLETPROOF: Remove required fields for ticket forms with error handling
            for(let ticketIndex=1; ticketIndex <= no_of_tickets; ticketIndex++) {
                try {
                    // Escape CSS selector to handle special characters like hyphens
                    const escapedLotteryId = CSS.escape(lotteryId);
                    const selector = `.${escapedLotteryId}.t_${ticketIndex} input.req`;
                    const ticketInputs = document.querySelectorAll(selector);
                    ticketInputs.forEach(input => {
                        if (input) input.required = false;
                    });
                } catch (error) {
                    console.error('❌ ERROR IN REMOVING REQUIRED FIELDS:', error);
                    // Fallback: try alternative selector approach
                    const ticketInputs = document.querySelectorAll(`[class*="${lotteryId}"][class*="t_${ticketIndex}"] input.req`);
                    ticketInputs.forEach(input => {
                        if (input) input.required = false;
                    });
                }
            }
            console.log('✅ REQUIRED FIELDS REMOVED FROM TICKET FORMS');

            // BULLETPROOF: Remove lottery from calculations with validation
            if(added_lotteries[lotteryType]) {
                console.log('🔄 REMOVING LOTTERY FROM CALCULATIONS:', lotteryType);

                const previousLotteryTotal = lotteryTotal;
                const previousSubscribePrice = subscribePrice;

                try {
                    removeRowForTotal(lotteryType);
                    console.log('✅ PAYMENT ROW REMOVED SUCCESSFULLY');
                } catch (error) {
                    console.error('❌ ERROR REMOVING PAYMENT ROW:', error);
                }

                // BULLETPROOF: Update totals with validation
                const removedLotteryAmount = added_lotteries[lotteryType].price * added_lotteries[lotteryType].no_of_tickets;
                const removedRegistrationAmount = added_lotteries[lotteryType].no_of_tickets * registration_price;

                lotteryTotal -= removedLotteryAmount;
                subscribePrice -= removedRegistrationAmount;

                // BULLETPROOF: Ensure totals don't go negative
                if (lotteryTotal < 0) {
                    console.warn('⚠️ LOTTERY TOTAL WENT NEGATIVE, RESETTING TO 0');
                    lotteryTotal = 0;
                }
                if (subscribePrice < 0) {
                    console.warn('⚠️ SUBSCRIBE PRICE WENT NEGATIVE, RESETTING TO 0');
                    subscribePrice = 0;
                }

                console.log('💰 UPDATED TOTALS AFTER REMOVAL:', {
                    'Previous Lottery Total': previousLotteryTotal,
                    'New Lottery Total': lotteryTotal,
                    'Previous Subscribe Price': previousSubscribePrice,
                    'New Subscribe Price': subscribePrice,
                    'Removed Lottery Amount': removedLotteryAmount,
                    'Removed Registration Amount': removedRegistrationAmount
                });

                // BULLETPROOF: Remove from global tracking
                delete added_lotteries[lotteryType];
                console.log('✅ LOTTERY REMOVED FROM GLOBAL TRACKING');
            } else {
                console.warn('⚠️ LOTTERY NOT FOUND IN added_lotteries:', lotteryType);
            }

            console.log('📊 REMAINING LOTTERIES:', Object.keys(added_lotteries));

            // BULLETPROOF: Update totals with comprehensive error handling
            try {
                showTotal(lotteryTotal, subscribePrice);
                console.log('✅ PAYMENT SECTION UPDATED AFTER REMOVAL');
            } catch (error) {
                console.error('❌ ERROR UPDATING PAYMENT SECTION AFTER REMOVAL:', error);
                // Fallback: Hide payment section if no lotteries remain
                if (Object.keys(added_lotteries).length === 0) {
                    const paymentSection = document.getElementById('paySide');
                    if (paymentSection) {
                        paymentSection.style.display = 'none';
                        paymentSection.style.visibility = 'hidden';
                        console.log('✅ PAYMENT SECTION HIDDEN AS FALLBACK');
                    }
                }
            }

        } catch (error) {
            console.error('❌ CRITICAL ERROR IN CHECKBOX UNCHECKED LOGIC:', error);
        }
    }

    console.log('🔄 === BULLETPROOF HANDLE CHANGE COMPLETED ===\n');
    return true;
}


function createRowForTotal(lotteryType, price, lotteryId, index) {
    console.log('🏗️ === CREATE ROW FOR TOTAL ===');
    console.log('📊 PARAMETERS:', {
        'LotteryType': lotteryType,
        'Price': price,
        'LotteryId': lotteryId,
        'Index': index
    });

    // CRITICAL: Check if lotteryInvoiceDetails exists
    const invoiceDetails = document.getElementById("lotteryInvoiceDetails");
    if (!invoiceDetails) {
        console.error('❌ CRITICAL ERROR: lotteryInvoiceDetails element not found!');
        return;
    }

    clickCounter = clickCounter + 1;
    if (lotteryType && price) {
        const lotteryTypeClass = lotteryType.replace(/ /ig, "-").replace(/[^a-zA-Z0-9-]/g, "");

        console.log('🎯 CLASS GENERATION:', {
            'Original Name': lotteryType,
            'Generated Class': lotteryTypeClass,
            'CSS Selector': `.${lotteryTypeClass}`
        });

        // Check if row already exists and update it instead of adding duplicate
        const existingRow = document.querySelector(`.${lotteryTypeClass}`);
        console.log('🔍 EXISTING ROW CHECK:', {
            'Selector': `.${lotteryTypeClass}`,
            'Found': !!existingRow
        });

        if (existingRow) {
            console.log('🔄 UPDATING EXISTING ROW FOR:', lotteryType);
            // Update the existing row's price
            const priceInput = existingRow.querySelector('input[readonly]');
            if (priceInput) {
                console.log('💰 UPDATING EXISTING ROW PRICE:', priceInput.value, '→', price);
                priceInput.value = price;
                priceInput.placeholder = price;
            }
            clickCounter = clickCounter - 1; // Don't increment counter for updates
        } else {
            console.log('🆕 CREATING NEW ROW FOR:', lotteryType, 'WITH CLASS:', lotteryTypeClass);

            const newRowHTML = `
            <div class="row p-3 border-bottom ${lotteryTypeClass}" data-lottery-type="${lotteryType}" data-lottery-id="${lotteryId}">
                <div class="col-sm-6 text-center p-3">
                    <span>${lotteryType}($)</span>
                    <input type="hidden" class="form-control" name="lotteryType" value="${lotteryType}"/>
                    <input type="hidden" class="form-control" name="lotteryId" value="${lotteryId}"/>
                    <input type="hidden" class="form-control" name="rownum" value="${index}"/>
                </div>
                <div class="col-sm-6 p-3">
                    <input class="form-control" name="lotteryprice[]" type="text" value="${price}" placeholder="${price}" readonly>
                </div>
            </div>
            `;

            console.log('📝 NEW ROW HTML:', newRowHTML);

            // CRITICAL: Use appendChild instead of innerHTML += to avoid conflicts
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newRowHTML;
            const newRow = tempDiv.firstElementChild;

            invoiceDetails.appendChild(newRow);
            console.log('✅ ROW APPENDED TO INVOICE DETAILS');

            // Verify the row was created
            const verifyRow = document.querySelector(`.${lotteryTypeClass}`);
            console.log('✅ ROW CREATION VERIFIED:', {
                'Class': lotteryTypeClass,
                'Found After Creation': !!verifyRow,
                'Row HTML': verifyRow ? verifyRow.outerHTML : 'NOT FOUND',
                'Invoice Details Content': invoiceDetails.innerHTML
            });
        }
    } else {
        console.error('❌ INVALID PARAMETERS:', {
            'LotteryType': lotteryType,
            'Price': price
        });
    }

    console.log('🏗️ === CREATE ROW COMPLETED ===\n');
}

function removeRowForTotal(lotteryType) {
    clickCounter = clickCounter - 1;

    const lotteryTypeClass = lotteryType.replace(/ /ig, "-");
    if (lotteryTypeClass) {
        const childNodes = document.getElementsByClassName(lotteryTypeClass);
        if (childNodes && childNodes.length > 0) {
            childNodes[0].remove();
        }
    }

    // Only remove total sections if no lotteries are selected
    if (clickCounter === 0) {
        // Safely remove total sections
        const totalElements = document.getElementsByClassName("lotteryTotal");
        if (totalElements && totalElements.length > 0) {
            totalElements[0].remove();
        }

        const total1Elements = document.getElementsByClassName("lotteryTotal1");
        if (total1Elements && total1Elements.length > 0) {
            total1Elements[0].remove();
        }

        const total2Elements = document.getElementsByClassName("lotteryTotal2");
        if (total2Elements && total2Elements.length > 0) {
            total2Elements[0].remove();
        }
    }
}

function showTotal(lotteryTotal, subscribePrice) {
    console.log('💰 === SHOW TOTAL FUNCTION CALLED ===');
    console.log('📊 PAYMENT TOTALS:', {
        'Lottery Total': lotteryTotal,
        'Registration/Subscribe Price': subscribePrice,
        'Grand Total': lotteryTotal + subscribePrice
    });

    const paymentSection = document.getElementById('paySide');
    const invoiceTotal = document.getElementById("lotteryInvoiceTotal");

    console.log('🎯 DOM ELEMENTS:', {
        'Payment Section Found': !!paymentSection,
        'Invoice Total Found': !!invoiceTotal
    });

    if (lotteryTotal > 0) {
        console.log('SHOWING PAYMENT SECTION - Total > 0');

        // INSTANT PAYMENT SECTION DISPLAY - Force immediate visibility with aggressive approach
        if (paymentSection) {
            console.log('🔍 SHOW TOTAL - PAYMENT SECTION BEFORE:', {
                'Current Style': paymentSection.getAttribute('style'),
                'Computed Display': window.getComputedStyle(paymentSection).display
            });

            // AGGRESSIVE APPROACH: Remove ALL inline styles first
            paymentSection.removeAttribute('style');

            // Force immediate display with maximum specificity
            paymentSection.style.setProperty('display', 'block', 'important');
            paymentSection.style.setProperty('visibility', 'visible', 'important');
            paymentSection.style.setProperty('opacity', '1', 'important');

            // Additional backup method
            paymentSection.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';

            // Force class-based approach as backup
            paymentSection.classList.remove('d-none', 'hidden');
            paymentSection.classList.add('d-block');

            // Force immediate DOM update
            paymentSection.offsetHeight; // Trigger reflow

            console.log('🔍 SHOW TOTAL - PAYMENT SECTION AFTER:', {
                'New Style': paymentSection.getAttribute('style'),
                'Computed Display': window.getComputedStyle(paymentSection).display
            });

            forceInstantUpdate(); // Force immediate visual update
        }

        // CRITICAL FIX: Update ALL lottery cost rows first
        console.log('🔄 UPDATING ALL LOTTERY COST ROWS...');
        console.log('📊 CURRENT ADDED LOTTERIES:', added_lotteries);

        // ENHANCED: Try multiple strategies to find and update lottery cost inputs
        Object.keys(added_lotteries).forEach(lotteryType => {
            const lottery = added_lotteries[lotteryType];
            const newLotteryCost = lottery.price * lottery.no_of_tickets;

            console.log('🔍 PROCESSING LOTTERY TYPE:', {
                'Original Name': lotteryType,
                'Lottery Data': lottery,
                'New Cost': newLotteryCost
            });

            // Strategy 1: Try to find by lottery row finder
            const rowResult = findLotteryRow(lotteryType);
            let updated = false;

            if (rowResult) {
                const { row: lotteryRow, method, identifier } = rowResult;
                const priceInput = lotteryRow.querySelector('input[readonly]');

                if (priceInput) {
                    console.log('🎯 UPDATING LOTTERY ROW (Strategy 1):', {
                        'Lottery Type': lotteryType,
                        'Found Method': method,
                        'Old Value': priceInput.value,
                        'New Value': newLotteryCost
                    });

                    priceInput.value = newLotteryCost;
                    priceInput.placeholder = newLotteryCost;
                    priceInput.dispatchEvent(new Event('change'));
                    updated = true;
                    console.log('✅ LOTTERY ROW UPDATED SUCCESSFULLY (Strategy 1)');
                }
            }

            // Strategy 2: Direct search for lottery price inputs
            if (!updated) {
                console.log('🔄 TRYING STRATEGY 2: Direct input search...');
                const allPriceInputs = document.querySelectorAll('#lotteryInvoiceDetails input[readonly]');

                allPriceInputs.forEach((input, index) => {
                    const parentRow = input.closest('.row');
                    if (parentRow) {
                        const rowText = parentRow.textContent || parentRow.innerText;
                        console.log(`🔍 Checking input ${index}:`, rowText.trim());

                        if (rowText.toLowerCase().includes(lotteryType.toLowerCase())) {
                            console.log('🎯 FOUND MATCHING INPUT (Strategy 2):', {
                                'Row Text': rowText.trim(),
                                'Old Value': input.value,
                                'New Value': newLotteryCost
                            });

                            input.value = newLotteryCost;
                            input.placeholder = newLotteryCost;
                            input.dispatchEvent(new Event('change'));
                            updated = true;
                            console.log('✅ LOTTERY ROW UPDATED SUCCESSFULLY (Strategy 2)');
                        }
                    }
                });
            }

            // Strategy 3: Update by ID if it exists
            if (!updated) {
                console.log('🔄 TRYING STRATEGY 3: ID-based search...');
                const priceInputById = document.getElementById('lotteryprice');
                if (priceInputById) {
                    console.log('🎯 UPDATING BY ID (Strategy 3):', {
                        'Old Value': priceInputById.value,
                        'New Value': newLotteryCost
                    });

                    priceInputById.value = newLotteryCost;
                    priceInputById.placeholder = newLotteryCost;
                    priceInputById.dispatchEvent(new Event('change'));
                    updated = true;
                    console.log('✅ LOTTERY ROW UPDATED SUCCESSFULLY (Strategy 3)');
                }
            }

            // Strategy 4: BRUTE FORCE - Update ALL readonly inputs in invoice details
            if (!updated) {
                console.log('🔄 TRYING STRATEGY 4: BRUTE FORCE UPDATE...');
                const allReadonlyInputs = document.querySelectorAll('#lotteryInvoiceDetails input[readonly]');
                console.log('🔍 FOUND', allReadonlyInputs.length, 'READONLY INPUTS');

                if (allReadonlyInputs.length === 1) {
                    // If there's only one lottery selected, update the single input
                    const input = allReadonlyInputs[0];
                    console.log('🎯 UPDATING SINGLE LOTTERY INPUT (Strategy 4):', {
                        'Old Value': input.value,
                        'New Value': newLotteryCost
                    });

                    input.value = newLotteryCost;
                    input.placeholder = newLotteryCost;
                    input.dispatchEvent(new Event('change'));
                    updated = true;
                    console.log('✅ LOTTERY ROW UPDATED SUCCESSFULLY (Strategy 4)');
                }
            }

            if (!updated) {
                console.log('❌ COULD NOT UPDATE LOTTERY COST FOR:', lotteryType);
                console.log('🔍 AVAILABLE INPUTS IN #lotteryInvoiceDetails:');
                const allInputs = document.querySelectorAll('#lotteryInvoiceDetails input');
                allInputs.forEach((input, index) => {
                    console.log(`   Input ${index}:`, input.id, input.name, input.value);
                });
            }
        });

        // INSTANT TOTAL UPDATES - Check if total section already exists
        const existingTotal = document.querySelector('.lotteryTotal');
        console.log('🔍 TOTAL SECTION CHECK:', {
            'Existing Total Found': !!existingTotal,
            'Expected Registration Fee': subscribePrice,
            'Expected Grand Total': lotteryTotal + subscribePrice
        });

        if (existingTotal) {
            console.log('UPDATING EXISTING TOTAL SECTION');
            console.log('NEW VALUES:', 'Registration Fee:', subscribePrice, 'Total:', lotteryTotal + subscribePrice);

            // Update existing values INSTANTLY without recreating HTML
            const regFeeInput = existingTotal.querySelector('input[readonly]');
            const totalInput = document.getElementById('lotterypricetotal');

            console.log('🔍 TOTAL SECTION INPUTS:', {
                'Registration Fee Input Found': !!regFeeInput,
                'Registration Fee Current Value': regFeeInput ? regFeeInput.value : 'NOT FOUND',
                'Total Input Found': !!totalInput,
                'Total Current Value': totalInput ? totalInput.value : 'NOT FOUND'
            });

            // Force immediate value updates
            if (regFeeInput) {
                console.log('UPDATING REGISTRATION FEE FROM', regFeeInput.value, 'TO', subscribePrice);
                regFeeInput.value = subscribePrice;
                regFeeInput.placeholder = subscribePrice;
                regFeeInput.dispatchEvent(new Event('change'));
                console.log('✅ REGISTRATION FEE UPDATED');
            } else {
                console.log('❌ REGISTRATION FEE INPUT NOT FOUND');
            }

            if (totalInput) {
                const newTotal = lotteryTotal + subscribePrice;
                console.log('UPDATING TOTAL FROM', totalInput.value, 'TO', newTotal);
                totalInput.value = newTotal;
                totalInput.placeholder = newTotal;
                totalInput.dispatchEvent(new Event('change'));
                console.log('✅ TOTAL INPUT UPDATED');
            } else {
                console.log('❌ TOTAL INPUT NOT FOUND');
            }
        } else {
            console.log('CREATING NEW TOTAL SECTION');
            // Create new total section INSTANTLY
            if (invoiceTotal) {
                invoiceTotal.innerHTML = `
                    <div class="row p-3 border-top border-bottom lotteryTotal">
                        <div class="col-sm-6 text-center p-3">
                            <span class="">
                                Registration Fee($)
                            </span>
                        </div>
                        <div class="col-sm-6 p-3">
                            <input class="form-control" type="text" value="${subscribePrice}" placeholder="${subscribePrice}" readonly>
                        </div>
                        <div class="col-sm-6 text-center">
                            <b>
                                Total($)
                            </b>
                        </div>
                        <div class="col-sm-6">
                            <input class="form-control" id="lotterypricetotal" name="lotterypricetotal" type="text" value="${lotteryTotal + subscribePrice}" placeholder="${lotteryTotal + subscribePrice}" readonly>
                        </div>
                    </div>
                    <div class="row lotteryTotal1">
                        <div class="col-sm-2">
                            <input id = "ageConfirmBox" name="ageconfirm" type="checkbox" />
                        </div>
                        <div class="row col-sm-10">
                            I am 19 years of age or older and I am currently in the province of Ontario.
                        </div>
                        <div class="p-3 col-sm-12 border-top">
                            <font style="font-size:20px;color:#35c2f9;font-weight:bold">Mark Age Group</font><br/>
                            <label><input type="radio" name="r1"> 19-35</label><br/>
                            <label><input type="radio" name="r1"> 36-355</label><br/>
                            <label><input type="radio" name="r1"> 56-75</label><br/>
                            <label><input type="radio" name="r1"> 76-100</label>
                        </div>
                    </div>
                `;
            }
        }
    } else {
        console.log('HIDING PAYMENT SECTION - Total = 0');
        // Hide payment section INSTANTLY and clear content
        if (paymentSection) {
            paymentSection.style.display = 'none';
            paymentSection.style.visibility = 'hidden';
            paymentSection.style.opacity = '0';
        }

        // Clear invoice details and totals
        if (invoiceTotal) {
            invoiceTotal.innerHTML = '';
        }

        const invoiceDetails = document.getElementById("lotteryInvoiceDetails");
        if (invoiceDetails) {
            invoiceDetails.innerHTML = '';
        }
    }
}

function myReview() {

    if (document.getElementById('ageConfirmBox').checked == true) {
        
        document.getElementById('dsphide').style.display = "block";
        

    } else if (document.getElementById('ageConfirmBox').checked == false) {
        alert("Confirm I am 19 years of age or older and I am currently in the province of Ontario.");
        document.getElementById('dsphide').style.display = "none";
    }
    
}

function myRegistration() {

    console.log('Registration called');
    

    location.replace("./registration");
}



function choosenumbersyesnoCheck(radio) {


    if (radio.checked == true) {
        
        radio.parentNode.parentNode.style.height = 'auto';

        $(radio).next().next().css('visibility', 'visible');
        $(radio).next().next().show();

        $(radio).next().next().children().prop('required', true);
    } else {
        document.getElementsByName(ifyes)[0].style.visibility = 'hidden';
        $(radio).next().next().children().prop('required', false);

    }
}

function quickpickyesnoCheck(radio) {


    if (radio.checked == true) {

        radio.parentNode.parentNode.style.height = 'auto';

        radio.parentNode.nextElementSibling.children[2].style.visibility = 'hidden';
        radio.parentNode.nextElementSibling.children[2].style.display = 'none';

        $(radio.parentNode.nextElementSibling.children[2]).children().prop('required', false);
    } else {
        document.getElementsByName(ifyes)[0].style.visibility = 'visible';
        document.getElementsByName(ifyes)[0].style.display = 'block';

    }
}

function lyesnoCheck() {
    if (document.getElementById('lyesCheck').checked) {
        document.getElementById('lifYes').style.visibility = 'visible';
    } else document.getElementById('lifYes').style.visibility = 'hidden';
}

function getRndNum(min, max) {
    return [Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min];
}

function getRndNum1(min, max) {
    return [Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min, Math.floor(Math.random() * (max - min)) + min];
}


// NEW: Universal lottery row finder function
function findLotteryRow(lotteryType) {
    console.log('🔍 === UNIVERSAL LOTTERY ROW FINDER ===');
    console.log('🎯 SEARCHING FOR:', lotteryType);

    // Strategy 1: Try standard class-based search with SAFE CSS selectors
    const classStrategies = [
        lotteryType.replace(/ /ig, "-"),                    // Standard: spaces to dashes
        lotteryType.replace(/[^a-zA-Z0-9]/g, "-"),         // All special chars to dashes
        lotteryType.replace(/[^a-zA-Z0-9]/g, ""),          // Remove all special chars
        lotteryType.toLowerCase().replace(/ /ig, "-"),      // Lowercase with dashes
        lotteryType.toLowerCase().replace(/[^a-zA-Z0-9]/g, "-"), // Lowercase all special to dashes
        lotteryType.toLowerCase().replace(/[^a-zA-Z0-9]/g, "")   // Lowercase remove all special
    ];

    for (let testClass of classStrategies) {
        // SAFETY CHECK: Ensure valid CSS class selector
        if (testClass && testClass.length > 0 && /^[a-zA-Z]/.test(testClass)) {
            try {
                const row = document.querySelector(`.${testClass}`);
                if (row) {
                    console.log('✅ FOUND BY CLASS:', testClass);
                    return { row, method: 'class', identifier: testClass };
                }
            } catch (error) {
                console.log('⚠️ INVALID CSS SELECTOR:', testClass, error.message);
            }
        } else {
            console.log('⚠️ SKIPPING INVALID CLASS:', testClass);
        }
    }

    // Strategy 2: Search by data attributes
    const dataAttrRow = document.querySelector(`[data-lottery-type="${lotteryType}"]`);
    if (dataAttrRow) {
        console.log('✅ FOUND BY DATA ATTRIBUTE');
        return { row: dataAttrRow, method: 'data-attribute', identifier: lotteryType };
    }

    // Strategy 3: Search by text content
    const allRows = document.querySelectorAll('#lotteryInvoiceDetails .row');
    for (let row of allRows) {
        const textContent = row.textContent || row.innerText;
        if (textContent.toLowerCase().includes(lotteryType.toLowerCase())) {
            console.log('✅ FOUND BY TEXT CONTENT:', textContent.trim());
            return { row, method: 'text-content', identifier: textContent.trim() };
        }
    }

    // Strategy 4: Search by hidden input value
    const hiddenInputs = document.querySelectorAll('input[name="lotteryType"]');
    for (let input of hiddenInputs) {
        if (input.value === lotteryType) {
            const row = input.closest('.row');
            if (row) {
                console.log('✅ FOUND BY HIDDEN INPUT VALUE');
                return { row, method: 'hidden-input', identifier: lotteryType };
            }
        }
    }

    console.log('❌ LOTTERY ROW NOT FOUND FOR:', lotteryType);
    console.log('🔍 AVAILABLE ROWS:');
    allRows.forEach((row, index) => {
        console.log(`   Row ${index}:`, row.className, '|', (row.textContent || row.innerText).trim());
    });

    return null;
}

// NEW: Real-time payment update function for ticket quantity changes
function updatePaymentForTicketChange(lotteryId, newTicketCount) {
    console.log('💰 === REAL-TIME PAYMENT UPDATE FUNCTION ===');
    console.log('📊 PARAMETERS:', {
        'LotteryId': lotteryId,
        'New Ticket Count': newTicketCount
    });

    const checkbox = document.getElementById('main_checkbox_' + lotteryId);
    console.log('🔍 CHECKBOX SEARCH:', {
        'Looking for ID': 'main_checkbox_' + lotteryId,
        'Found checkbox': !!checkbox,
        'Checkbox checked': checkbox ? checkbox.checked : 'N/A',
        'Checkbox value': checkbox ? checkbox.value : 'N/A',
        'Checkbox data-lname': checkbox ? checkbox.getAttribute("data-lname") : 'N/A'
    });

    if (!checkbox || !checkbox.checked) {
        console.log('❌ LOTTERY NOT SELECTED - NO UPDATE NEEDED');
        return;
    }

    const lotteryType = checkbox.getAttribute("data-lname");
    console.log('🎫 UPDATING PAYMENT FOR:', lotteryType);

    console.log('🔍 CURRENT added_lotteries STATE:', added_lotteries);
    console.log('🔍 CURRENT GLOBAL TOTALS BEFORE UPDATE:', {
        'lotteryTotal': lotteryTotal,
        'subscribePrice': subscribePrice,
        'registration_price': registration_price
    });

    // Get the current lottery data from added_lotteries
    if (!added_lotteries[lotteryType]) {
        console.log('❌ LOTTERY NOT FOUND IN added_lotteries - TRYING TO EXTRACT PRICE FROM CHECKBOX');
        console.log('🔍 AVAILABLE KEYS IN added_lotteries:', Object.keys(added_lotteries));

        // Fallback: Extract price from checkbox value
        const checkboxValue = checkbox.value;
        const parts = checkboxValue.split('_');
        console.log('🔧 CHECKBOX VALUE BREAKDOWN:', {
            'Full Value': checkboxValue,
            'Split Parts': parts,
            'Parts Length': parts.length
        });

        if (parts.length >= 3) {
            const pricePerTicket = parseFloat(parts[2]);
            console.log('🔧 EXTRACTED PRICE FROM CHECKBOX:', pricePerTicket);

            // Calculate totals directly
            const newLotteryCost = pricePerTicket * newTicketCount;
            const newRegistrationCost = newTicketCount * registration_price;

            console.log('🔧 DIRECT CALCULATION BREAKDOWN:', {
                'Price Per Ticket': pricePerTicket,
                'New Ticket Count': newTicketCount,
                'New Lottery Cost': newLotteryCost,
                'Registration Price Per Ticket': registration_price,
                'New Registration Cost': newRegistrationCost
            });

            // Update global totals (assuming this is the only lottery selected)
            lotteryTotal = newLotteryCost;
            subscribePrice = newRegistrationCost;

            console.log('🔧 DIRECT CALCULATION TOTALS:', {
                'Lottery Cost': newLotteryCost,
                'Registration Cost': newRegistrationCost,
                'Grand Total': lotteryTotal + subscribePrice
            });

            // Force immediate visual update
            showTotal(lotteryTotal, subscribePrice);
            console.log('✅ REAL-TIME PAYMENT UPDATE COMPLETED (FALLBACK)');
            return;
        } else {
            console.log('❌ COULD NOT EXTRACT PRICE FROM CHECKBOX VALUE:', checkboxValue);
            return;
        }
    }

    const currentLottery = added_lotteries[lotteryType];
    const pricePerTicket = currentLottery.price;
    const oldTicketCount = currentLottery.no_of_tickets;

    console.log('🔄 PAYMENT UPDATE DETAILS:', {
        'Lottery Type': lotteryType,
        'Price Per Ticket': pricePerTicket,
        'Old Ticket Count': oldTicketCount,
        'New Ticket Count': newTicketCount,
        'Old Total': pricePerTicket * oldTicketCount,
        'New Total': pricePerTicket * newTicketCount
    });

    // Update the global totals by removing old values and adding new ones
    const oldLotteryCost = pricePerTicket * oldTicketCount;
    const newLotteryCost = pricePerTicket * newTicketCount;
    const oldRegistrationCost = oldTicketCount * registration_price;
    const newRegistrationCost = newTicketCount * registration_price;

    // Update global totals
    lotteryTotal = lotteryTotal - oldLotteryCost + newLotteryCost;
    subscribePrice = subscribePrice - oldRegistrationCost + newRegistrationCost;

    // Update the added_lotteries object with new ticket count
    added_lotteries[lotteryType].no_of_tickets = newTicketCount;

    console.log('📊 UPDATED GLOBAL TOTALS:', {
        'Old Lottery Total': lotteryTotal - newLotteryCost + oldLotteryCost,
        'New Lottery Total': lotteryTotal,
        'Old Subscribe Price': subscribePrice - newRegistrationCost + oldRegistrationCost,
        'New Subscribe Price': subscribePrice,
        'Grand Total': lotteryTotal + subscribePrice
    });

    // Force immediate visual update
    showTotal(lotteryTotal, subscribePrice);

    console.log('✅ REAL-TIME PAYMENT UPDATE COMPLETED');
}

function changeTicketNo(selectE, Lid) {
    console.log('🎯 === CHANGE TICKET NO FUNCTION CALLED ===');
    console.log('📊 PARAMETERS:', {
        'LotteryId': Lid,
        'New Quantity': selectE.value,
        'Select Element': selectE,
        'Select Name': selectE.name
    });

    // Verify the dropdown element and its value
    const dropdownName = 'tickets_no' + Lid;
    const dropdownElement = document.getElementsByName(dropdownName)[0];
    console.log('🔍 DROPDOWN VERIFICATION:', {
        'Expected Name': dropdownName,
        'Found Element': !!dropdownElement,
        'Current Value': dropdownElement ? dropdownElement.value : 'NOT FOUND',
        'New Value': selectE.value
    });

    // Show/hide ticket forms instantly using direct DOM manipulation
    console.log('🎫 SHOWING/HIDING TICKET FORMS FOR LOTTERY:', Lid);

    // First, let's find all elements that might be ticket forms for this lottery
    const allPossibleTickets = document.querySelectorAll(`[class*="${Lid}"]`);
    console.log(`🔍 ALL ELEMENTS WITH LOTTERY ID ${Lid}:`, allPossibleTickets.length);
    allPossibleTickets.forEach((el, index) => {
        console.log(`   Element ${index}:`, el.className);
    });

    for(let i=1;i<=5;i++) {
        // Try multiple selectors to find the ticket elements
        const selectors = [
            `.${Lid}.t_${i}`,                           // Original selector
            `[class*="${Lid}"][class*="t_${i}"]`,       // Contains both classes
            `.t_${i}[class*="${Lid}"]`,                 // Alternative order
            `div[class*="${Lid} t_${i}"]`,              // Space-separated classes
            `div[class*="t_${i}"][class*="${Lid}"]`,    // Both classes present
            `div[class~="${Lid}"][class~="t_${i}"]`,    // Exact word match
            `div.col-sm-12.h287.${Lid}.t_${i}`,         // Full class chain
            `[class="${Lid} t_${i}"]`,                  // Exact class match (partial)
            `[class*="col-sm-12 h287 ${Lid} t_${i}"]`   // Full class string
        ];

        let ticketElement = null;
        for (let selector of selectors) {
            try {
                ticketElement = document.querySelector(selector);
                if (ticketElement) {
                    console.log(`✅ FOUND TICKET ${i} WITH SELECTOR:`, selector);
                    break;
                }
            } catch (e) {
                console.warn(`⚠️ INVALID SELECTOR: ${selector}`, e.message);
            }
        }

        if(ticketElement) {
            if(i<=selectE.value) {
                ticketElement.style.display = 'block';
                console.log(`✅ SHOWING TICKET FORM ${i}`);
            } else {
                ticketElement.style.display = 'none';
                console.log(`❌ HIDING TICKET FORM ${i}`);
            }
        } else {
            console.warn(`⚠️ TICKET FORM ${i} NOT FOUND FOR LOTTERY ${Lid}`);
        }
    }

    // INSTANT PAYMENT UPDATE - Use the new real-time update function
    const newTicketCount = parseInt(selectE.value);
    console.log('🚀 CALLING REAL-TIME PAYMENT UPDATE...');

    // DEBUG: Show payment section state BEFORE update
    console.log('📊 BEFORE UPDATE:');
    debugPaymentSection();

    updatePaymentForTicketChange(Lid, newTicketCount);

    // DEBUG: Show payment section state AFTER update
    console.log('📊 AFTER UPDATE:');
    debugPaymentSection();

    console.log('✅ REAL-TIME PAYMENT UPDATE COMPLETED');
    console.log('🎯 === CHANGE TICKET NO FUNCTION COMPLETED ===\n');
}

// BULLETPROOF: Universal lottery compatibility check
function checkLotteryCompatibility() {
    console.log('🔍 === UNIVERSAL LOTTERY COMPATIBILITY CHECK ===');

    const allCheckboxes = document.querySelectorAll('input[type="checkbox"][name="lotteryNames[]"]');
    console.log('📊 FOUND LOTTERY CHECKBOXES:', allCheckboxes.length);

    allCheckboxes.forEach((checkbox, index) => {
        const lotteryType = checkbox.getAttribute('data-lname');
        const lotteryId = checkbox.value ? checkbox.value.split('_')[1] : 'unknown';
        const price = checkbox.value ? checkbox.value.split('_')[2] : 'unknown';

        console.log(`🎯 LOTTERY ${index + 1}:`, {
            'Checkbox ID': checkbox.id,
            'Lottery Type': lotteryType,
            'Lottery ID': lotteryId,
            'Price': price,
            'OnChange Attribute': checkbox.getAttribute('onchange'),
            'Data Attributes': Array.from(checkbox.attributes).filter(attr => attr.name.startsWith('data-'))
        });

        // Test if handleChange function is accessible
        if (typeof window.handleChange === 'function') {
            console.log('✅ handleChange function is accessible');
        } else {
            console.error('❌ handleChange function is NOT accessible');
        }
    });

    console.log('🔍 === COMPATIBILITY CHECK COMPLETED ===\n');
}

// NEW: Comprehensive DOM debugging function
function debugPaymentSection() {
    console.log('🔍 === COMPREHENSIVE PAYMENT SECTION DEBUG ===');

    // Check payment section visibility
    const paymentSection = document.getElementById('paySide');
    console.log('💳 PAYMENT SECTION:', {
        'Found': !!paymentSection,
        'Display': paymentSection ? paymentSection.style.display : 'NOT FOUND',
        'Visibility': paymentSection ? paymentSection.style.visibility : 'NOT FOUND',
        'Computed Display': paymentSection ? window.getComputedStyle(paymentSection).display : 'NOT FOUND'
    });

    // Check invoice details
    const invoiceDetails = document.getElementById('lotteryInvoiceDetails');
    console.log('📋 INVOICE DETAILS:', {
        'Found': !!invoiceDetails,
        'HTML Content': invoiceDetails ? invoiceDetails.innerHTML : 'NOT FOUND'
    });

    // Check all lottery cost inputs
    const allLotteryInputs = document.querySelectorAll('#lotteryInvoiceDetails input[readonly]');
    console.log('🎫 LOTTERY COST INPUTS:', allLotteryInputs.length);
    allLotteryInputs.forEach((input, index) => {
        const parentRow = input.closest('.row');
        console.log(`   Input ${index}:`, {
            'Value': input.value,
            'Placeholder': input.placeholder,
            'Parent Row Text': parentRow ? parentRow.textContent.trim() : 'NO PARENT'
        });
    });

    // Check invoice total section
    const invoiceTotal = document.getElementById('lotteryInvoiceTotal');
    console.log('💰 INVOICE TOTAL:', {
        'Found': !!invoiceTotal,
        'HTML Content': invoiceTotal ? invoiceTotal.innerHTML : 'NOT FOUND'
    });

    // Check registration fee and total inputs
    const regFeeInput = document.querySelector('.lotteryTotal input[readonly]');
    const totalInput = document.getElementById('lotterypricetotal');
    console.log('💵 PAYMENT INPUTS:', {
        'Registration Fee Input Found': !!regFeeInput,
        'Registration Fee Value': regFeeInput ? regFeeInput.value : 'NOT FOUND',
        'Total Input Found': !!totalInput,
        'Total Value': totalInput ? totalInput.value : 'NOT FOUND'
    });

    // Check global variables
    console.log('🌐 GLOBAL VARIABLES:', {
        'lotteryTotal': lotteryTotal,
        'subscribePrice': subscribePrice,
        'registration_price': registration_price,
        'added_lotteries': added_lotteries
    });

    console.log('🔍 === END PAYMENT SECTION DEBUG ===\n');
}

// BULLETPROOF: Initialize compatibility check on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 === PAGE LOADED - INITIALIZING BULLETPROOF LOTTERY SYSTEM ===');

    // Run compatibility check
    setTimeout(checkLotteryCompatibility, 1000);

    // Add event listeners as backup - ENHANCED VERSION
    const allCheckboxes = document.querySelectorAll('input[type="checkbox"][name="lotteryNames[]"]');
    console.log('🔧 FOUND CHECKBOXES FOR EVENT BINDING:', allCheckboxes.length);

    allCheckboxes.forEach((checkbox, index) => {
        console.log(`🔧 BINDING EVENTS TO CHECKBOX ${index}:`, {
            'ID': checkbox.id,
            'Value': checkbox.value,
            'Data-lname': checkbox.getAttribute('data-lname')
        });

        // KEEP the original onchange attribute - don't remove it
        console.log('🔍 ORIGINAL ONCHANGE:', checkbox.getAttribute('onchange'));

        // Add backup event listeners with delays to avoid conflicts with original onchange
        checkbox.addEventListener('change', function() {
            console.log('🔄 BACKUP CHANGE EVENT TRIGGERED FOR:', this.getAttribute('data-lname'));
            // Delay to let original onchange run first
            setTimeout(() => {
                console.log('🔄 BACKUP CHANGE EVENT EXECUTING...');
                handleCheckboxChange(this, index);
            }, 100);
        });

        checkbox.addEventListener('click', function() {
            console.log('🔄 BACKUP CLICK EVENT TRIGGERED FOR:', this.getAttribute('data-lname'));
            // Delay to ensure checkbox state is updated and original onchange runs
            setTimeout(() => {
                console.log('🔄 BACKUP CLICK EVENT EXECUTING...');
                handleCheckboxChange(this, index);
            }, 150);
        });
    });

    // Create unified checkbox handler
    function handleCheckboxChange(checkbox, index) {
        console.log('🎯 UNIFIED CHECKBOX HANDLER CALLED:', {
            'Checkbox': checkbox.id,
            'Checked': checkbox.checked,
            'Index': index
        });

        // Extract parameters from checkbox
        const value = checkbox.value;
        const parts = value.split('_');
        if (parts.length >= 3) {
            const lotteryType = parts[0];
            const lotteryId = parts[1];
            const price = parts[2];

            console.log('🎯 CALLING handleChange WITH EXTRACTED PARAMS:', {
                'lotteryType': lotteryType,
                'lotteryId': lotteryId,
                'price': price,
                'index': index
            });

            // Call handleChange
            if (typeof window.handleChange === 'function') {
                window.handleChange(checkbox, price, lotteryId, index);
            } else {
                console.error('❌ handleChange function not found!');
            }
        } else {
            console.error('❌ Could not parse checkbox value:', value);
        }
    }

    console.log('✅ BULLETPROOF LOTTERY SYSTEM INITIALIZED');
});

// Ensure functions are globally accessible
window.changeTicketNo = changeTicketNo;
window.handleChange = handleChange;
window.updatePaymentForTicketChange = updatePaymentForTicketChange;
window.findLotteryRow = findLotteryRow;
window.checkLotteryCompatibility = checkLotteryCompatibility;

// Payment system ready
console.log('💰 Payment system functions loaded and ready');

// Payment system ready and working correctly

$(document).ready(() => {
    console.log('🚀 DOCUMENT READY - Initializing lottery payment system...');

    // Show first ticket form for all lotteries
    $('.t_1').show();

    console.log('✅ PAYMENT SECTION INITIALIZATION COMPLETE');

    // Ensure payment section is initially hidden
    const paymentSection = document.getElementById('paySide');
    if (paymentSection) {
        paymentSection.style.display = 'none';
        paymentSection.style.visibility = 'hidden';
        paymentSection.style.opacity = '0';
    }

    // Clear any existing payment data
    lotteryTotal = 0;
    subscribePrice = 0;
    clickCounter = 0;
    added_lotteries = {};

    console.log('✅ LOTTERY PAYMENT SYSTEM INITIALIZED');

    // Add backup event listeners for ticket quantity dropdowns
    const dropdowns = document.querySelectorAll('select[name^="tickets_no"]');
    dropdowns.forEach((dropdown) => {
        // Primary backup event listener
        dropdown.addEventListener('change', function(e) {
            const lotteryId = e.target.name.replace('tickets_no', '');
            changeTicketNo(e.target, lotteryId);
        });

        // Secondary backup using direct payment update
        dropdown.addEventListener('change', function(e) {
            const lotteryId = e.target.name.replace('tickets_no', '');
            const newTicketCount = parseInt(e.target.value);

            // Direct payment update as backup
            setTimeout(() => {
                updatePaymentForTicketChange(lotteryId, newTicketCount);
            }, 100); // Small delay to ensure primary update completes first
        });
    });
});
