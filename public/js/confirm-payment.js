$( document ).ready(function() {
    getPaymentList();
});
function getPaymentInfo(payment_id){
    fetch(config.hostApi+`/admin/payment/get-by-id/${payment_id}`, {
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=utf-8'
        }
    })
    .then((response) => {
        return response.json();
    })
    .then(data => {
        console.log('Payment data received:', data);
        const paymentData = data.payment

        // Display the correct payment ID (LPG ID for email transfers, regular ID for others)
        const displayPaymentId = paymentData.lpg_payment_id || paymentData.payment_id;
        document.getElementById('inputPaymentId').value = displayPaymentId;
        document.getElementById('inputPaymentAmount').value = paymentData.payment_amount;
        document.getElementById('inputDateReceived').value = paymentData.date_added ? paymentData.date_added.split("T")[0] : null;
        document.getElementById('inputPaymentEmailId').value = paymentData.payment_email_id;
        document.getElementById('inputMobile').value = paymentData.payment_phone_number || 'Not provided';

        // Clear and populate ticket table
        let tbody = document.getElementById('ticket-table');
        tbody.innerHTML = "";

        if (data.tickets && data.tickets.length > 0) {
            for(let item of data.tickets){
                let tr = document.createElement('tr');
                tr.style.backgroundColor = 'white';
                tr.style.color = 'black';

                // Lottery Name
                let lotteryName = document.createElement('td');
                lotteryName.innerText = item.lottery_name || 'N/A';
                lotteryName.style.padding = '12px';
                lotteryName.style.fontWeight = '500';

                // Player Name
                let nameCell = document.createElement('td');
                nameCell.innerText = item.playerName || 'N/A';
                nameCell.style.padding = '12px';

                // Age
                let ageCell = document.createElement('td');
                ageCell.innerText = item.age || 'N/A';
                ageCell.style.padding = '12px';

                // Quick Pick or Custom Numbers - Enhanced display
                let quickPick = document.createElement('td');
                quickPick.style.padding = '12px';
                if(item.quickpick === 1){
                    quickPick.innerHTML = '<span class="badge badge-success" style="background-color: #28a745; color: white; padding: 5px 10px; border-radius: 12px; font-size: 11px;">Quick Pick</span>';
                } else {
                    let numbers = item.custom_number || 'N/A';
                    quickPick.innerHTML = `<span class="badge badge-info" style="background-color: #17a2b8; color: white; padding: 5px 10px; border-radius: 12px; font-size: 11px;">${numbers}</span>`;
                }

                tr.appendChild(lotteryName);
                tr.appendChild(nameCell);
                tr.appendChild(ageCell);
                tr.appendChild(quickPick);

                tbody.appendChild(tr);
            }
        } else {
            // Show message if no tickets found
            let tr = document.createElement('tr');
            let td = document.createElement('td');
            td.colSpan = 4;
            td.innerHTML = '<div style="text-align: center; padding: 20px; color: #856404; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">⚠️ No tickets found for this payment</div>';
            td.style.padding = '20px';
            tr.appendChild(td);
            tbody.appendChild(tr);
        }

        document.getElementById('payment-id').value = payment_id;
        document.getElementById('tickets').value = data.tickets ? data.tickets.map(v => v.ticket_id).join(",") : "";
     })
     .catch(error => {
         console.error('Error fetching payment info:', error);
         alert('Error loading payment details. Please try again.');
     });
}
async function updatePaymentInfo(event){
    try{
        let table = null
        let e = event || window.event
        e.preventDefault()
        let obj = {
            "id" : document.getElementById('payment-id').value,
            //"date_completed" : document.getElementById('inputDateCompleted').value,
            "tickets" : document.getElementById('tickets').value,
        }
        console.log(obj)
        let res = await fetch(config.hostApi+"/admin/payment/confirm",{
            method : "POST",
            headers : { "Content-Type" : "application/json" },
            body : JSON.stringify(obj)
        })
        let status = await res.json()
        if(status){
            if ( $.fn.dataTable.isDataTable('#paymentList') ) {
                table = $('#paymentList').DataTable();
            }
            let resp = await fetch(config.hostApi+"/admin/payment/get-payments",{
                method : "GET"
            })
    
            let data = await resp.json()
            console.log(data)
            if(data){
                table.clear().draw();
                table.rows.add(data.result.map(x=>{
                    //console.log(x);
                    return {
                        payment_id: x.lpg_payment_id ? x.lpg_payment_id : (x.payment_id ? x.payment_id : "NA"),
                        payment_amount:x.payment_amount ? x.payment_amount : "NA",
                        date_added:x.date_added ? x.date_added.split("T")[0] : "Not Completed",
                        payment_method:x.payment_method ? x.payment_method : "NA",
                        payment_email_id:x.payment_email_id ? x.payment_email_id : "NA",
                        payment_phone_number : x.payment_phone_number ? x.payment_phone_number : "NA",
                        date_received : x.date_received ? x.date_received.split("T")[0] : "NA",
                        tx_id:x.tx_id? x.tx_id : "NA",
                        unique_base64code : x.unique_base64code? x.unique_base64code : "NA",
                        postalCode : x.postal_code? x.postal_code : "NA",
                        ticket_id : x.ticket_id ? x.ticket_id : "NA",
                        payment_status : x.payment_status === "COMPLETED" ? "Completed" : "Pending"
                    }
                }));
                table.columns.adjust().draw();
            }
            alert("Payment Status updated successfully")
        }
        else{
            alert("Ticket could not be confirmed")
        }
    }catch(err){
        alert("There has been some kind of error, Please try again")
    }
}
function getPaymentList(){
    getApi(config.hostApi+"/admin/payment/get-payments",null).then(res=>{
       $("#paymentList").DataTable({
           searching: false,
           scrollX : true,
           dom: 'Bfrtip',
           buttons: [ "excel"],
               data:res.result.map(x=>{
                        //console.log(x);
                        return {
                            payment_id: x.lpg_payment_id ? x.lpg_payment_id : (x.payment_id ? x.payment_id : "NA"),
                            payment_amount:x.payment_amount ? x.payment_amount : "NA",
                            date_added:x.date_added ? x.date_added.split("T")[0] : "Not Completed",
                            payment_method:x.payment_method ? x.payment_method : "NA",
                            payment_email_id:x.payment_email_id ? x.payment_email_id : "NA",
                            payment_phone_number : x.payment_phone_number ? x.payment_phone_number : "NA",
                            date_received : x.date_received ? x.date_received.split("T")[0] : "NA",
                            tx_id:x.tx_id? x.tx_id : "NA",
                            unique_base64code : x.unique_base64code? x.unique_base64code : "NA",
                            postalCode : x.postal_code? x.postal_code : "NA",
                            ticket_id : x.ticket_id ? x.ticket_id : "NA",
                            payment_status : x.payment_status === "COMPLETED" ? "Completed" : "Pending"
                        }
                    }),
                    columns: [
                    {
                        data:"payment_id",
                        title:"Payment ID"
                    },{
                        data:"payment_amount",
                        title:"Payment Amount"
                    },{
                       data:"date_added",
                       title:"Date Started",
                   },{
                        data:"payment_method",
                        title:"Payment Method"
                    },{
                        data:"payment_email_id",
                        title:"Payment Email"
                    },{
                        data:"payment_phone_number",
                        title:"Contact Number"
                    },{
                       data:"date_received",
                       title:"Date Completed"
                   },{
                       data:"ticket_id",
                       title:"Associated Tickets"
                   },{
                       data:"payment_status",
                       title:"Payment Status"
                   },{
                        title: "Action",
                        class: "table-first-name",
                        render : function (data, type, row){
                            return `
                                <div class="btn-group-vertical" role="group">
                                    <button type="button" class="btn btn-primary btn-sm mb-1" data-toggle="modal" data-target="#exampleModalCenter" onclick="getPaymentInfo('${row.payment_id}')">Update</button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deletePayment('${row.payment_id}')">Delete</button>
                                    <button type="button" class="btn btn-success btn-sm mt-1" onclick="sendEmail('${row.payment_id}')">Send Email</button>
                                </div>
                            `;
                        }
                    }
                ]
       });
    })
}

// Delete payment function
async function deletePayment(paymentId) {
    if (!confirm('Are you sure you want to delete this payment? This will also delete all associated tickets and cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(config.hostApi + `/admin/payment/delete/${paymentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok && result.status === 'success') {
            alert('Payment deleted successfully');
            // Refresh the payment list
            location.reload();
        } else {
            alert('Error deleting payment: ' + (result.error || result.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting payment:', error);
        alert('Error deleting payment: ' + error.message);
    }
}

// Send email function
async function sendEmail(paymentId) {
    try {
        console.log('Sending email for payment:', paymentId);

        const response = await fetch(config.hostApi + `/admin/payment/send-email/${paymentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok && result.status === 'success') {
            alert('Confirmation email sent successfully!');
        } else {
            alert('Error sending email: ' + (result.error || result.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error sending email:', error);
        alert('Error sending email: ' + error.message);
    }
}
