function getlotteryList() {

    fetch(config.hostApi+'/api/get-lottery', {
            method: 'get',
            headers: {
                'Content-Type': 'application/json;charset=utf-8'
            }
        })
        .then((response) => {
            return response.json();
        })
        .then(values => {
            $('#lotterySection').DataTable({
                responsive: true,
                scrollX: true,
                language: {
                    search: "",
                    searchPlaceholder: "Search"
                },
                dom: 'Bfrtip ',

                paging: true,
                data: values.map(x => {
                    return {
                        lottery_name: x.lottery_name ? x.lottery_name : "NA",
                        start_date: x.start_date ? moment(x.start_date).utc().format("YYYY-MM-DD hh:mm:ss") : "NA",
                        end_date: x.end_date ? moment(x.end_date).utc().format("YYYY-MM-DD hh:mm:ss") : "NA",
                        price :x.lottery_price ? x.lottery_price:"NA",
                        winning_price:x.winning_price?x.winning_price:"NA",
                        bumper_prize:x.bumper_prize?x.bumper_prize:"NA",
                        no_of_groups:x.no_of_groups?x.no_of_groups:"NA",
                        person_per_group:x.person_per_group?x.person_per_group:"NA",
                        subscription_entry_price:x.subscription_entry_price?x.subscription_entry_price:"NA",
                        encore:x.encore?x.encore:0,
                        quickpick:x.quickpick?x.quickpick:0,
                        customeNumber:x.custom_number?x.custom_number:0,
                        lottery_id :x.id

                    };
                }),

                columns: [
                {
                    data: "lottery_name",
                    title: "Lottery Name",
                    class: "table-first-name"
                },
                {
                    data: "start_date",
                    title: "Start Date",
                    class: "table-first-name"
                },
                {
                    data: "end_date",
                    title: "End Date",
                    class: "table-first-name"
                },  {
                    data: "price",
                    title: " Lottery Price",
                    class: "table-first-name"
                }, {
                    data:"no_of_groups",
                    title: "No fo Group",
                    class: "table-first-name"
                } , {
                    data:"person_per_group",
                    title: "No of Person",
                    class: "table-first-name"
                } , {
                    data:"subscription_entry_price",
                    title: "Subscription Entry Price",
                    class: "table-first-name"
                } , {
                    data:"encore",
                    title: "encore",
                    class: "table-first-name"
                } , {
                    data:"quickpick",
                    title: "quickPick",
                    class: "table-first-name"
                } , {
                    data:"customeNumber",
                    title: "customeNumber",
                    class: "table-first-name"
                } ,{
                    title: "Action",
                    class: "table-first-name",
                    render : function (data, type, row){
                        return '<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter" onclick=getLotteryById('+row.lottery_id+')>Update</button>';
                    }
                }]
            });
        });
}


function getLotteryById(lotteryid){
    fetch(window.location.origin + '/api/get-lottery-detail?lotterid=' + lotteryid + '', {
        method: 'get',
        headers: {
            'Content-Type': 'application/json;charset=utf-8'
        }
    })
    .then((response) => {
        return response.json();
    })
    .then(data => {
        const lotteryData = data[0];

        if (lotteryData) {
            document.getElementById("lotteryName").value = lotteryData.lottery_name;
            document.getElementById("lotteryStartDate").value = moment(lotteryData.start_date).utc().format('YYYY-MM-DD') ;
            document.getElementById("lotteryEndDate").value =  moment(lotteryData.end_date).utc().format('YYYY-MM-DD');
            document.getElementById("lotteryDrawDate").value = moment(lotteryData.date_of_joining).utc().format('YYYY-MM-DD');
            document.getElementById("lotteryPrice").value = lotteryData.lottery_price;
            document.getElementById("lotteryBumperPrice").value = lotteryData.bumper_prize;
            document.getElementById("lotteryGroupNumber").value = lotteryData.no_of_groups;
            document.getElementById("lotteryPersonPerGroup").value = lotteryData.person_per_group;
            document.getElementById("lotteryCountry").value = lotteryData.country;
            document.getElementById("lotteryState").value = lotteryData.state;
            document.getElementById("lotterysubscriptionEntryPrice").value = lotteryData.subscription_entry_price;
            document.getElementById("isDeleted").checked  = lotteryData.is_deleted ? true :false;
            document.getElementById("isEncore").checked  = lotteryData.encore ? true :false;
            document.getElementById("QuickPick").checked  = lotteryData.quickpick ? true :false;
            document.getElementById("customerNumber").checked  = lotteryData.custom_number ? true :false;

            document.getElementById("isActive").checked = lotteryData.is_active ? true : false;
            document.getElementById("cardcolordark").value = lotteryData.card_color_dark;
            document.getElementById("cardcolorlight").value =  lotteryData.card_color_light;
        }
    });
}

function getLotteryByName(lotteryName){
    fetch(window.location.origin + '/api/get-lottery-detail?lotteryname=' + encodeURIComponent(lotteryName), {
        method: 'get',
        headers: {
            'Content-Type': 'application/json;charset=utf-8'
        }
    })
    .then((response) => {
        return response.json();
    })
    .then(data => {
        const lotteryData = data[0];

        if (lotteryData) {
            // Set the hidden lottery ID field for the update form
            document.getElementById("hiddenLotteryId").value = lotteryData.id;

            document.getElementById("lotteryName").value = lotteryData.lottery_name;
            document.getElementById("lotteryStartDate").value = moment(lotteryData.start_date).utc().format('YYYY-MM-DD') ;
            document.getElementById("lotteryEndDate").value =  moment(lotteryData.end_date).utc().format('YYYY-MM-DD');
            document.getElementById("lotteryDrawDate").value = moment(lotteryData.date_of_joining).utc().format('YYYY-MM-DD');
            document.getElementById("lotteryPrice").value = lotteryData.lottery_price;
            document.getElementById("lotteryBumperPrice").value = lotteryData.bumper_prize;
            document.getElementById("lotteryGroupNumber").value = lotteryData.no_of_groups;
            document.getElementById("lotteryPersonPerGroup").value = lotteryData.person_per_group;
            document.getElementById("lotteryCountry").value = lotteryData.country;
            document.getElementById("lotteryState").value = lotteryData.state;
            document.getElementById("lotterysubscriptionEntryPrice").value = lotteryData.subscription_entry_price;
            document.getElementById("isDeleted").checked  = lotteryData.is_deleted ? true :false;
            document.getElementById("isEncore").checked  = lotteryData.encore ? true :false;
            document.getElementById("QuickPick").checked  = lotteryData.quickpick ? true :false;
            document.getElementById("customerNumber").checked  = lotteryData.custom_number ? true :false;

            // Set the isActive toggle - ensure it works with both 0/1 and true/false
            const isActiveValue = lotteryData.is_active;
            const toggleElement = document.getElementById("isActive");

            // Force the toggle to the correct state
            toggleElement.checked = (isActiveValue == 1 || isActiveValue === true);

            // Trigger change event to ensure CSS updates
            toggleElement.dispatchEvent(new Event('change'));

            // Handle color fields safely (they might be commented out)
            const cardColorDark = document.getElementById("cardcolordark");
            const cardColorLight = document.getElementById("cardcolorlight");
            if (cardColorDark) cardColorDark.value = lotteryData.card_color_dark || '#FFFFFF';
            if (cardColorLight) cardColorLight.value = lotteryData.card_color_light || '#FFFFFF';
        } else {

        }
    })
    .catch(error => {
        console.error('Error fetching lottery data:', error);
    });
}

$(window).on("load", function () {
    // getlotteryList();

});

document.addEventListener('DOMContentLoaded', function() {
    // const error = swalMsg)};
    if (swalMsg) {
       Swal.fire({
       title: swalMsg.error?'Error':"Success",
       text: swalMsg.msg,
       icon: swalMsg.error?'error':"success",
       confirmButtonText: 'Ok',
       reverseButtons: true
       }).then((result) => {
       })
    }
  });

$(document).ready(function () {

    let selectedLotteryName = $('#inputSelectLotteryId').val();
    if (selectedLotteryName && selectedLotteryName !== '') {
        getLotteryByName(selectedLotteryName);
    }

    $('#inputSelectLotteryId').change(function () {
        let selectedLotteryName = $(this).val();
        if (selectedLotteryName && selectedLotteryName !== '') {
            getLotteryByName(selectedLotteryName);
        }
    });


})


