$(document).ready(function() {
    // Form validation
    $('#contact-form').on('submit', function() {
        const form = $(this);
        const submitBtn = form.find('input[type="submit"]');
        
        // Basic validation
        let isValid = true;
        
        // Check required fields
        form.find('[required]').each(function() {
            if ($(this).val().trim() === '') {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // Check email format
        const emailField = form.find('input[type="email"]');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailField.val() && !emailRegex.test(emailField.val())) {
            isValid = false;
            emailField.addClass('is-invalid');
        }
        
        if (!isValid) {
            return false; // Prevent form submission
        }
        
        // Disable submit button to prevent multiple submissions
        submitBtn.prop('disabled', true);
        
        // Allow the form to submit normally for redirect
        return true;
    });
    
    // Clear validation styling on input
    $('#contact-form input, #contact-form select, #contact-form textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});
