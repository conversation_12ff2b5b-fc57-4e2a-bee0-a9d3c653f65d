$(document).ready(function(){
    getSubscriberList()

    $('#collapseTwo').on('shown.bs.collapse', function () {
        $($.fn.dataTable.tables(true)).DataTable()
           .columns.adjust();
     });
})
function getSubscriberList(){
    getApi(config.hostApi+"/admin/news-letter/getall",null).then(res=>{
       $("#subscriberList").DataTable({
           searching: false,
           scrollX : true,
           dom: 'Bfrtip',
           buttons: [ "excel"],
               data:res.result.map(x=>{
                        //console.log(x);
                        return {
                            id : x.id,
                            email : x.email,
                            created_at : x.created_at
                        }
                    }),
                    columns: [
                    {
                        data:"id",
                        title:"Subscriber ID"
                    },{
                        data:"email",
                        title:"Subscirber Email"
                    },{
                       data:"created_at",
                       title:"Subscription Start Timestamp",
                   },{
                        title: "Action",
                        class: "table-first-name",
                        render : function (data, type, row){
                            return '<button type="button" class="btn btn-danger" data-toggle="modal" data-target="#exampleModalCenter" onclick=deleteUser('+row.id+')>Delete</button>';
                        }
                    }
                ]
       });
    })
}

async function deleteUser(id){
    let resp = await fetch(config.hostApi+"/admin/news-letter/delete",{
        method : "POST",
        headers : {"Content-Type" : "application/json"},
        body : JSON.stringify({"id" : id})
    })
    let data = await resp.json()
    if(data.status){
        alert("Record Successfully Deleted")
        getSubscriberList()
    }
    else{
        alert("Record could not be deleted")
    }
}
