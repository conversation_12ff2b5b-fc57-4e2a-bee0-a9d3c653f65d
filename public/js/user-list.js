$( document ).ready(function() {
    getUserList(null,null);
});

// Define config object if it doesn't exist
if (typeof config === 'undefined') {
    var config = {
        hostApi: ''
    };
}

// Add postApi function for making API calls
async function postApi(url, data) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        return await response.json();
    } catch (error) {
        console.error('API Error:', error);
        return { userData: [] };
    }
}

const group = ["A","B","C","D"]; // Only 4 groups allowed as per business requirements
function getval(sel)
{
    var selectedOption = sel.options[sel.selectedIndex];
    const groupCount = selectedOption.getAttribute('groupcount');
   groupDrop(groupCount);
}

function groupDrop(groupCount){
    select = document.getElementById('groupList');
    select.classList.remove('hidden');
    select.innerHTML = '<option value="">Select the Group</option>';

    // Only show groups A, B, C, D (limit to 4 groups max)
    const maxGroups = Math.min(groupCount, 4);
    for(i = 0; i < maxGroups; i++){
        var opt = document.createElement('option');
        opt.value = i + 1;  // Store numeric value (1, 2, 3, 4)
        opt.innerHTML = group[i];  // Display letter (A, B, C, D)
        select.appendChild(opt);
    }
}

function submit(){

    let e = document.getElementById('lotteryList');
    let lotteryvalue = e.options[e.selectedIndex].value ? e.options[e.selectedIndex].value : null;
    let ele = document.getElementById('groupList');
    let groupvalue = ele.options[ele.selectedIndex].value ?  ele.options[ele.selectedIndex].value: null ;
    getUserList(lotteryvalue,groupvalue);

    // Enable email button and populate hidden fields for email functionality
    if(lotteryvalue && groupvalue){
        // Populate hidden form fields for email
        document.getElementById('lotteryid').value = lotteryvalue;
        document.getElementById('lotteryName').value = e.options[e.selectedIndex].text ? e.options[e.selectedIndex].text : null;
        document.getElementById('groupid').value = groupvalue;
        document.getElementById('groupName').value = ele.options[ele.selectedIndex].text ? ele.options[ele.selectedIndex].text : null;

        // Enable email button
        document.getElementById("emailbtn").removeAttribute("disabled");
    } else {
        // Disable email button if no lottery or group selected
        document.getElementById("emailbtn").setAttribute("disabled", "disabled");
    }
}


function getUserList(lotteryId,groupId){
  const data = {"lotteryId":lotteryId,"groupId":groupId};
  document.getElementById('groupSelection').innerHTML = '';
  document.getElementById('groupSelection').innerHTML = '<table id="userList" class="table table-striped table-bordered table-hover"><thead class="thead-dark"></thead></table>';
  
  // Use the correct API endpoint
  const apiUrl = "/admin/user-list/get-user-list";
  
  console.log('Fetching user list with data:', data);
  
  postApi(apiUrl, data)
    .then(data => {
      console.log('API response:', data);
      
      if (!data || !data.userData) {
        console.error('Invalid response format:', data);
        document.getElementById('groupSelection').innerHTML = '<div class="alert alert-danger">Error loading user data</div>';
        return;
      }
      
      let userList = data.userData;
      
      if (userList.length === 0) {
        alert('No paid customers found for the selected lottery and group.');
        document.getElementById('groupSelection').innerHTML = '<div class="alert alert-info">No paid customers found for the selected criteria</div>';
        return;
      }
      
      $("#userList").DataTable({
        searching: true,
        scrollX: true,
        responsive: true,
        pageLength: 10,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
        dom: 'Blfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'Export to Excel',
                className: 'btn btn-success btn-sm'
            }
        ],
        language: {
            search: "Search customers:",
            lengthMenu: "Show _MENU_ customers per page",
            info: "Showing _START_ to _END_ of _TOTAL_ customers",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        },
        data: userList.map(x => {
          console.log(x);
          return {
                         ticketId:x.ticket_id,
                         userId:x.user_id,
                         firstName:x.first_name,
                         lastName :x.last_name ,
                         profession:x.profession,
                         email:x.email_id,
                         phoneNumber:x.phone_number,
                         group:x.group_no ? group[x.group_no-1] : '-',
                         // Handle fields that might exist in live but not in dev
                         playerName: x.playerName || '-',
                         personGroupno: x.person_in_group || '-',
                         QuickPickStatus:x.quickpick ? "true":"false",
                         TicketNumber: (() => {
                             if (x.quickpick === 1) {
                                 return "Quick Pick";
                             } else if (x.custom_number && typeof x.custom_number === 'string' && x.custom_number.includes(',')) {
                                 return x.custom_number;
                             } else if (x.custom_number && x.custom_number !== "1") {
                                 return x.custom_number;
                             } else {
                                 return "Custom Numbers";
                             }
                         })(),
                         lotteryName:x.lottery_name,
                         date_joined:x.created_date
                     }
                 }),
                 columns: [{
                     data:"firstName",
                     title:"First Name",
                     width: "120px",
                     className: "text-center font-weight-bold"
                 },{
                     data:"lastName",
                     title:"Last Name",
                     width: "120px",
                     className: "text-center font-weight-bold"
                 },{
                    data:"profession",
                    title:"Profession",
                    width: "100px",
                    className: "text-center",
                    visible: false
                },{
                     data:"email",
                     title:"Email",
                     width: "200px",
                     className: "text-truncate",
                     render: function(data, type, row) {
                         if (data && data.length > 25) {
                             return '<span title="' + data + '">' + data.substring(0, 25) + '...</span>';
                         }
                         return data || 'N/A';
                     }
                 },{
                     data:"phoneNumber",
                     title:"Phone",
                     width: "120px",
                     className: "text-center"
                 },{
                     data:"group",
                     title:"Group",
                     width: "80px",
                     className: "text-center",
                     render: function(data, type, row) {
                         return '<span class="badge badge-primary">' + (data || 'N/A') + '</span>';
                     }
                 },{
                     data:"personGroupno",
                     title:"Person #",
                     width: "80px",
                     className: "text-center",
                     render: function(data, type, row) {
                         return '<span class="badge badge-info">' + (data || 'N/A') + '</span>';
                     }
                 },{
                     data:"playerName",
                     title:"Player Name",
                     width: "120px",
                     className: "text-center"
                },{
                     data:"TicketNumber",
                     title:"Quick Pick / Lucky Numbers",
                     width: "150px",
                     className: "text-center font-weight-bold",
                     render: function(data, type, row) {
                         if (data === "Quick Pick") {
                             return '<span class="badge badge-success">Quick Pick</span>';
                         } else if (data && data !== "-") {
                             return '<span class="badge badge-warning">' + data + '</span>';
                         }
                         return '<span class="text-muted">-</span>';
                     }
                 },{
                     data:"lotteryName",
                     title:"Lottery",
                     width: "150px",
                     className: "text-center",
                     render: function(data, type, row) {
                         return '<span class="badge badge-info">' + (data || 'N/A') + '</span>';
                     }
                 }]
    });
 })
}


function emailSubmit(){

    let e = document.getElementById('lotteryList');
    let lotteryvalue = e.options[e.selectedIndex].value ? e.options[e.selectedIndex].value : null;
    document.getElementById('lotteryid').value =lotteryvalue;
    document.getElementById('lotteryName').value = e.options[e.selectedIndex].text ? e.options[e.selectedIndex].text : null;;
    let ele = document.getElementById('groupList');
    let groupvalue = ele.options[ele.selectedIndex].value ?  ele.options[ele.selectedIndex].value: null ;
    document.getElementById('groupid').value =groupvalue;
    document.getElementById('groupName').value =ele.options[ele.selectedIndex].text ?  ele.options[ele.selectedIndex].text: null ;
    getUserList(lotteryvalue,groupvalue);
    if(lotteryvalue && groupvalue){
        document.getElementById("emailbtn").removeAttribute("disabled");
    }else{
        document.getElementById("emailbtn").setAttribute("disabled", "disabled");
    }
}

function fileValidation(){
    var fileInput = document.getElementById('imageFile');
  
        var filePath = fileInput.value;

        // Allowing file type
        var allowedExtensions = 
                /(\.jpg|\.jpeg|\.png|\.gif)$/i;
        
        if (!allowedExtensions.exec(filePath)) {
            alert('Invalid file type');
            fileInput.value = '';
            return false;
        } 
}
