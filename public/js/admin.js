/*global $, console*/
/*
  By <PERSON><PERSON><PERSON>
	https://www.facebook.com/MostafaOmarIbrahiem
*/
$(function () {

    'use strict';
  
    (function () {
  
      var aside = $('.side-nav'),
  
          showAsideBtn = $('.show-side-btn'),
  
          contents = $('#contents');
  
      showAsideBtn.on("click", function () {
  
        $("#" + $(this).data('show')).toggleClass('show-side-nav');
  
        contents.toggleClass('margin');
  
      });
  
      if ($(window).width() <= 767) {
  
        aside.addClass('show-side-nav');
  
      }
      $(window).on('resize', function () {
  
        if ($(window).width() > 767) {
  
          aside.removeClass('show-side-nav');
  
        }
  
      });
  
      // dropdown menu in the side nav
      var slideNavDropdown = $('.side-nav-dropdown');
  
      $('.side-nav .categories li').on('click', function () {
  
        $(this).toggleClass('opend').siblings().removeClass('opend');
  
        if ($(this).hasClass('opend')) {
  
          $(this).find('.side-nav-dropdown').slideToggle('fast');
  
          $(this).siblings().find('.side-nav-dropdown').slideUp('fast');
  
        } else {
  
          $(this).find('.side-nav-dropdown').slideUp('fast');
  
        }
  
      });
  
      $('.side-nav .close-aside').on('click', function () {
  
        $('#' + $(this).data('close')).addClass('show-side-nav');
  
        contents.removeClass('margin');
  
      });
  
    }());
  
    // Start chart
  
    var chart = document.getElementById('myChart');
    Chart.defaults.global.animation.duration = 2000; // Animation duration
    Chart.defaults.global.title.display = false; // Remove title
    Chart.defaults.global.title.text = "Chart"; // Title
    Chart.defaults.global.title.position = 'bottom'; // Title position
    Chart.defaults.global.defaultFontColor = '#999'; // Font color
    Chart.defaults.global.defaultFontSize = 10; // Font size for every label
  
    // Chart.defaults.global.tooltips.backgroundColor = '#FFF'; // Tooltips background color
    Chart.defaults.global.tooltips.borderColor = 'white'; // Tooltips border color
    Chart.defaults.global.legend.labels.padding = 0;
    Chart.defaults.scale.ticks.beginAtZero = true;
    Chart.defaults.scale.gridLines.zeroLineColor = 'rgba(255, 255, 255, 0.1)';
    Chart.defaults.scale.gridLines.color = 'rgba(255, 255, 255, 0.02)';
  
    Chart.defaults.global.legend.display = false;
  
    var myChart = new Chart(chart, {
      type: 'bar',
      data: {
        labels: ["January", "February", "March", "April", "May", 'Jul'],
        datasets: [{
          label: "Lost",
          fill: false,
          lineTension: 0,
          data: [45, 25, 40, 20, 45, 20],
          pointBorderColor: "#4bc0c0",
          borderColor: '#4bc0c0',
          borderWidth: 2,
          showLine: true,
        }, {
          label: "Succes",
          fill: false,
          lineTension: 0,
          startAngle: 2,
          data: [20, 40, 20, 45, 25, 60],
          // , '#ff6384', '#4bc0c0', '#ffcd56', '#457ba1'
          backgroundColor: "transparent",
          pointBorderColor: "#ff6384",
          borderColor: '#ff6384',
          borderWidth: 2,
          showLine: true,
        }]
      },
    });
    //  Chart ( 2 )
  
  
    var Chart2 = document.getElementById('myChart2').getContext('2d');
    var chart = new Chart(Chart2, {
      type: 'line',
      data: {
        labels: ["January", "February", "March", "April", 'test', 'test', 'test', 'test'],
        datasets: [{
          label: "My First dataset",
          backgroundColor: 'rgb(255, 99, 132)',
          borderColor: 'rgb(255, 79, 116)',
          borderWidth: 2,
          pointBorderColor: false,
          data: [5, 10, 5, 8, 20, 30, 20, 10],
          fill: false,
          lineTension: .4,
        }, {
          label: "Month",
          fill: false,
          lineTension: .4,
          startAngle: 2,
          data: [20, 14, 20, 25, 10, 15, 25, 10],
          // , '#ff6384', '#4bc0c0', '#ffcd56', '#457ba1'
          backgroundColor: "transparent",
          pointBorderColor: "#4bc0c0",
          borderColor: '#4bc0c0',
          borderWidth: 2,
          showLine: true,
        }, {
          label: "Month",
          fill: false,
          lineTension: .4,
          startAngle: 2,
          data: [40, 20, 5, 10, 30, 15, 15, 10],
          // , '#ff6384', '#4bc0c0', '#ffcd56', '#457ba1'
          backgroundColor: "transparent",
          pointBorderColor: "#ffcd56",
          borderColor: '#ffcd56',
          borderWidth: 2,
          showLine: true,
        }]
      },
  
      // Configuration options
      options: {
        title: {
          display: false
        }
      }
    });
  
  
  function showAlert(type, message, duration) {
    if (!message) return false;
    if (!type) type = 'info';
    $("<div class='alert alert-message alert-" +
        type +
        " data-alert alert-dismissible'>" +
        "<button class='close alert-link' data-dismiss='alert'>&times;</button>" +
        message + " </div>").hide().appendTo('body').fadeIn(300);
    if (duration === undefined) {
        duration = 5000;
    }
    if (duration !== false) {
        $(".alert-message").delay(duration).fadeOut(500, function() {
            $(this).remove();
        });
    }
};


  // ...or trigger it using a button
  $('.btn').on("click", function() {
      var type = $(this).data('type');
      var message = $(this).data('message');
      var duration = $(this).data('duration');
      showAlert(type, message, duration);
  });
});