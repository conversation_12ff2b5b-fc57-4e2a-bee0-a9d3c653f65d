$( document ).ready(function() {
    getRegisteredUserList();
});
function getUserByID(user_id){
    fetch(config.hostApi+`/admin/get-by-id/${user_id}`, {
        method: 'get',
        headers: {
            'Content-Type': 'application/json;charset=utf-8'
        }
    })
    .then((response) => {
        return response.json();
    })
    .then(data => {
        const userData = data.result[0]
        console.log(userData);
        document.getElementById('inputFirstName').value = userData.first_name
        document.getElementById('inputLastName').value = userData.last_name
        document.getElementById('inputProfession').value = userData.profession ? userData.profession : "NA"
        document.getElementById('inputEmail').value = userData.email_id
        document.getElementById('inputMobile').value = userData.phone_number
        document.getElementById('inputStreet').value = userData.address
        document.getElementById('inputCity').value = userData.city
        document.getElementById('inputProvince').value = userData.province
        document.getElementById('inputPostalcode').value = userData.postal_code
        document.getElementById('user-id').value = userData.id
     });
}
async function archiveUser(event){
    let userID =  document.getElementById('user-id').value
    console.log('delete',userID)
    let resp = await fetch(config.hostApi+"/admin/archive",{
        method : "POST",
        headers : { "Content-Type" : "application/json" },
        body : JSON.stringify({id : userID})
    })
    let data = await resp.json()
    if(data.status){
        alert("User archived")
        window.location.reload()
    }
    else{
        console.log(data.error)
    }
}
async function reactivateUser(event){
    let userID =  document.getElementById('user-id').value
    console.log('delete',userID)
    let resp = await fetch(config.hostApi+"/admin/reactivate",{
        method : "POST",
        headers : { "Content-Type" : "application/json" },
        body : JSON.stringify({id : userID})
    })
    let data = await resp.json()
    if(data.status){
        alert("User activated")
        window.location.reload()
    }
    else{
        console.log(data.error)
    }
}
async function updateUser(event){
    let table = null
    let e = event || window.event
    e.preventDefault()
    let obj = {
        "id" : document.getElementById('user-id').value,
        "phone_number" : document.getElementById('inputMobile').value,
        "address" : document.getElementById('inputStreet').value,
        "city" : document.getElementById('inputCity').value,
        "profession" : document.getElementById('inputProfession').value
    }
    console.log(obj)
    await fetch(config.hostApi+"/admin/update-user",{
        method : "POST",
        headers : { "Content-Type" : "application/json" },
        body : JSON.stringify(obj)
    })
    if ( $.fn.dataTable.isDataTable('#userList') ) {
        table = $('#userList').DataTable();
    }
    let resp = await fetch(config.hostApi+"/admin/get-users",{
        method : "GET"
    })
    alert("User Successfully updated")
    let data = await resp.json()
    console.log(data)
    if(data){
        table.clear().draw();
        table.rows.add(data.result.map(x=>{
            //console.log(x);
            return {
                firstName:x.first_name ? x.first_name : "NA",
                lastName:x.last_name ? x.last_name : "NA",  
                profession:x.profession ? x.profession : "NA",
                email:x.email_id ? x.email_id : "NA",
                phoneNumber:x.phone_number ? x.phone_number : "NA",
                address : x.address ? x.address : "NA",
                city:x.city? x.city : "NA",
                province : x.province? x.province : "NA",
                postalCode : x.postal_code? x.postal_code : "NA",
                user_id : x.id ? parseInt(x.id) : -1,
                last_login : x.last_login ? x.last_login.split("T")[0] : "NA",
                is_active : x.is_active ? "Active" : "Inactive"
            }
        }));
        table.columns.adjust().draw();
    }
}
function getRegisteredUserList(){
    getApi(config.hostApi+"/admin/get-users",null).then(res=>{
       $("#userList").DataTable({
           searching: false,
           scrollX : true,
           dom: 'Bfrtip',
           buttons: [ "excel"],
               data:res.result.map(x=>{
                        //console.log(x);
                        return {
                            firstName:x.first_name ? x.first_name : "NA",
                            lastName:x.last_name ? x.last_name : "NA",  
                            profession:x.profession ? x.profession : "NA",
                            email:x.email_id ? x.email_id : "NA",
                            phoneNumber:x.phone_number ? x.phone_number : "NA",
                            address : x.address ? x.address : "NA",
                            city:x.city? x.city : "NA",
                            province : x.province? x.province : "NA",
                            postalCode : x.postal_code? x.postal_code : "NA",
                            user_id : x.id ? parseInt(x.id) : -1,
                            last_login : x.last_login ? x.last_login.split("T")[0] : "NA",
                            is_active : x.is_active ? "Active" : "Inactive"
                        }
                    }),
                    columns: [
                    {
                        data:"firstName",
                        title:"First Name"
                    },{
                        data:"lastName",
                        title:"Last Name"
                    },{
                       data:"profession",
                       title:"Profession",
                   },{
                        data:"email",
                        title:"Email"
                    },{
                        data:"phoneNumber",
                        title:"Contact Number"
                    },{
                       data:"address",
                       title:"Appt/ Street Name"
                   },{
                       data:"city",
                       title:"City"
                   },{
                       data:"province",
                       title:"Province"
                   },{
                        data:"postalCode",
                        title:"Postal Code" 
                    },
                    {
                        data : "last_login",
                        title : "Last Logged In"
                    },{
                        data : "is_active",
                        title : "User Active",
                    },
                    {
                        title: "Update",
                        class: "table-first-name",
                        render : function (data, type, row){
                            return '<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter" onclick=getUserByID('+row.user_id+')>Update</button>';
                        }
                    }
                ]
       });
    })
}
