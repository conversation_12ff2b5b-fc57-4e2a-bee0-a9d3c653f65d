!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";function n(e,t,n){n=n||ut;var r,o,i=n.createElement("script");if(i.text=e,t)for(r in Ct)o=t[r]||t.getAttribute&&t.getAttribute(r),o&&i.setAttribute(r,o);n.head.appendChild(i).parentNode.removeChild(i)}function r(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?ht[gt.call(e)]||"object":typeof e}function o(e){var t=!!e&&"length"in e&&e.length,n=r(e);return bt(e)||wt(e)?!1:"array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e}function i(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}function a(e,t,n){return bt(t)?St.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?St.grep(e,function(e){return e===t!==n}):"string"!=typeof t?St.grep(e,function(e){return dt.call(t,e)>-1!==n}):St.filter(t,e,n)}function s(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}function u(e){var t={};return St.each(e.match(Ot)||[],function(e,n){t[n]=!0}),t}function l(e){return e}function c(e){throw e}function f(e,t,n,r){var o;try{e&&bt(o=e.promise)?o.call(e).done(t).fail(n):e&&bt(o=e.then)?o.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}function p(){ut.removeEventListener("DOMContentLoaded",p),e.removeEventListener("load",p),St.ready()}function d(e,t){return t.toUpperCase()}function h(e){return e.replace($t,"ms-").replace(Ft,d)}function g(){this.expando=St.expando+g.uid++}function m(e){return"true"===e?!0:"false"===e?!1:"null"===e?null:e===+e+""?+e:zt.test(e)?JSON.parse(e):e}function y(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(Vt,"-$&").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n=m(n)}catch(o){}Ut.set(e,t,n)}else n=void 0;return n}function v(e,t,n,r){var o,i,a=20,s=r?function(){return r.cur()}:function(){return St.css(e,t,"")},u=s(),l=n&&n[3]||(St.cssNumber[t]?"":"px"),c=e.nodeType&&(St.cssNumber[t]||"px"!==l&&+u)&&Jt.exec(St.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;)St.style(e,t,c+l),(1-i)*(1-(i=s()/u||.5))<=0&&(a=0),c/=i;c=2*c,St.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,o=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=o)),o}function x(e){var t,n=e.ownerDocument,r=e.nodeName,o=tn[r];return o?o:(t=n.body.appendChild(n.createElement(r)),o=St.css(t,"display"),t.parentNode.removeChild(t),"none"===o&&(o="block"),tn[r]=o,o)}function b(e,t){for(var n,r,o=[],i=0,a=e.length;a>i;i++)r=e[i],r.style&&(n=r.style.display,t?("none"===n&&(o[i]=Wt.get(r,"display")||null,o[i]||(r.style.display="")),""===r.style.display&&Zt(r)&&(o[i]=x(r))):"none"!==n&&(o[i]="none",Wt.set(r,"display",n)));for(i=0;a>i;i++)null!=o[i]&&(e[i].style.display=o[i]);return e}function w(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&i(e,t)?St.merge([e],n):n}function C(e,t){for(var n=0,r=e.length;r>n;n++)Wt.set(e[n],"globalEval",!t||Wt.get(t[n],"globalEval"))}function T(e,t,n,o,i){for(var a,s,u,l,c,f,p=t.createDocumentFragment(),d=[],h=0,g=e.length;g>h;h++)if(a=e[h],a||0===a)if("object"===r(a))St.merge(d,a.nodeType?[a]:a);else if(sn.test(a)){for(s=s||p.appendChild(t.createElement("div")),u=(rn.exec(a)||["",""])[1].toLowerCase(),l=an[u]||an._default,s.innerHTML=l[1]+St.htmlPrefilter(a)+l[2],f=l[0];f--;)s=s.lastChild;St.merge(d,s.childNodes),s=p.firstChild,s.textContent=""}else d.push(t.createTextNode(a));for(p.textContent="",h=0;a=d[h++];)if(o&&St.inArray(a,o)>-1)i&&i.push(a);else if(c=Qt(a),s=w(p.appendChild(a),"script"),c&&C(s),n)for(f=0;a=s[f++];)on.test(a.type||"")&&n.push(a);return p}function S(){return!0}function k(){return!1}function E(e,t){return e===A()==("focus"===t)}function A(){try{return ut.activeElement}catch(e){}}function N(e,t,n,r,o,i){var a,s;if("object"==typeof t){"string"!=typeof n&&(r=r||n,n=void 0);for(s in t)N(e,s,n,r,t[s],i);return e}if(null==r&&null==o?(o=n,r=n=void 0):null==o&&("string"==typeof n?(o=r,r=void 0):(o=r,r=n,n=void 0)),o===!1)o=k;else if(!o)return e;return 1===i&&(a=o,o=function(e){return St().off(e),a.apply(this,arguments)},o.guid=a.guid||(a.guid=St.guid++)),e.each(function(){St.event.add(this,t,o,r,n)})}function D(e,t,n){return n?(Wt.set(e,t,!1),void St.event.add(e,t,{namespace:!1,handler:function(e){var r,o,i=Wt.get(this,t);if(1&e.isTrigger&&this[t]){if(i.length)(St.event.special[t]||{}).delegateType&&e.stopPropagation();else if(i=ct.call(arguments),Wt.set(this,t,i),r=n(this,t),this[t](),o=Wt.get(this,t),i!==o||r?Wt.set(this,t,!1):o={},i!==o)return e.stopImmediatePropagation(),e.preventDefault(),o.value}else i.length&&(Wt.set(this,t,{value:St.event.trigger(St.extend(i[0],St.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void(void 0===Wt.get(e,t)&&St.event.add(e,t,S))}function j(e,t){return i(e,"table")&&i(11!==t.nodeType?t:t.firstChild,"tr")?St(e).children("tbody")[0]||e:e}function _(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function L(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function R(e,t){var n,r,o,i,a,s,u,l;if(1===t.nodeType){if(Wt.hasData(e)&&(i=Wt.access(e),a=Wt.set(t,i),l=i.events)){delete a.handle,a.events={};for(o in l)for(n=0,r=l[o].length;r>n;n++)St.event.add(t,o,l[o][n])}Ut.hasData(e)&&(s=Ut.access(e),u=St.extend({},s),Ut.set(t,u))}}function q(e,t){var n=t.nodeName.toLowerCase();"input"===n&&nn.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function P(e,t,r,o){t=ft.apply([],t);var i,a,s,u,l,c,f=0,p=e.length,d=p-1,h=t[0],g=bt(h);if(g||p>1&&"string"==typeof h&&!xt.checkClone&&dn.test(h))return e.each(function(n){var i=e.eq(n);g&&(t[0]=h.call(this,n,i.html())),P(i,t,r,o)});if(p&&(i=T(t,e[0].ownerDocument,!1,e,o),a=i.firstChild,1===i.childNodes.length&&(i=a),a||o)){for(s=St.map(w(i,"script"),_),u=s.length;p>f;f++)l=i,f!==d&&(l=St.clone(l,!0,!0),u&&St.merge(s,w(l,"script"))),r.call(e[f],l,f);if(u)for(c=s[s.length-1].ownerDocument,St.map(s,L),f=0;u>f;f++)l=s[f],on.test(l.type||"")&&!Wt.access(l,"globalEval")&&St.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?St._evalUrl&&!l.noModule&&St._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")}):n(l.textContent.replace(hn,""),l,c))}return e}function O(e,t,n){for(var r,o=t?St.filter(t,e):e,i=0;null!=(r=o[i]);i++)n||1!==r.nodeType||St.cleanData(w(r)),r.parentNode&&(n&&Qt(r)&&C(w(r,"script")),r.parentNode.removeChild(r));return e}function H(e,t,n){var r,o,i,a,s=e.style;return n=n||mn(e),n&&(a=n.getPropertyValue(t)||n[t],""!==a||Qt(e)||(a=St.style(e,t)),!xt.pixelBoxStyles()&&gn.test(a)&&yn.test(t)&&(r=s.width,o=s.minWidth,i=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=o,s.maxWidth=i)),void 0!==a?a+"":a}function I(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function M(e){for(var t=e[0].toUpperCase()+e.slice(1),n=vn.length;n--;)if(e=vn[n]+t,e in xn)return e}function $(e){var t=St.cssProps[e]||bn[e];return t?t:e in xn?e:bn[e]=M(e)||e}function F(e,t,n){var r=Jt.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function B(e,t,n,r,o,i){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;4>a;a+=2)"margin"===n&&(u+=St.css(e,n+Yt[a],!0,o)),r?("content"===n&&(u-=St.css(e,"padding"+Yt[a],!0,o)),"margin"!==n&&(u-=St.css(e,"border"+Yt[a]+"Width",!0,o))):(u+=St.css(e,"padding"+Yt[a],!0,o),"padding"!==n?u+=St.css(e,"border"+Yt[a]+"Width",!0,o):s+=St.css(e,"border"+Yt[a]+"Width",!0,o));return!r&&i>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-i-u-s-.5))||0),u}function W(e,t,n){var r=mn(e),o=!xt.boxSizingReliable()||n,i=o&&"border-box"===St.css(e,"boxSizing",!1,r),a=i,s=H(e,t,r),u="offset"+t[0].toUpperCase()+t.slice(1);if(gn.test(s)){if(!n)return s;s="auto"}return(!xt.boxSizingReliable()&&i||"auto"===s||!parseFloat(s)&&"inline"===St.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===St.css(e,"boxSizing",!1,r),a=u in e,a&&(s=e[u])),s=parseFloat(s)||0,s+B(e,t,n||(i?"border":"content"),a,r,s)+"px"}function U(e,t,n,r,o){return new U.prototype.init(e,t,n,r,o)}function z(){En&&(ut.hidden===!1&&e.requestAnimationFrame?e.requestAnimationFrame(z):e.setTimeout(z,St.fx.interval),St.fx.tick())}function V(){return e.setTimeout(function(){kn=void 0}),kn=Date.now()}function X(e,t){var n,r=0,o={height:e};for(t=t?1:0;4>r;r+=2-t)n=Yt[r],o["margin"+n]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function J(e,t,n){for(var r,o=(Q.tweeners[t]||[]).concat(Q.tweeners["*"]),i=0,a=o.length;a>i;i++)if(r=o[i].call(n,t,e))return r}function Y(e,t,n){var r,o,i,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&Zt(e),m=Wt.get(e,"fxshow");n.queue||(a=St._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,St.queue(e,"fx").length||a.empty.fire()})}));for(r in t)if(o=t[r],An.test(o)){if(delete t[r],i=i||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[r])continue;g=!0}d[r]=m&&m[r]||St.style(e,r)}if(u=!St.isEmptyObject(t),u||!St.isEmptyObject(d)){f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],l=m&&m.display,null==l&&(l=Wt.get(e,"display")),c=St.css(e,"display"),"none"===c&&(l?c=l:(b([e],!0),l=e.style.display||l,c=St.css(e,"display"),b([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===St.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1;for(r in d)u||(m?"hidden"in m&&(g=m.hidden):m=Wt.access(e,"fxshow",{display:l}),i&&(m.hidden=!g),g&&b([e],!0),p.done(function(){g||b([e]),Wt.remove(e,"fxshow");for(r in d)St.style(e,r,d[r])})),u=J(g?m[r]:0,r,p),r in m||(m[r]=u.start,g&&(u.end=u.start,u.start=0))}}function G(e,t){var n,r,o,i,a;for(n in e)if(r=h(n),o=t[r],i=e[n],Array.isArray(i)&&(o=i[1],i=e[n]=i[0]),n!==r&&(e[r]=i,delete e[n]),a=St.cssHooks[r],a&&"expand"in a){i=a.expand(i),delete e[r];for(n in i)n in e||(e[n]=i[n],t[n]=o)}else t[r]=o}function Q(e,t,n){var r,o,i=0,a=Q.prefilters.length,s=St.Deferred().always(function(){delete u.elem}),u=function(){if(o)return!1;for(var t=kn||V(),n=Math.max(0,l.startTime+l.duration-t),r=n/l.duration||0,i=1-r,a=0,u=l.tweens.length;u>a;a++)l.tweens[a].run(i);return s.notifyWith(e,[l,i,n]),1>i&&u?n:(u||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:St.extend({},t),opts:St.extend(!0,{specialEasing:{},easing:St.easing._default},n),originalProperties:t,originalOptions:n,startTime:kn||V(),duration:n.duration,tweens:[],createTween:function(t,n){var r=St.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(o)return this;for(o=!0;r>n;n++)l.tweens[n].run(1);return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(G(c,l.opts.specialEasing);a>i;i++)if(r=Q.prefilters[i].call(l,e,c,l.opts))return bt(r.stop)&&(St._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return St.map(c,J,l),bt(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),St.fx.timer(St.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}function K(e){var t=e.match(Ot)||[];return t.join(" ")}function Z(e){return e.getAttribute&&e.getAttribute("class")||""}function et(e){return Array.isArray(e)?e:"string"==typeof e?e.match(Ot)||[]:[]}function tt(e,t,n,o){var i;if(Array.isArray(t))St.each(t,function(t,r){n||Mn.test(e)?o(e,r):tt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,o)});else if(n||"object"!==r(t))o(e,t);else for(i in t)tt(e+"["+i+"]",t[i],n,o)}function nt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,o=0,i=t.toLowerCase().match(Ot)||[];if(bt(n))for(;r=i[o++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function rt(e,t,n,r){function o(s){var u;return i[s]=!0,St.each(e[s]||[],function(e,s){var l=s(t,n,r);return"string"!=typeof l||a||i[l]?a?!(u=l):void 0:(t.dataTypes.unshift(l),o(l),!1)}),u}var i={},a=e===Qn;return o(t.dataTypes[0])||!i["*"]&&o("*")}function ot(e,t){var n,r,o=St.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:r||(r={}))[n]=t[n]);return r&&St.extend(!0,e,r),e}function it(e,t,n){for(var r,o,i,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(o in s)if(s[o]&&s[o].test(r)){u.unshift(o);break}if(u[0]in n)i=u[0];else{for(o in n){if(!u[0]||e.converters[o+" "+u[0]]){i=o;break}a||(a=o)}i=i||a}return i?(i!==u[0]&&u.unshift(i),n[i]):void 0}function at(e,t,n,r){var o,i,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(i=c.shift();i;)if(e.responseFields[i]&&(n[e.responseFields[i]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=i,i=c.shift())if("*"===i)i=u;else if("*"!==u&&u!==i){if(a=l[u+" "+i]||l["* "+i],!a)for(o in l)if(s=o.split(" "),s[1]===i&&(a=l[u+" "+s[0]]||l["* "+s[0]])){a===!0?a=l[o]:l[o]!==!0&&(i=s[0],c.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(f){return{state:"parsererror",error:a?f:"No conversion from "+u+" to "+i}}}return{state:"success",data:t}}var st=[],ut=e.document,lt=Object.getPrototypeOf,ct=st.slice,ft=st.concat,pt=st.push,dt=st.indexOf,ht={},gt=ht.toString,mt=ht.hasOwnProperty,yt=mt.toString,vt=yt.call(Object),xt={},bt=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},wt=function(e){return null!=e&&e===e.window},Ct={type:!0,src:!0,nonce:!0,noModule:!0},Tt="3.4.1",St=function(e,t){return new St.fn.init(e,t)},kt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;St.fn=St.prototype={jquery:Tt,constructor:St,length:0,toArray:function(){return ct.call(this)},get:function(e){return null==e?ct.call(this):0>e?this[e+this.length]:this[e]},pushStack:function(e){var t=St.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return St.each(this,e)},map:function(e){return this.pushStack(St.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(ct.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:pt,sort:st.sort,splice:st.splice},St.extend=St.fn.extend=function(){var e,t,n,r,o,i,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||bt(a)||(a={}),s===u&&(a=this,s--);u>s;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(St.isPlainObject(r)||(o=Array.isArray(r)))?(n=a[t],i=o&&!Array.isArray(n)?[]:o||St.isPlainObject(n)?n:{},o=!1,a[t]=St.extend(l,i,r)):void 0!==r&&(a[t]=r));return a},St.extend({expando:"jQuery"+(Tt+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return e&&"[object Object]"===gt.call(e)?(t=lt(e))?(n=mt.call(t,"constructor")&&t.constructor,"function"==typeof n&&yt.call(n)===vt):!0:!1},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t){n(e,{nonce:t&&t.nonce})},each:function(e,t){var n,r=0;if(o(e))for(n=e.length;n>r&&t.call(e[r],r,e[r])!==!1;r++);else for(r in e)if(t.call(e[r],r,e[r])===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(kt,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(o(Object(e))?St.merge(n,"string"==typeof e?[e]:e):pt.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:dt.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,o=e.length;n>r;r++)e[o++]=t[r];return e.length=o,e},grep:function(e,t,n){for(var r,o=[],i=0,a=e.length,s=!n;a>i;i++)r=!t(e[i],i),r!==s&&o.push(e[i]);return o},map:function(e,t,n){var r,i,a=0,s=[];if(o(e))for(r=e.length;r>a;a++)i=t(e[a],a,n),null!=i&&s.push(i);else for(a in e)i=t(e[a],a,n),null!=i&&s.push(i);return ft.apply([],s)},guid:1,support:xt}),"function"==typeof Symbol&&(St.fn[Symbol.iterator]=st[Symbol.iterator]),St.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){ht["[object "+t+"]"]=t.toLowerCase()});var Et=function(e){function t(e,t,n,r){var o,i,a,s,u,l,c,p=t&&t.ownerDocument,h=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==h&&9!==h&&11!==h)return n;if(!r&&((t?t.ownerDocument||t:F)!==R&&L(t),t=t||R,P)){if(11!==h&&(u=xt.exec(e)))if(o=u[1]){if(9===h){if(!(a=t.getElementById(o)))return n;if(a.id===o)return n.push(a),n}else if(p&&(a=p.getElementById(o))&&M(t,a)&&a.id===o)return n.push(a),n}else{if(u[2])return Z.apply(n,t.getElementsByTagName(e)),n;if((o=u[3])&&C.getElementsByClassName&&t.getElementsByClassName)return Z.apply(n,t.getElementsByClassName(o)),n}if(!(!C.qsa||X[e+" "]||O&&O.test(e)||1===h&&"object"===t.nodeName.toLowerCase())){if(c=e,p=t,1===h&&ft.test(e)){for((s=t.getAttribute("id"))?s=s.replace(Tt,St):t.setAttribute("id",s=$),l=E(e),i=l.length;i--;)l[i]="#"+s+" "+d(l[i]);c=l.join(","),p=bt.test(e)&&f(t.parentNode)||t}try{return Z.apply(n,p.querySelectorAll(c)),n}catch(g){X(e,!0)}finally{s===$&&t.removeAttribute("id")}}}return N(e.replace(ut,"$1"),t,n,r)}function n(){function e(n,r){return t.push(n+" ")>T.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function r(e){return e[$]=!0,e}function o(e){var t=R.createElement("fieldset");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function i(e,t){for(var n=e.split("|"),r=n.length;r--;)T.attrHandle[n[r]]=t}function a(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function u(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function l(e){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&Et(t)===e:t.disabled===e:"label"in t?t.disabled===e:!1}}function c(e){return r(function(t){return t=+t,r(function(n,r){for(var o,i=e([],n.length,t),a=i.length;a--;)n[o=i[a]]&&(n[o]=!(r[o]=n[o]))})})}function f(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function p(){}function d(e){for(var t=0,n=e.length,r="";n>t;t++)r+=e[t].value;return r}function h(e,t,n){var r=t.dir,o=t.next,i=o||r,a=n&&"parentNode"===i,s=W++;return t.first?function(t,n,o){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,o);return!1}:function(t,n,u){var l,c,f,p=[B,s];if(u){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,u))return!0}else for(;t=t[r];)if(1===t.nodeType||a)if(f=t[$]||(t[$]={}),c=f[t.uniqueID]||(f[t.uniqueID]={}),o&&o===t.nodeName.toLowerCase())t=t[r]||t;else{if((l=c[i])&&l[0]===B&&l[1]===s)return p[2]=l[2];if(c[i]=p,p[2]=e(t,n,u))return!0}return!1}}function g(e){return e.length>1?function(t,n,r){for(var o=e.length;o--;)if(!e[o](t,n,r))return!1;return!0}:e[0]}function m(e,n,r){for(var o=0,i=n.length;i>o;o++)t(e,n[o],r);return r}function y(e,t,n,r,o){for(var i,a=[],s=0,u=e.length,l=null!=t;u>s;s++)(i=e[s])&&(!n||n(i,r,o))&&(a.push(i),l&&t.push(s));return a}function v(e,t,n,o,i,a){return o&&!o[$]&&(o=v(o)),i&&!i[$]&&(i=v(i,a)),r(function(r,a,s,u){var l,c,f,p=[],d=[],h=a.length,g=r||m(t||"*",s.nodeType?[s]:s,[]),v=!e||!r&&t?g:y(g,p,e,s,u),x=n?i||(r?e:h||o)?[]:a:v;if(n&&n(v,x,s,u),o)for(l=y(x,d),o(l,[],s,u),c=l.length;c--;)(f=l[c])&&(x[d[c]]=!(v[d[c]]=f));if(r){if(i||e){if(i){for(l=[],c=x.length;c--;)(f=x[c])&&l.push(v[c]=f);i(null,x=[],l,u)}for(c=x.length;c--;)(f=x[c])&&(l=i?tt(r,f):p[c])>-1&&(r[l]=!(a[l]=f))}}else x=y(x===a?x.splice(h,x.length):x),i?i(null,a,x,u):Z.apply(a,x)})}function x(e){for(var t,n,r,o=e.length,i=T.relative[e[0].type],a=i||T.relative[" "],s=i?1:0,u=h(function(e){return e===t},a,!0),l=h(function(e){return tt(t,e)>-1},a,!0),c=[function(e,n,r){var o=!i&&(r||n!==D)||((t=n).nodeType?u(e,n,r):l(e,n,r));return t=null,o}];o>s;s++)if(n=T.relative[e[s].type])c=[h(g(c),n)];else{if(n=T.filter[e[s].type].apply(null,e[s].matches),n[$]){for(r=++s;o>r&&!T.relative[e[r].type];r++);return v(s>1&&g(c),s>1&&d(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(ut,"$1"),n,r>s&&x(e.slice(s,r)),o>r&&x(e=e.slice(r)),o>r&&d(e))}c.push(n)}return g(c)}function b(e,n){var o=n.length>0,i=e.length>0,a=function(r,a,s,u,l){var c,f,p,d=0,h="0",g=r&&[],m=[],v=D,x=r||i&&T.find.TAG("*",l),b=B+=null==v?1:Math.random()||.1,w=x.length;for(l&&(D=a===R||a||l);h!==w&&null!=(c=x[h]);h++){if(i&&c){for(f=0,a||c.ownerDocument===R||(L(c),s=!P);p=e[f++];)if(p(c,a||R,s)){u.push(c);break}l&&(B=b)}o&&((c=!p&&c)&&d--,r&&g.push(c))}if(d+=h,o&&h!==d){for(f=0;p=n[f++];)p(g,m,a,s);if(r){if(d>0)for(;h--;)g[h]||m[h]||(m[h]=Q.call(u));m=y(m)}Z.apply(u,m),l&&!r&&m.length>0&&d+n.length>1&&t.uniqueSort(u)}return l&&(B=b,D=v),g};return o?r(a):a}var w,C,T,S,k,E,A,N,D,j,_,L,R,q,P,O,H,I,M,$="sizzle"+1*new Date,F=e.document,B=0,W=0,U=n(),z=n(),V=n(),X=n(),J=function(e,t){return e===t&&(_=!0),0},Y={}.hasOwnProperty,G=[],Q=G.pop,K=G.push,Z=G.push,et=G.slice,tt=function(e,t){for(var n=0,r=e.length;r>n;n++)if(e[n]===t)return n;return-1},nt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",rt="[\\x20\\t\\r\\n\\f]",ot="(?:\\\\.|[\\w-]|[^\x00-\\xa0])+",it="\\["+rt+"*("+ot+")(?:"+rt+"*([*^$|!~]?=)"+rt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ot+"))|)"+rt+"*\\]",at=":("+ot+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+it+")*)|.*)\\)|)",st=new RegExp(rt+"+","g"),ut=new RegExp("^"+rt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+rt+"+$","g"),lt=new RegExp("^"+rt+"*,"+rt+"*"),ct=new RegExp("^"+rt+"*([>+~]|"+rt+")"+rt+"*"),ft=new RegExp(rt+"|>"),pt=new RegExp(at),dt=new RegExp("^"+ot+"$"),ht={ID:new RegExp("^#("+ot+")"),CLASS:new RegExp("^\\.("+ot+")"),TAG:new RegExp("^("+ot+"|[*])"),ATTR:new RegExp("^"+it),PSEUDO:new RegExp("^"+at),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+rt+"*(even|odd|(([+-]|)(\\d*)n|)"+rt+"*(?:([+-]|)"+rt+"*(\\d+)|))"+rt+"*\\)|)","i"),bool:new RegExp("^(?:"+nt+")$","i"),needsContext:new RegExp("^"+rt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+rt+"*((?:-\\d)?\\d*)"+rt+"*\\)|)(?=[^-]|$)","i")},gt=/HTML$/i,mt=/^(?:input|select|textarea|button)$/i,yt=/^h\d$/i,vt=/^[^{]+\{\s*\[native \w/,xt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,bt=/[+~]/,wt=new RegExp("\\\\([\\da-f]{1,6}"+rt+"?|("+rt+")|.)","ig"),Ct=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:0>r?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},Tt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,St=function(e,t){return t?"\x00"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},kt=function(){L()},Et=h(function(e){return e.disabled===!0&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{Z.apply(G=et.call(F.childNodes),F.childNodes),G[F.childNodes.length].nodeType}catch(At){Z={apply:G.length?function(e,t){K.apply(e,et.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}C=t.support={},k=t.isXML=function(e){var t=e.namespaceURI,n=(e.ownerDocument||e).documentElement;return!gt.test(t||n&&n.nodeName||"HTML")},L=t.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:F;return r!==R&&9===r.nodeType&&r.documentElement?(R=r,q=R.documentElement,P=!k(R),F!==R&&(n=R.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",kt,!1):n.attachEvent&&n.attachEvent("onunload",kt)),C.attributes=o(function(e){return e.className="i",!e.getAttribute("className")}),C.getElementsByTagName=o(function(e){return e.appendChild(R.createComment("")),!e.getElementsByTagName("*").length}),C.getElementsByClassName=vt.test(R.getElementsByClassName),C.getById=o(function(e){return q.appendChild(e).id=$,!R.getElementsByName||!R.getElementsByName($).length}),C.getById?(T.filter.ID=function(e){var t=e.replace(wt,Ct);return function(e){return e.getAttribute("id")===t}},T.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&P){var n=t.getElementById(e);return n?[n]:[]}}):(T.filter.ID=function(e){var t=e.replace(wt,Ct);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},T.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&P){var n,r,o,i=t.getElementById(e);if(i){if(n=i.getAttributeNode("id"),n&&n.value===e)return[i];for(o=t.getElementsByName(e),r=0;i=o[r++];)if(n=i.getAttributeNode("id"),n&&n.value===e)return[i]}return[]}}),T.find.TAG=C.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):C.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"===e){for(;n=i[o++];)1===n.nodeType&&r.push(n);return r}return i},T.find.CLASS=C.getElementsByClassName&&function(e,t){return"undefined"!=typeof t.getElementsByClassName&&P?t.getElementsByClassName(e):void 0},H=[],O=[],(C.qsa=vt.test(R.querySelectorAll))&&(o(function(e){q.appendChild(e).innerHTML="<a id='"+$+"'></a><select id='"+$+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&O.push("[*^$]="+rt+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||O.push("\\["+rt+"*(?:value|"+nt+")"),e.querySelectorAll("[id~="+$+"-]").length||O.push("~="),e.querySelectorAll(":checked").length||O.push(":checked"),e.querySelectorAll("a#"+$+"+*").length||O.push(".#.+[+~]")}),o(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=R.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&O.push("name"+rt+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&O.push(":enabled",":disabled"),q.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&O.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),O.push(",.*:")})),(C.matchesSelector=vt.test(I=q.matches||q.webkitMatchesSelector||q.mozMatchesSelector||q.oMatchesSelector||q.msMatchesSelector))&&o(function(e){C.disconnectedMatch=I.call(e,"*"),I.call(e,"[s!='']:x"),H.push("!=",at)}),O=O.length&&new RegExp(O.join("|")),H=H.length&&new RegExp(H.join("|")),t=vt.test(q.compareDocumentPosition),M=t||vt.test(q.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},J=t?function(e,t){if(e===t)return _=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!C.sortDetached&&t.compareDocumentPosition(e)===n?e===R||e.ownerDocument===F&&M(F,e)?-1:t===R||t.ownerDocument===F&&M(F,t)?1:j?tt(j,e)-tt(j,t):0:4&n?-1:1)}:function(e,t){if(e===t)return _=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,s=[e],u=[t];if(!o||!i)return e===R?-1:t===R?1:o?-1:i?1:j?tt(j,e)-tt(j,t):0;if(o===i)return a(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;s[r]===u[r];)r++;return r?a(s[r],u[r]):s[r]===F?-1:u[r]===F?1:0},R):R},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==R&&L(e),!(!C.matchesSelector||!P||X[n+" "]||H&&H.test(n)||O&&O.test(n)))try{var r=I.call(e,n);if(r||C.disconnectedMatch||e.document&&11!==e.document.nodeType)return r}catch(o){X(n,!0)}return t(n,R,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==R&&L(e),M(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==R&&L(e);var n=T.attrHandle[t.toLowerCase()],r=n&&Y.call(T.attrHandle,t.toLowerCase())?n(e,t,!P):void 0;return void 0!==r?r:C.attributes||!P?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},t.escape=function(e){return(e+"").replace(Tt,St)},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],r=0,o=0;if(_=!C.detectDuplicates,j=!C.sortStable&&e.slice(0),e.sort(J),_){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return j=null,e},S=t.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=S(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=S(t);return n},T=t.selectors={cacheLength:50,createPseudo:r,match:ht,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(wt,Ct),e[3]=(e[3]||e[4]||e[5]||"").replace(wt,Ct),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return ht.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pt.test(n)&&(t=E(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(wt,Ct).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=U[e+" "];return t||(t=new RegExp("(^|"+rt+")"+e+"("+rt+"|$)"))&&U(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(o){var i=t.attr(o,e);return null==i?"!="===n:n?(i+="","="===n?i===r:"!="===n?i!==r:"^="===n?r&&0===i.indexOf(r):"*="===n?r&&i.indexOf(r)>-1:"$="===n?r&&i.slice(-r.length)===r:"~="===n?(" "+i.replace(st," ")+" ").indexOf(r)>-1:"|="===n?i===r||i.slice(0,r.length+1)===r+"-":!1):!0}},CHILD:function(e,t,n,r,o){var i="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===o?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h,g=i!==a?"nextSibling":"previousSibling",m=t.parentNode,y=s&&t.nodeName.toLowerCase(),v=!u&&!s,x=!1;if(m){if(i){for(;g;){for(p=t;p=p[g];)if(s?p.nodeName.toLowerCase()===y:1===p.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?m.firstChild:m.lastChild],a&&v){for(p=m,f=p[$]||(p[$]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),l=c[e]||[],d=l[0]===B&&l[1],x=d&&l[2],p=d&&m.childNodes[d];p=++d&&p&&p[g]||(x=d=0)||h.pop();)if(1===p.nodeType&&++x&&p===t){c[e]=[B,d,x];break}}else if(v&&(p=t,f=p[$]||(p[$]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),l=c[e]||[],d=l[0]===B&&l[1],x=d),x===!1)for(;(p=++d&&p&&p[g]||(x=d=0)||h.pop())&&((s?p.nodeName.toLowerCase()!==y:1!==p.nodeType)||!++x||(v&&(f=p[$]||(p[$]={}),c=f[p.uniqueID]||(f[p.uniqueID]={}),c[e]=[B,x]),p!==t)););return x-=o,x===r||x%r===0&&x/r>=0}}},PSEUDO:function(e,n){var o,i=T.pseudos[e]||T.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return i[$]?i(n):i.length>1?(o=[e,e,"",n],T.setFilters.hasOwnProperty(e.toLowerCase())?r(function(e,t){for(var r,o=i(e,n),a=o.length;a--;)r=tt(e,o[a]),e[r]=!(t[r]=o[a])}):function(e){return i(e,0,o)
}):i}},pseudos:{not:r(function(e){var t=[],n=[],o=A(e.replace(ut,"$1"));return o[$]?r(function(e,t,n,r){for(var i,a=o(e,null,r,[]),s=e.length;s--;)(i=a[s])&&(e[s]=!(t[s]=i))}):function(e,r,i){return t[0]=e,o(t,null,i,n),t[0]=null,!n.pop()}}),has:r(function(e){return function(n){return t(e,n).length>0}}),contains:r(function(e){return e=e.replace(wt,Ct),function(t){return(t.textContent||S(t)).indexOf(e)>-1}}),lang:r(function(e){return dt.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(wt,Ct).toLowerCase(),function(t){var n;do if(n=P?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===q},focus:function(e){return e===R.activeElement&&(!R.hasFocus||R.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:l(!1),disabled:l(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!T.pseudos.empty(e)},header:function(e){return yt.test(e.nodeName)},input:function(e){return mt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[0>n?n+t:n]}),even:c(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:c(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:c(function(e,t,n){for(var r=0>n?n+t:n>t?t:n;--r>=0;)e.push(r);return e}),gt:c(function(e,t,n){for(var r=0>n?n+t:n;++r<t;)e.push(r);return e})}},T.pseudos.nth=T.pseudos.eq;for(w in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})T.pseudos[w]=s(w);for(w in{submit:!0,reset:!0})T.pseudos[w]=u(w);return p.prototype=T.filters=T.pseudos,T.setFilters=new p,E=t.tokenize=function(e,n){var r,o,i,a,s,u,l,c=z[e+" "];if(c)return n?0:c.slice(0);for(s=e,u=[],l=T.preFilter;s;){(!r||(o=lt.exec(s)))&&(o&&(s=s.slice(o[0].length)||s),u.push(i=[])),r=!1,(o=ct.exec(s))&&(r=o.shift(),i.push({value:r,type:o[0].replace(ut," ")}),s=s.slice(r.length));for(a in T.filter)!(o=ht[a].exec(s))||l[a]&&!(o=l[a](o))||(r=o.shift(),i.push({value:r,type:a,matches:o}),s=s.slice(r.length));if(!r)break}return n?s.length:s?t.error(e):z(e,u).slice(0)},A=t.compile=function(e,t){var n,r=[],o=[],i=V[e+" "];if(!i){for(t||(t=E(e)),n=t.length;n--;)i=x(t[n]),i[$]?r.push(i):o.push(i);i=V(e,b(o,r)),i.selector=e}return i},N=t.select=function(e,t,n,r){var o,i,a,s,u,l="function"==typeof e&&e,c=!r&&E(e=l.selector||e);if(n=n||[],1===c.length){if(i=c[0]=c[0].slice(0),i.length>2&&"ID"===(a=i[0]).type&&9===t.nodeType&&P&&T.relative[i[1].type]){if(t=(T.find.ID(a.matches[0].replace(wt,Ct),t)||[])[0],!t)return n;l&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=ht.needsContext.test(e)?0:i.length;o--&&(a=i[o],!T.relative[s=a.type]);)if((u=T.find[s])&&(r=u(a.matches[0].replace(wt,Ct),bt.test(i[0].type)&&f(t.parentNode)||t))){if(i.splice(o,1),e=r.length&&d(i),!e)return Z.apply(n,r),n;break}}return(l||A(e,c))(r,t,!P,n,!t||bt.test(e)&&f(t.parentNode)||t),n},C.sortStable=$.split("").sort(J).join("")===$,C.detectDuplicates=!!_,L(),C.sortDetached=o(function(e){return 1&e.compareDocumentPosition(R.createElement("fieldset"))}),o(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||i("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),C.attributes&&o(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||i("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),o(function(e){return null==e.getAttribute("disabled")})||i(nt,function(e,t,n){var r;return n?void 0:e[t]===!0?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),t}(e);St.find=Et,St.expr=Et.selectors,St.expr[":"]=St.expr.pseudos,St.uniqueSort=St.unique=Et.uniqueSort,St.text=Et.getText,St.isXMLDoc=Et.isXML,St.contains=Et.contains,St.escapeSelector=Et.escape;var At=function(e,t,n){for(var r=[],o=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&St(e).is(n))break;r.push(e)}return r},Nt=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},Dt=St.expr.match.needsContext,jt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;St.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?St.find.matchesSelector(r,e)?[r]:[]:St.find.matches(e,St.grep(t,function(e){return 1===e.nodeType}))},St.fn.extend({find:function(e){var t,n,r=this.length,o=this;if("string"!=typeof e)return this.pushStack(St(e).filter(function(){for(t=0;r>t;t++)if(St.contains(o[t],this))return!0}));for(n=this.pushStack([]),t=0;r>t;t++)St.find(e,o[t],n);return r>1?St.uniqueSort(n):n},filter:function(e){return this.pushStack(a(this,e||[],!1))},not:function(e){return this.pushStack(a(this,e||[],!0))},is:function(e){return!!a(this,"string"==typeof e&&Dt.test(e)?St(e):e||[],!1).length}});var _t,Lt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Rt=St.fn.init=function(e,t,n){var r,o;if(!e)return this;if(n=n||_t,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:Lt.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof St?t[0]:t,St.merge(this,St.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:ut,!0)),jt.test(r[1])&&St.isPlainObject(t))for(r in t)bt(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return o=ut.getElementById(r[2]),o&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):bt(e)?void 0!==n.ready?n.ready(e):e(St):St.makeArray(e,this)};Rt.prototype=St.fn,_t=St(ut);var qt=/^(?:parents|prev(?:Until|All))/,Pt={children:!0,contents:!0,next:!0,prev:!0};St.fn.extend({has:function(e){var t=St(e,this),n=t.length;return this.filter(function(){for(var e=0;n>e;e++)if(St.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,o=this.length,i=[],a="string"!=typeof e&&St(e);if(!Dt.test(e))for(;o>r;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&St.find.matchesSelector(n,e))){i.push(n);break}return this.pushStack(i.length>1?St.uniqueSort(i):i)},index:function(e){return e?"string"==typeof e?dt.call(St(e),this[0]):dt.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(St.uniqueSort(St.merge(this.get(),St(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),St.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return At(e,"parentNode")},parentsUntil:function(e,t,n){return At(e,"parentNode",n)},next:function(e){return s(e,"nextSibling")},prev:function(e){return s(e,"previousSibling")},nextAll:function(e){return At(e,"nextSibling")},prevAll:function(e){return At(e,"previousSibling")},nextUntil:function(e,t,n){return At(e,"nextSibling",n)},prevUntil:function(e,t,n){return At(e,"previousSibling",n)},siblings:function(e){return Nt((e.parentNode||{}).firstChild,e)},children:function(e){return Nt(e.firstChild)},contents:function(e){return"undefined"!=typeof e.contentDocument?e.contentDocument:(i(e,"template")&&(e=e.content||e),St.merge([],e.childNodes))}},function(e,t){St.fn[e]=function(n,r){var o=St.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(o=St.filter(r,o)),this.length>1&&(Pt[e]||St.uniqueSort(o),qt.test(e)&&o.reverse()),this.pushStack(o)}});var Ot=/[^\x20\t\r\n\f]+/g;St.Callbacks=function(e){e="string"==typeof e?u(e):St.extend({},e);var t,n,o,i,a=[],s=[],l=-1,c=function(){for(i=i||e.once,o=t=!0;s.length;l=-1)for(n=s.shift();++l<a.length;)a[l].apply(n[0],n[1])===!1&&e.stopOnFalse&&(l=a.length,n=!1);e.memory||(n=!1),t=!1,i&&(a=n?[]:"")},f={add:function(){return a&&(n&&!t&&(l=a.length-1,s.push(n)),function o(t){St.each(t,function(t,n){bt(n)?e.unique&&f.has(n)||a.push(n):n&&n.length&&"string"!==r(n)&&o(n)})}(arguments),n&&!t&&c()),this},remove:function(){return St.each(arguments,function(e,t){for(var n;(n=St.inArray(t,a,n))>-1;)a.splice(n,1),l>=n&&l--}),this},has:function(e){return e?St.inArray(e,a)>-1:a.length>0},empty:function(){return a&&(a=[]),this},disable:function(){return i=s=[],a=n="",this},disabled:function(){return!a},lock:function(){return i=s=[],n||t||(a=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],s.push(n),t||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},St.extend({Deferred:function(t){var n=[["notify","progress",St.Callbacks("memory"),St.Callbacks("memory"),2],["resolve","done",St.Callbacks("once memory"),St.Callbacks("once memory"),0,"resolved"],["reject","fail",St.Callbacks("once memory"),St.Callbacks("once memory"),1,"rejected"]],r="pending",o={state:function(){return r},always:function(){return i.done(arguments).fail(arguments),this},"catch":function(e){return o.then(null,e)},pipe:function(){var e=arguments;return St.Deferred(function(t){St.each(n,function(n,r){var o=bt(e[r[4]])&&e[r[4]];i[r[1]](function(){var e=o&&o.apply(this,arguments);e&&bt(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,o?[e]:arguments)})}),e=null}).promise()},then:function(t,r,o){function i(t,n,r,o){return function(){var s=this,u=arguments,f=function(){var e,f;if(!(a>t)){if(e=r.apply(s,u),e===n.promise())throw new TypeError("Thenable self-resolution");f=e&&("object"==typeof e||"function"==typeof e)&&e.then,bt(f)?o?f.call(e,i(a,n,l,o),i(a,n,c,o)):(a++,f.call(e,i(a,n,l,o),i(a,n,c,o),i(a,n,l,n.notifyWith))):(r!==l&&(s=void 0,u=[e]),(o||n.resolveWith)(s,u))}},p=o?f:function(){try{f()}catch(e){St.Deferred.exceptionHook&&St.Deferred.exceptionHook(e,p.stackTrace),t+1>=a&&(r!==c&&(s=void 0,u=[e]),n.rejectWith(s,u))}};t?p():(St.Deferred.getStackHook&&(p.stackTrace=St.Deferred.getStackHook()),e.setTimeout(p))}}var a=0;return St.Deferred(function(e){n[0][3].add(i(0,e,bt(o)?o:l,e.notifyWith)),n[1][3].add(i(0,e,bt(t)?t:l)),n[2][3].add(i(0,e,bt(r)?r:c))}).promise()},promise:function(e){return null!=e?St.extend(e,o):o}},i={};return St.each(n,function(e,t){var a=t[2],s=t[5];o[t[1]]=a.add,s&&a.add(function(){r=s},n[3-e][2].disable,n[3-e][3].disable,n[0][2].lock,n[0][3].lock),a.add(t[3].fire),i[t[0]]=function(){return i[t[0]+"With"](this===i?void 0:this,arguments),this},i[t[0]+"With"]=a.fireWith}),o.promise(i),t&&t.call(i,i),i},when:function(e){var t=arguments.length,n=t,r=Array(n),o=ct.call(arguments),i=St.Deferred(),a=function(e){return function(n){r[e]=this,o[e]=arguments.length>1?ct.call(arguments):n,--t||i.resolveWith(r,o)}};if(1>=t&&(f(e,i.done(a(n)).resolve,i.reject,!t),"pending"===i.state()||bt(o[n]&&o[n].then)))return i.then();for(;n--;)f(o[n],a(n),i.reject);return i.promise()}});var Ht=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;St.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&Ht.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},St.readyException=function(t){e.setTimeout(function(){throw t})};var It=St.Deferred();St.fn.ready=function(e){return It.then(e)["catch"](function(e){St.readyException(e)}),this},St.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--St.readyWait:St.isReady)||(St.isReady=!0,e!==!0&&--St.readyWait>0||It.resolveWith(ut,[St]))}}),St.ready.then=It.then,"complete"===ut.readyState||"loading"!==ut.readyState&&!ut.documentElement.doScroll?e.setTimeout(St.ready):(ut.addEventListener("DOMContentLoaded",p),e.addEventListener("load",p));var Mt=function(e,t,n,o,i,a,s){var u=0,l=e.length,c=null==n;if("object"===r(n)){i=!0;for(u in n)Mt(e,t,u,n[u],!0,a,s)}else if(void 0!==o&&(i=!0,bt(o)||(s=!0),c&&(s?(t.call(e,o),t=null):(c=t,t=function(e,t,n){return c.call(St(e),n)})),t))for(;l>u;u++)t(e[u],n,s?o:o.call(e[u],u,t(e[u],n)));return i?e:c?t.call(e):l?t(e[0],n):a},$t=/^-ms-/,Ft=/-([a-z])/g,Bt=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};g.uid=1,g.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Bt(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,o=this.cache(e);if("string"==typeof t)o[h(t)]=n;else for(r in t)o[h(r)]=t[r];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][h(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(h):(t=h(t),t=t in r?[t]:t.match(Ot)||[]),n=t.length;for(;n--;)delete r[t[n]]}(void 0===t||St.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!St.isEmptyObject(t)}};var Wt=new g,Ut=new g,zt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Vt=/[A-Z]/g;St.extend({hasData:function(e){return Ut.hasData(e)||Wt.hasData(e)},data:function(e,t,n){return Ut.access(e,t,n)},removeData:function(e,t){Ut.remove(e,t)},_data:function(e,t,n){return Wt.access(e,t,n)},_removeData:function(e,t){Wt.remove(e,t)}}),St.fn.extend({data:function(e,t){var n,r,o,i=this[0],a=i&&i.attributes;if(void 0===e){if(this.length&&(o=Ut.get(i),1===i.nodeType&&!Wt.get(i,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=h(r.slice(5)),y(i,r,o[r])));Wt.set(i,"hasDataAttrs",!0)}return o}return"object"==typeof e?this.each(function(){Ut.set(this,e)}):Mt(this,function(t){var n;if(i&&void 0===t){if(n=Ut.get(i,e),void 0!==n)return n;if(n=y(i,e),void 0!==n)return n}else this.each(function(){Ut.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){Ut.remove(this,e)})}}),St.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=Wt.get(e,t),n&&(!r||Array.isArray(n)?r=Wt.access(e,t,St.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=St.queue(e,t),r=n.length,o=n.shift(),i=St._queueHooks(e,t),a=function(){St.dequeue(e,t)};"inprogress"===o&&(o=n.shift(),r--),o&&("fx"===t&&n.unshift("inprogress"),delete i.stop,o.call(e,a,i)),!r&&i&&i.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Wt.get(e,n)||Wt.access(e,n,{empty:St.Callbacks("once memory").add(function(){Wt.remove(e,[t+"queue",n])})})}}),St.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?St.queue(this[0],e):void 0===t?this:this.each(function(){var n=St.queue(this,e,t);St._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&St.dequeue(this,e)})},dequeue:function(e){return this.each(function(){St.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,o=St.Deferred(),i=this,a=this.length,s=function(){--r||o.resolveWith(i,[i])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=Wt.get(i[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),o.promise(t)}});var Xt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Jt=new RegExp("^(?:([+-])=|)("+Xt+")([a-z%]*)$","i"),Yt=["Top","Right","Bottom","Left"],Gt=ut.documentElement,Qt=function(e){return St.contains(e.ownerDocument,e)},Kt={composed:!0};Gt.getRootNode&&(Qt=function(e){return St.contains(e.ownerDocument,e)||e.getRootNode(Kt)===e.ownerDocument});var Zt=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&Qt(e)&&"none"===St.css(e,"display")},en=function(e,t,n,r){var o,i,a={};for(i in t)a[i]=e.style[i],e.style[i]=t[i];o=n.apply(e,r||[]);for(i in t)e.style[i]=a[i];return o},tn={};St.fn.extend({show:function(){return b(this,!0)},hide:function(){return b(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Zt(this)?St(this).show():St(this).hide()})}});var nn=/^(?:checkbox|radio)$/i,rn=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,on=/^$|^module$|\/(?:java|ecma)script/i,an={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};an.optgroup=an.option,an.tbody=an.tfoot=an.colgroup=an.caption=an.thead,an.th=an.td;var sn=/<|&#?\w+;/;!function(){var e=ut.createDocumentFragment(),t=e.appendChild(ut.createElement("div")),n=ut.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),xt.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",xt.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var un=/^key/,ln=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,cn=/^([^.]*)(?:\.(.+)|)/;St.event={global:{},add:function(e,t,n,r,o){var i,a,s,u,l,c,f,p,d,h,g,m=Wt.get(e);if(m)for(n.handler&&(i=n,n=i.handler,o=i.selector),o&&St.find.matchesSelector(Gt,o),n.guid||(n.guid=St.guid++),(u=m.events)||(u=m.events={}),(a=m.handle)||(a=m.handle=function(t){return"undefined"!=typeof St&&St.event.triggered!==t.type?St.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(Ot)||[""],l=t.length;l--;)s=cn.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d&&(f=St.event.special[d]||{},d=(o?f.delegateType:f.bindType)||d,f=St.event.special[d]||{},c=St.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:o,needsContext:o&&St.expr.match.needsContext.test(o),namespace:h.join(".")},i),(p=u[d])||(p=u[d]=[],p.delegateCount=0,f.setup&&f.setup.call(e,r,h,a)!==!1||e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),o?p.splice(p.delegateCount++,0,c):p.push(c),St.event.global[d]=!0)},remove:function(e,t,n,r,o){var i,a,s,u,l,c,f,p,d,h,g,m=Wt.hasData(e)&&Wt.get(e);if(m&&(u=m.events)){for(t=(t||"").match(Ot)||[""],l=t.length;l--;)if(s=cn.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d){for(f=St.event.special[d]||{},d=(r?f.delegateType:f.bindType)||d,p=u[d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=i=p.length;i--;)c=p[i],!o&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(i,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&f.teardown.call(e,h,m.handle)!==!1||St.removeEvent(e,d,m.handle),delete u[d])}else for(d in u)St.event.remove(e,d+t[l],n,r,!0);St.isEmptyObject(u)&&Wt.remove(e,"handle events")}},dispatch:function(e){var t,n,r,o,i,a,s=St.event.fix(e),u=new Array(arguments.length),l=(Wt.get(this,"events")||{})[s.type]||[],c=St.event.special[s.type]||{};for(u[0]=s,t=1;t<arguments.length;t++)u[t]=arguments[t];if(s.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,s)!==!1){for(a=St.event.handlers.call(this,s,l),t=0;(o=a[t++])&&!s.isPropagationStopped();)for(s.currentTarget=o.elem,n=0;(i=o.handlers[n++])&&!s.isImmediatePropagationStopped();)(!s.rnamespace||i.namespace===!1||s.rnamespace.test(i.namespace))&&(s.handleObj=i,s.data=i.data,r=((St.event.special[i.origType]||{}).handle||i.handler).apply(o.elem,u),void 0!==r&&(s.result=r)===!1&&(s.preventDefault(),s.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,o,i,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||l.disabled!==!0)){for(i=[],a={},n=0;u>n;n++)r=t[n],o=r.selector+" ",void 0===a[o]&&(a[o]=r.needsContext?St(o,this).index(l)>-1:St.find(o,this,null,[l]).length),a[o]&&i.push(r);i.length&&s.push({elem:l,handlers:i})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(St.Event.prototype,e,{enumerable:!0,configurable:!0,get:bt(t)?function(){return this.originalEvent?t(this.originalEvent):void 0}:function(){return this.originalEvent?this.originalEvent[e]:void 0},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[St.expando]?e:new St.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return nn.test(t.type)&&t.click&&i(t,"input")&&D(t,"click",S),!1},trigger:function(e){var t=this||e;return nn.test(t.type)&&t.click&&i(t,"input")&&D(t,"click"),!0},_default:function(e){var t=e.target;return nn.test(t.type)&&t.click&&i(t,"input")&&Wt.get(t,"click")||i(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},St.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},St.Event=function(e,t){return this instanceof St.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?S:k,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&St.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),void(this[St.expando]=!0)):new St.Event(e,t)},St.Event.prototype={constructor:St.Event,isDefaultPrevented:k,isPropagationStopped:k,isImmediatePropagationStopped:k,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=S,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=S,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=S,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},St.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&un.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&ln.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},St.event.addProp),St.each({focus:"focusin",blur:"focusout"},function(e,t){St.event.special[e]={setup:function(){return D(this,e,E),!1},trigger:function(){return D(this,e),!0},delegateType:t}}),St.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){St.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,o=e.relatedTarget,i=e.handleObj;return(!o||o!==r&&!St.contains(r,o))&&(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}}),St.fn.extend({on:function(e,t,n,r){return N(this,e,t,n,r)},one:function(e,t,n,r){return N(this,e,t,n,r,1)},off:function(e,t,n){var r,o;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,St(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=void 0),n===!1&&(n=k),this.each(function(){St.event.remove(this,e,n,t)})}});var fn=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,pn=/<script|<style|<link/i,dn=/checked\s*(?:[^=]|=\s*.checked.)/i,hn=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;St.extend({htmlPrefilter:function(e){return e.replace(fn,"<$1></$2>")},clone:function(e,t,n){var r,o,i,a,s=e.cloneNode(!0),u=Qt(e);if(!(xt.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||St.isXMLDoc(e)))for(a=w(s),i=w(e),r=0,o=i.length;o>r;r++)q(i[r],a[r]);if(t)if(n)for(i=i||w(e),a=a||w(s),r=0,o=i.length;o>r;r++)R(i[r],a[r]);else R(e,s);return a=w(s,"script"),a.length>0&&C(a,!u&&w(e,"script")),s},cleanData:function(e){for(var t,n,r,o=St.event.special,i=0;void 0!==(n=e[i]);i++)if(Bt(n)){if(t=n[Wt.expando]){if(t.events)for(r in t.events)o[r]?St.event.remove(n,r):St.removeEvent(n,r,t.handle);n[Wt.expando]=void 0}n[Ut.expando]&&(n[Ut.expando]=void 0)}}}),St.fn.extend({detach:function(e){return O(this,e,!0)},remove:function(e){return O(this,e)},text:function(e){return Mt(this,function(e){return void 0===e?St.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=e)})},null,e,arguments.length)},append:function(){return P(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=j(this,e);t.appendChild(e)}})},prepend:function(){return P(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=j(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return P(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return P(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(St.cleanData(w(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return St.clone(this,e,t)})},html:function(e){return Mt(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!pn.test(e)&&!an[(rn.exec(e)||["",""])[1].toLowerCase()]){e=St.htmlPrefilter(e);try{for(;r>n;n++)t=this[n]||{},1===t.nodeType&&(St.cleanData(w(t,!1)),t.innerHTML=e);t=0}catch(o){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return P(this,arguments,function(t){var n=this.parentNode;St.inArray(this,e)<0&&(St.cleanData(w(this)),n&&n.replaceChild(t,this))},e)}}),St.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){St.fn[e]=function(e){for(var n,r=[],o=St(e),i=o.length-1,a=0;i>=a;a++)n=a===i?this:this.clone(!0),St(o[a])[t](n),pt.apply(r,n.get());return this.pushStack(r)}});var gn=new RegExp("^("+Xt+")(?!px)[a-z%]+$","i"),mn=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},yn=new RegExp(Yt.join("|"),"i");!function(){function t(){if(l){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",l.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Gt.appendChild(u).appendChild(l);var t=e.getComputedStyle(l);r="1%"!==t.top,s=12===n(t.marginLeft),l.style.right="60%",a=36===n(t.right),o=36===n(t.width),l.style.position="absolute",i=12===n(l.offsetWidth/3),Gt.removeChild(u),l=null}}function n(e){return Math.round(parseFloat(e))}var r,o,i,a,s,u=ut.createElement("div"),l=ut.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",xt.clearCloneStyle="content-box"===l.style.backgroundClip,St.extend(xt,{boxSizingReliable:function(){return t(),o},pixelBoxStyles:function(){return t(),a},pixelPosition:function(){return t(),r},reliableMarginLeft:function(){return t(),s},scrollboxSize:function(){return t(),i}}))}();var vn=["Webkit","Moz","ms"],xn=ut.createElement("div").style,bn={},wn=/^(none|table(?!-c[ea]).+)/,Cn=/^--/,Tn={position:"absolute",visibility:"hidden",display:"block"},Sn={letterSpacing:"0",fontWeight:"400"};St.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=H(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,i,a,s=h(t),u=Cn.test(t),l=e.style;return u||(t=$(s)),a=St.cssHooks[t]||St.cssHooks[s],void 0===n?a&&"get"in a&&void 0!==(o=a.get(e,!1,r))?o:l[t]:(i=typeof n,"string"===i&&(o=Jt.exec(n))&&o[1]&&(n=v(e,t,o),i="number"),null!=n&&n===n&&("number"!==i||u||(n+=o&&o[3]||(St.cssNumber[s]?"":"px")),xt.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n)),void 0)}},css:function(e,t,n,r){var o,i,a,s=h(t),u=Cn.test(t);return u||(t=$(s)),a=St.cssHooks[t]||St.cssHooks[s],a&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=H(e,t,r)),"normal"===o&&t in Sn&&(o=Sn[t]),""===n||n?(i=parseFloat(o),n===!0||isFinite(i)?i||0:o):o}}),St.each(["height","width"],function(e,t){St.cssHooks[t]={get:function(e,n,r){return n?!wn.test(St.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?W(e,t,r):en(e,Tn,function(){return W(e,t,r)}):void 0},set:function(e,n,r){var o,i=mn(e),a=!xt.scrollboxSize()&&"absolute"===i.position,s=a||r,u=s&&"border-box"===St.css(e,"boxSizing",!1,i),l=r?B(e,t,r,u,i):0;return u&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(i[t])-B(e,t,"border",!1,i)-.5)),l&&(o=Jt.exec(n))&&"px"!==(o[3]||"px")&&(e.style[t]=n,n=St.css(e,t)),F(e,n,l)}}}),St.cssHooks.marginLeft=I(xt.reliableMarginLeft,function(e,t){return t?(parseFloat(H(e,"marginLeft"))||e.getBoundingClientRect().left-en(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px":void 0}),St.each({margin:"",padding:"",border:"Width"},function(e,t){St.cssHooks[e+t]={expand:function(n){for(var r=0,o={},i="string"==typeof n?n.split(" "):[n];4>r;r++)o[e+Yt[r]+t]=i[r]||i[r-2]||i[0];return o}},"margin"!==e&&(St.cssHooks[e+t].set=F)}),St.fn.extend({css:function(e,t){return Mt(this,function(e,t,n){var r,o,i={},a=0;if(Array.isArray(t)){for(r=mn(e),o=t.length;o>a;a++)i[t[a]]=St.css(e,t[a],!1,r);return i}return void 0!==n?St.style(e,t,n):St.css(e,t)},e,t,arguments.length>1)}}),St.Tween=U,U.prototype={constructor:U,init:function(e,t,n,r,o,i){this.elem=e,this.prop=n,this.easing=o||St.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=i||(St.cssNumber[n]?"":"px")},cur:function(){var e=U.propHooks[this.prop];return e&&e.get?e.get(this):U.propHooks._default.get(this)},run:function(e){var t,n=U.propHooks[this.prop];return this.pos=t=this.options.duration?St.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):U.propHooks._default.set(this),this}},U.prototype.init.prototype=U.prototype,U.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=St.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){St.fx.step[e.prop]?St.fx.step[e.prop](e):1!==e.elem.nodeType||!St.cssHooks[e.prop]&&null==e.elem.style[$(e.prop)]?e.elem[e.prop]=e.now:St.style(e.elem,e.prop,e.now+e.unit)}}},U.propHooks.scrollTop=U.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},St.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},St.fx=U.prototype.init,St.fx.step={};var kn,En,An=/^(?:toggle|show|hide)$/,Nn=/queueHooks$/;St.Animation=St.extend(Q,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return v(n.elem,e,Jt.exec(t),n),n}]},tweener:function(e,t){bt(e)?(t=e,e=["*"]):e=e.match(Ot);for(var n,r=0,o=e.length;o>r;r++)n=e[r],Q.tweeners[n]=Q.tweeners[n]||[],Q.tweeners[n].unshift(t)
},prefilters:[Y],prefilter:function(e,t){t?Q.prefilters.unshift(e):Q.prefilters.push(e)}}),St.speed=function(e,t,n){var r=e&&"object"==typeof e?St.extend({},e):{complete:n||!n&&t||bt(e)&&e,duration:e,easing:n&&t||t&&!bt(t)&&t};return St.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration=r.duration in St.fx.speeds?St.fx.speeds[r.duration]:St.fx.speeds._default),(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){bt(r.old)&&r.old.call(this),r.queue&&St.dequeue(this,r.queue)},r},St.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Zt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var o=St.isEmptyObject(e),i=St.speed(t,n,r),a=function(){var t=Q(this,St.extend({},e),i);(o||Wt.get(this,"finish"))&&t.stop(!0)};return a.finish=a,o||i.queue===!1?this.each(a):this.queue(i.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,o=null!=e&&e+"queueHooks",i=St.timers,a=Wt.get(this);if(o)a[o]&&a[o].stop&&r(a[o]);else for(o in a)a[o]&&a[o].stop&&Nn.test(o)&&r(a[o]);for(o=i.length;o--;)i[o].elem!==this||null!=e&&i[o].queue!==e||(i[o].anim.stop(n),t=!1,i.splice(o,1));(t||!n)&&St.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=Wt.get(this),r=n[e+"queue"],o=n[e+"queueHooks"],i=St.timers,a=r?r.length:0;for(n.finish=!0,St.queue(this,e,[]),o&&o.stop&&o.stop.call(this,!0),t=i.length;t--;)i[t].elem===this&&i[t].queue===e&&(i[t].anim.stop(!0),i.splice(t,1));for(t=0;a>t;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),St.each(["toggle","show","hide"],function(e,t){var n=St.fn[t];St.fn[t]=function(e,r,o){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(X(t,!0),e,r,o)}}),St.each({slideDown:X("show"),slideUp:X("hide"),slideToggle:X("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){St.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),St.timers=[],St.fx.tick=function(){var e,t=0,n=St.timers;for(kn=Date.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||St.fx.stop(),kn=void 0},St.fx.timer=function(e){St.timers.push(e),St.fx.start()},St.fx.interval=13,St.fx.start=function(){En||(En=!0,z())},St.fx.stop=function(){En=null},St.fx.speeds={slow:600,fast:200,_default:400},St.fn.delay=function(t,n){return t=St.fx?St.fx.speeds[t]||t:t,n=n||"fx",this.queue(n,function(n,r){var o=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(o)}})},function(){var e=ut.createElement("input"),t=ut.createElement("select"),n=t.appendChild(ut.createElement("option"));e.type="checkbox",xt.checkOn=""!==e.value,xt.optSelected=n.selected,e=ut.createElement("input"),e.value="t",e.type="radio",xt.radioValue="t"===e.value}();var Dn,jn=St.expr.attrHandle;St.fn.extend({attr:function(e,t){return Mt(this,St.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){St.removeAttr(this,e)})}}),St.extend({attr:function(e,t,n){var r,o,i=e.nodeType;if(3!==i&&8!==i&&2!==i)return"undefined"==typeof e.getAttribute?St.prop(e,t,n):(1===i&&St.isXMLDoc(e)||(o=St.attrHooks[t.toLowerCase()]||(St.expr.match.bool.test(t)?Dn:void 0)),void 0!==n?null===n?void St.removeAttr(e,t):o&&"set"in o&&void 0!==(r=o.set(e,n,t))?r:(e.setAttribute(t,n+""),n):o&&"get"in o&&null!==(r=o.get(e,t))?r:(r=St.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!xt.radioValue&&"radio"===t&&i(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,o=t&&t.match(Ot);if(o&&1===e.nodeType)for(;n=o[r++];)e.removeAttribute(n)}}),Dn={set:function(e,t,n){return t===!1?St.removeAttr(e,n):e.setAttribute(n,n),n}},St.each(St.expr.match.bool.source.match(/\w+/g),function(e,t){var n=jn[t]||St.find.attr;jn[t]=function(e,t,r){var o,i,a=t.toLowerCase();return r||(i=jn[a],jn[a]=o,o=null!=n(e,t,r)?a:null,jn[a]=i),o}});var _n=/^(?:input|select|textarea|button)$/i,Ln=/^(?:a|area)$/i;St.fn.extend({prop:function(e,t){return Mt(this,St.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[St.propFix[e]||e]})}}),St.extend({prop:function(e,t,n){var r,o,i=e.nodeType;if(3!==i&&8!==i&&2!==i)return 1===i&&St.isXMLDoc(e)||(t=St.propFix[t]||t,o=St.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(r=o.set(e,n,t))?r:e[t]=n:o&&"get"in o&&null!==(r=o.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=St.find.attr(e,"tabindex");return t?parseInt(t,10):_n.test(e.nodeName)||Ln.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),xt.optSelected||(St.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),St.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){St.propFix[this.toLowerCase()]=this}),St.fn.extend({addClass:function(e){var t,n,r,o,i,a,s,u=0;if(bt(e))return this.each(function(t){St(this).addClass(e.call(this,t,Z(this)))});if(t=et(e),t.length)for(;n=this[u++];)if(o=Z(n),r=1===n.nodeType&&" "+K(o)+" "){for(a=0;i=t[a++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s=K(r),o!==s&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,o,i,a,s,u=0;if(bt(e))return this.each(function(t){St(this).removeClass(e.call(this,t,Z(this)))});if(!arguments.length)return this.attr("class","");if(t=et(e),t.length)for(;n=this[u++];)if(o=Z(n),r=1===n.nodeType&&" "+K(o)+" "){for(a=0;i=t[a++];)for(;r.indexOf(" "+i+" ")>-1;)r=r.replace(" "+i+" "," ");s=K(r),o!==s&&n.setAttribute("class",s)}return this},toggleClass:function(e,t){var n=typeof e,r="string"===n||Array.isArray(e);return"boolean"==typeof t&&r?t?this.addClass(e):this.removeClass(e):this.each(bt(e)?function(n){St(this).toggleClass(e.call(this,n,Z(this),t),t)}:function(){var t,o,i,a;if(r)for(o=0,i=St(this),a=et(e);t=a[o++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else(void 0===e||"boolean"===n)&&(t=Z(this),t&&Wt.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||e===!1?"":Wt.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+K(Z(n))+" ").indexOf(t)>-1)return!0;return!1}});var Rn=/\r/g;St.fn.extend({val:function(e){var t,n,r,o=this[0];{if(arguments.length)return r=bt(e),this.each(function(n){var o;1===this.nodeType&&(o=r?e.call(this,n,St(this).val()):e,null==o?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=St.map(o,function(e){return null==e?"":e+""})),t=St.valHooks[this.type]||St.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,o,"value")||(this.value=o))});if(o)return t=St.valHooks[o.type]||St.valHooks[o.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(o,"value"))?n:(n=o.value,"string"==typeof n?n.replace(Rn,""):null==n?"":n)}}}),St.extend({valHooks:{option:{get:function(e){var t=St.find.attr(e,"value");return null!=t?t:K(St.text(e))}},select:{get:function(e){var t,n,r,o=e.options,a=e.selectedIndex,s="select-one"===e.type,u=s?null:[],l=s?a+1:o.length;for(r=0>a?l:s?a:0;l>r;r++)if(n=o[r],!(!n.selected&&r!==a||n.disabled||n.parentNode.disabled&&i(n.parentNode,"optgroup"))){if(t=St(n).val(),s)return t;u.push(t)}return u},set:function(e,t){for(var n,r,o=e.options,i=St.makeArray(t),a=o.length;a--;)r=o[a],(r.selected=St.inArray(St.valHooks.option.get(r),i)>-1)&&(n=!0);return n||(e.selectedIndex=-1),i}}}}),St.each(["radio","checkbox"],function(){St.valHooks[this]={set:function(e,t){return Array.isArray(t)?e.checked=St.inArray(St(e).val(),t)>-1:void 0}},xt.checkOn||(St.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),xt.focusin="onfocusin"in e;var qn=/^(?:focusinfocus|focusoutblur)$/,Pn=function(e){e.stopPropagation()};St.extend(St.event,{trigger:function(t,n,r,o){var i,a,s,u,l,c,f,p,d=[r||ut],h=mt.call(t,"type")?t.type:t,g=mt.call(t,"namespace")?t.namespace.split("."):[];if(a=p=s=r=r||ut,3!==r.nodeType&&8!==r.nodeType&&!qn.test(h+St.event.triggered)&&(h.indexOf(".")>-1&&(g=h.split("."),h=g.shift(),g.sort()),l=h.indexOf(":")<0&&"on"+h,t=t[St.expando]?t:new St.Event(h,"object"==typeof t&&t),t.isTrigger=o?2:3,t.namespace=g.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:St.makeArray(n,[t]),f=St.event.special[h]||{},o||!f.trigger||f.trigger.apply(r,n)!==!1)){if(!o&&!f.noBubble&&!wt(r)){for(u=f.delegateType||h,qn.test(u+h)||(a=a.parentNode);a;a=a.parentNode)d.push(a),s=a;s===(r.ownerDocument||ut)&&d.push(s.defaultView||s.parentWindow||e)}for(i=0;(a=d[i++])&&!t.isPropagationStopped();)p=a,t.type=i>1?u:f.bindType||h,c=(Wt.get(a,"events")||{})[t.type]&&Wt.get(a,"handle"),c&&c.apply(a,n),c=l&&a[l],c&&c.apply&&Bt(a)&&(t.result=c.apply(a,n),t.result===!1&&t.preventDefault());return t.type=h,o||t.isDefaultPrevented()||f._default&&f._default.apply(d.pop(),n)!==!1||!Bt(r)||l&&bt(r[h])&&!wt(r)&&(s=r[l],s&&(r[l]=null),St.event.triggered=h,t.isPropagationStopped()&&p.addEventListener(h,Pn),r[h](),t.isPropagationStopped()&&p.removeEventListener(h,Pn),St.event.triggered=void 0,s&&(r[l]=s)),t.result}},simulate:function(e,t,n){var r=St.extend(new St.Event,n,{type:e,isSimulated:!0});St.event.trigger(r,null,t)}}),St.fn.extend({trigger:function(e,t){return this.each(function(){St.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?St.event.trigger(e,t,n,!0):void 0}}),xt.focusin||St.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){St.event.simulate(t,e.target,St.event.fix(e))};St.event.special[t]={setup:function(){var r=this.ownerDocument||this,o=Wt.access(r,t);o||r.addEventListener(e,n,!0),Wt.access(r,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this,o=Wt.access(r,t)-1;o?Wt.access(r,t,o):(r.removeEventListener(e,n,!0),Wt.remove(r,t))}}});var On=e.location,Hn=Date.now(),In=/\?/;St.parseXML=function(t){var n;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(r){n=void 0}return(!n||n.getElementsByTagName("parsererror").length)&&St.error("Invalid XML: "+t),n};var Mn=/\[\]$/,$n=/\r?\n/g,Fn=/^(?:submit|button|image|reset|file)$/i,Bn=/^(?:input|select|textarea|keygen)/i;St.param=function(e,t){var n,r=[],o=function(e,t){var n=bt(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!St.isPlainObject(e))St.each(e,function(){o(this.name,this.value)});else for(n in e)tt(n,e[n],t,o);return r.join("&")},St.fn.extend({serialize:function(){return St.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=St.prop(this,"elements");return e?St.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!St(this).is(":disabled")&&Bn.test(this.nodeName)&&!Fn.test(e)&&(this.checked||!nn.test(e))}).map(function(e,t){var n=St(this).val();return null==n?null:Array.isArray(n)?St.map(n,function(e){return{name:t.name,value:e.replace($n,"\r\n")}}):{name:t.name,value:n.replace($n,"\r\n")}}).get()}});var Wn=/%20/g,Un=/#.*$/,zn=/([?&])_=[^&]*/,Vn=/^(.*?):[ \t]*([^\r\n]*)$/gm,Xn=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Jn=/^(?:GET|HEAD)$/,Yn=/^\/\//,Gn={},Qn={},Kn="*/".concat("*"),Zn=ut.createElement("a");Zn.href=On.href,St.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:On.href,type:"GET",isLocal:Xn.test(On.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Kn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":St.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?ot(ot(e,St.ajaxSettings),t):ot(St.ajaxSettings,e)},ajaxPrefilter:nt(Gn),ajaxTransport:nt(Qn),ajax:function(t,n){function r(t,n,r,s){var l,p,d,b,w,C=n;c||(c=!0,u&&e.clearTimeout(u),o=void 0,a=s||"",T.readyState=t>0?4:0,l=t>=200&&300>t||304===t,r&&(b=it(h,T,r)),b=at(h,b,T,l),l?(h.ifModified&&(w=T.getResponseHeader("Last-Modified"),w&&(St.lastModified[i]=w),w=T.getResponseHeader("etag"),w&&(St.etag[i]=w)),204===t||"HEAD"===h.type?C="nocontent":304===t?C="notmodified":(C=b.state,p=b.data,d=b.error,l=!d)):(d=C,(t||!C)&&(C="error",0>t&&(t=0))),T.status=t,T.statusText=(n||C)+"",l?y.resolveWith(g,[p,C,T]):y.rejectWith(g,[T,C,d]),T.statusCode(x),x=void 0,f&&m.trigger(l?"ajaxSuccess":"ajaxError",[T,h,l?p:d]),v.fireWith(g,[T,C]),f&&(m.trigger("ajaxComplete",[T,h]),--St.active||St.event.trigger("ajaxStop")))}"object"==typeof t&&(n=t,t=void 0),n=n||{};var o,i,a,s,u,l,c,f,p,d,h=St.ajaxSetup({},n),g=h.context||h,m=h.context&&(g.nodeType||g.jquery)?St(g):St.event,y=St.Deferred(),v=St.Callbacks("once memory"),x=h.statusCode||{},b={},w={},C="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s)for(s={};t=Vn.exec(a);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?a:null},setRequestHeader:function(e,t){return null==c&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,b[e]=t),this},overrideMimeType:function(e){return null==c&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)T.always(e[T.status]);else for(t in e)x[t]=[x[t],e[t]];return this},abort:function(e){var t=e||C;return o&&o.abort(t),r(0,t),this}};if(y.promise(T),h.url=((t||h.url||On.href)+"").replace(Yn,On.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(Ot)||[""],null==h.crossDomain){l=ut.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=Zn.protocol+"//"+Zn.host!=l.protocol+"//"+l.host}catch(S){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=St.param(h.data,h.traditional)),rt(Gn,h,n,T),c)return T;f=St.event&&h.global,f&&0===St.active++&&St.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Jn.test(h.type),i=h.url.replace(Un,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Wn,"+")):(d=h.url.slice(i.length),h.data&&(h.processData||"string"==typeof h.data)&&(i+=(In.test(i)?"&":"?")+h.data,delete h.data),h.cache===!1&&(i=i.replace(zn,"$1"),d=(In.test(i)?"&":"?")+"_="+Hn++ +d),h.url=i+d),h.ifModified&&(St.lastModified[i]&&T.setRequestHeader("If-Modified-Since",St.lastModified[i]),St.etag[i]&&T.setRequestHeader("If-None-Match",St.etag[i])),(h.data&&h.hasContent&&h.contentType!==!1||n.contentType)&&T.setRequestHeader("Content-Type",h.contentType),T.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Kn+"; q=0.01":""):h.accepts["*"]);for(p in h.headers)T.setRequestHeader(p,h.headers[p]);if(h.beforeSend&&(h.beforeSend.call(g,T,h)===!1||c))return T.abort();if(C="abort",v.add(h.complete),T.done(h.success),T.fail(h.error),o=rt(Qn,h,n,T)){if(T.readyState=1,f&&m.trigger("ajaxSend",[T,h]),c)return T;h.async&&h.timeout>0&&(u=e.setTimeout(function(){T.abort("timeout")},h.timeout));try{c=!1,o.send(b,r)}catch(S){if(c)throw S;r(-1,S)}}else r(-1,"No Transport");return T},getJSON:function(e,t,n){return St.get(e,t,n,"json")},getScript:function(e,t){return St.get(e,void 0,t,"script")}}),St.each(["get","post"],function(e,t){St[t]=function(e,n,r,o){return bt(n)&&(o=o||r,r=n,n=void 0),St.ajax(St.extend({url:e,type:t,dataType:o,data:n,success:r},St.isPlainObject(e)&&e))}}),St._evalUrl=function(e,t){return St.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){St.globalEval(e,t)}})},St.fn.extend({wrapAll:function(e){var t;return this[0]&&(bt(e)&&(e=e.call(this[0])),t=St(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return this.each(bt(e)?function(t){St(this).wrapInner(e.call(this,t))}:function(){var t=St(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=bt(e);return this.each(function(n){St(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){St(this).replaceWith(this.childNodes)}),this}}),St.expr.pseudos.hidden=function(e){return!St.expr.pseudos.visible(e)},St.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},St.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(t){}};var er={0:200,1223:204},tr=St.ajaxSettings.xhr();xt.cors=!!tr&&"withCredentials"in tr,xt.ajax=tr=!!tr,St.ajaxTransport(function(t){var n,r;return xt.cors||tr&&!t.crossDomain?{send:function(o,i){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)s[a]=t.xhrFields[a];t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest");for(a in o)s.setRequestHeader(a,o[a]);n=function(e){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?i(0,"error"):i(s.status,s.statusText):i(er[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout(function(){n&&r()})},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(u){if(n)throw u}},abort:function(){n&&n()}}:void 0}),St.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),St.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return St.globalEval(e),e}}}),St.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),St.ajaxTransport("script",function(e){if(e.crossDomain||e.scriptAttrs){var t,n;return{send:function(r,o){t=St("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),ut.head.appendChild(t[0])},abort:function(){n&&n()}}}});var nr=[],rr=/(=)\?(?=&|$)|\?\?/;St.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=nr.pop()||St.expando+"_"+Hn++;return this[e]=!0,e}}),St.ajaxPrefilter("json jsonp",function(t,n,r){var o,i,a,s=t.jsonp!==!1&&(rr.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&rr.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(o=t.jsonpCallback=bt(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(rr,"$1"+o):t.jsonp!==!1&&(t.url+=(In.test(t.url)?"&":"?")+t.jsonp+"="+o),t.converters["script json"]=function(){return a||St.error(o+" was not called"),a[0]},t.dataTypes[0]="json",i=e[o],e[o]=function(){a=arguments},r.always(function(){void 0===i?St(e).removeProp(o):e[o]=i,t[o]&&(t.jsonpCallback=n.jsonpCallback,nr.push(o)),a&&bt(i)&&i(a[0]),a=i=void 0}),"script"):void 0}),xt.createHTMLDocument=function(){var e=ut.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),St.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var r,o,i;return t||(xt.createHTMLDocument?(t=ut.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=ut.location.href,t.head.appendChild(r)):t=ut),o=jt.exec(e),i=!n&&[],o?[t.createElement(o[1])]:(o=T([e],t,i),i&&i.length&&St(i).remove(),St.merge([],o.childNodes))},St.fn.load=function(e,t,n){var r,o,i,a=this,s=e.indexOf(" ");return s>-1&&(r=K(e.slice(s)),e=e.slice(0,s)),bt(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&St.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){i=arguments,a.html(r?St("<div>").append(St.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,i||[e.responseText,t,e])})}),this},St.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){St.fn[t]=function(e){return this.on(t,e)}}),St.expr.pseudos.animated=function(e){return St.grep(St.timers,function(t){return e===t.elem}).length},St.offset={setOffset:function(e,t,n){var r,o,i,a,s,u,l,c=St.css(e,"position"),f=St(e),p={};"static"===c&&(e.style.position="relative"),s=f.offset(),i=St.css(e,"top"),u=St.css(e,"left"),l=("absolute"===c||"fixed"===c)&&(i+u).indexOf("auto")>-1,l?(r=f.position(),a=r.top,o=r.left):(a=parseFloat(i)||0,o=parseFloat(u)||0),bt(t)&&(t=t.call(e,n,St.extend({},s))),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+o),"using"in t?t.using.call(e,p):f.css(p)}},St.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){St.offset.setOffset(this,e,t)});var t,n,r=this[0];if(r)return r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var e,t,n,r=this[0],o={top:0,left:0};if("fixed"===St.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===St.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&(o=St(e).offset(),o.top+=St.css(e,"borderTopWidth",!0),o.left+=St.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-St.css(r,"marginTop",!0),left:t.left-o.left-St.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===St.css(e,"position");)e=e.offsetParent;return e||Gt})}}),St.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;St.fn[e]=function(r){return Mt(this,function(e,r,o){var i;return wt(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===o?i?i[t]:e[r]:void(i?i.scrollTo(n?i.pageXOffset:o,n?o:i.pageYOffset):e[r]=o)},e,r,arguments.length)}}),St.each(["top","left"],function(e,t){St.cssHooks[t]=I(xt.pixelPosition,function(e,n){return n?(n=H(e,t),gn.test(n)?St(e).position()[t]+"px":n):void 0})}),St.each({Height:"height",Width:"width"},function(e,t){St.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){St.fn[r]=function(o,i){var a=arguments.length&&(n||"boolean"!=typeof o),s=n||(o===!0||i===!0?"margin":"border");return Mt(this,function(t,n,o){var i;return wt(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):void 0===o?St.css(t,n,s):St.style(t,n,o,s)},t,a?o:void 0,a)}})}),St.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){St.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),St.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),St.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),St.proxy=function(e,t){var n,r,o;return"string"==typeof t&&(n=e[t],t=e,e=n),bt(e)?(r=ct.call(arguments,2),o=function(){return e.apply(t||this,r.concat(ct.call(arguments)))},o.guid=e.guid=e.guid||St.guid++,o):void 0},St.holdReady=function(e){e?St.readyWait++:St.ready(!0)},St.isArray=Array.isArray,St.parseJSON=JSON.parse,St.nodeName=i,St.isFunction=bt,St.isWindow=wt,St.camelCase=h,St.type=r,St.now=Date.now,St.isNumeric=function(e){var t=St.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},"function"==typeof define&&define.amd&&define("jquery",[],function(){return St});var or=e.jQuery,ir=e.$;return St.noConflict=function(t){return e.$===St&&(e.$=ir),t&&e.jQuery===St&&(e.jQuery=or),St},t||(e._local_jQuery=e._local_$=St),St}),function(){return window.ConvergeEmbeddedPayment?void console.log("Checkout.js already loaded"):void(window.ConvergeEmbeddedPayment=new function(){function e(e){for(var t=document.getElementsByTagName("script"),n=0;n<t.length;++n){var r=t[n].src,o=r.indexOf(e);if(!(0>o))return r.substring(0,o)}}function t(){try{var e=JSON.parse(localStorage.getItem(x)),t=(new Date).getTime();for(var n in e.list)e.list.hasOwnProperty(n)&&t-e.list[n].timestamp>18e5&&delete e.list[n];localStorage.setItem(x,JSON.stringify(e))}catch(r){localStorage.removeItem(x)}}function n(e,t){try{if(!e)return;var n=JSON.parse(localStorage.getItem(x));n||(n={list:{}}),n.latestId=e,n.list[e]={content:t,timestamp:(new Date).getTime()},localStorage.setItem(x,JSON.stringify(n))}catch(r){}}function r(e){try{var t=JSON.parse(localStorage.getItem(x)),n=e.ssl_txn_auth_token;return n||(n=t.latestId),t.list[n].content}catch(r){}return void 0}function o(e,t,n,r){var o=_local_$.Deferred(),a=m;m=o,a?(console.log("Rest in progress, queue up request"),a.then(function(){i(e,t,n,r,o)})):i(e,t,n,r,o)}function i(e,t,o,i,a){var s={context:r(t),fields:t};return _local_$.post(g+"service/payment/hpe/"+e,JSON.stringify(s)).done(function(e){a.resolve();var t=i?e:JSON.parse(e);if(n(t.transactionTokenId,t.context),o.onRestResponse)return void o.onRestResponse(t);if(t.error)try{o.onError(t.errorMessage)}catch(r){}else if(t.dcc)try{o.onDCCDecision(t.fields,t.hashValue)}catch(r){}else if(t.threeDSecure)try{o.onThreeDSecure(t.fields,t.hashValue)}catch(r){}else if(t.approved)try{o.onApproval(t.fields,t.hashValue)}catch(r){}else try{o.onDeclined(t.fields,t.hashValue)}catch(r){}}).fail(function(e,t,n){a.resolve();try{o.onError(n)}catch(n){}})}function a(e,t){var n=_local_$.Deferred(),r={paypalCredit:t?"Y":"N",callbackUrl:g},i={};return i.onRestResponse=function(t){return e.payment=t,t.errorID||t.errorName?($log.error("Paypal Create Payment failed, "+t.errorName),null):void n.resolve(t.fields.paymentID)},o("ewallet/ppcreatepayment",r,i,!0),n.promise()}function s(e,t){var n={xid:e.payment.fields.ssl_xid,empiSessionId:e.resource.empiSessionId,ppaccesstoken:e.payment.fields.paypal_access_token,pppayerid:t.payerID,pppaymentid:t.paymentID};o("ewallet/ppexecutepayment",n,e.callback)}function u(e,t,n,r){var i=_local_$.Deferred();i.promise().then(function(n){var o={callback:r,resource:n},i={};t&&(i.label="credit"),paypal.Button.render({env:o.resource.environment,style:i,onCancel:function(){try{r.onCancelled()}catch(e){}},payment:function(){return a(o,t,r)},onAuthorize:function(e){s(o,e)}},"#"+e)});var u={};u.onRestResponse=function(e){if(e.error)try{r.onError(e.errorMessage)}catch(t){}else w?i.resolve(e):_local_$.getScript(e.scriptURL,function(){w=!0,i.resolve(e)})},o("ewallet/ppresource",n,u)}function l(e){c(e)}function c(e){var t=JSON.parse(e.checkoutResponse.fields.checkout);t.cartId=e.checkoutResponse.fields.cartId,t.callbackUrl=t.callbackUrl,masterpass.checkout(t)}function f(e){var t;try{for(var n=0;n<e.length;n++)t=t.length>0?t+" "+e[n]:e[n]}catch(r){}return t}function p(e){console.log("Apple Pay Payment, dataObj="+JSON.stringify(e));var t=JSON.stringify(e.token.paymentData),n=e.billingContact,r=e.shippingContact,o={};return n&&(o.ssl_first_name=n.givenName,o.ssl_last_name=n.familyName,o.ssl_city=n.locality,o.ssl_avs_zip=n.postalCode,o.ssl_state=n.administrativeArea,o.ssl_country=n.countryCode,o.ssl_avs_address=f(n.addressLines)),r&&(o.ssl_ship_to_first_name=r.givenName,o.ssl_ship_to_last_name=r.familyName,o.ssl_ship_to_address1=f(r.addressLines),o.ssl_ship_to_city=r.locality,o.ssl_ship_to_zip=r.postalCode,o.ssl_ship_to_country=r.countryCode,o.ssl_ship_to_phone=r.phoneNumber,o.ssl_email=r.emailAddress,o.ssl_ship_to_state=r.administrativeArea),o.ssl_applepay_web=t,o}function d(e){var t="US";"CAN"===e.resourceResponse.country&&(t="CA");var n={countryCode:t,currencyCode:e.resourceResponse.currency,total:{label:e.resourceResponse.merchantName,amount:e.resourceResponse.amount},requiredBillingContactFields:["name","postalAddress"],requiredShippingContactFields:e.resourceResponse.appleShippingFields,supportedNetworks:["amex","discover","masterCard","visa"],merchantCapabilities:["supports3DS"]};console.log("ApplePay request="+JSON.stringify(n));var r=new ApplePaySession(1,n);r.onvalidatemerchant=function(t){console.log("Merchant Validation - Start");var n={applepayUrl:t.validationURL,applepayDomain:window.location.hostname};console.log("ApplePay validation request="+JSON.stringify(n));var i={};i.onRestResponse=function(t){if(console.log("ApplePay validation response="+JSON.stringify(t)),t.fields.applepaySession)console.log("ApplePay Session created"),r.completeMerchantValidation(JSON.parse(t.fields.applepaySession));else{console.log("ApplePay Session error.");try{e.callback.onError("ApplePay session error")}catch(n){}}},o("ewallet/apvalidation",n,i)},r.onpaymentmethodselected=function(){console.log("onpaymentmethodselected - Start");var t={type:"final",label:e.resourceResponse.merchantName,amount:e.resourceResponse.amount},n=[{type:"final",label:e.resourceResponse.merchantName,amount:e.resourceResponse.amount}];console.log(t),console.log(n),r.completePaymentMethodSelection(t,n)},r.onpaymentauthorized=function(t){console.log("onpaymentauthorized - Start");var n=p(t.payment),i={};i.onRestResponse=function(t){if(t.error){try{e.callback.onError(t.errorMessage)}catch(n){}return void r.completePayment(ApplePaySession.STATUS_FAILURE)}if(t.approved){try{e.callback.onApproval(t.fields,t.hashValue)}catch(n){}r.completePayment(ApplePaySession.STATUS_SUCCESS)}else{try{e.callback.onDeclined(t.fields,t.hashValue)}catch(n){}r.completePayment(ApplePaySession.STATUS_FAILURE)}},o("ewallet/appayment",n,i)},r.oncancel=function(){console.log("session cancel");try{e.callback.onCancelled()}catch(t){}},r.begin()}var h,g,m,y="Checkout.js",v="EmbeddedPayment.js",x="convergeContext",b=!1,w=!1,C=!1;!function(){g=e(y),g||(g=e(v)),h=window.location.href,t()}(),this.pay=function(e,t){o("process",e,t)},this.threeDSecureReturn=function(e,t){var n={pares:e};o("three_d_return",n,t)},this.dccDecision=function(e,t){var n={dccoption:e?"Y":"N"};o("dcc_decision",n,t)},this.creditSurchargeDecision=function(e,t){var n=JSON.parse(e);if(n){var r={creditSurchargeAgree:"Y"};o("credit_surcharge_decision",r,t)}},this.initVisaCheckout=function(e,t,n){var r=this;if(!r.visaCheckoutInitialized){var i={};i.onRestResponse=function(i){if(i.error)try{n.onError(i.errorMessage)}catch(a){}else{r.visaCheckoutInitialized=!0;var s=_local_$("#"+e);s.addClass("v-checkout-wrapper");var u=i.buttonURL;_local_$("<img/>",{"class":"v-button",role:"button",alt:"Visa Checkout",width:"147",src:u}).appendTo(s),window.onVisaCheckoutReady=function(){V.init({apikey:i.convergeVisaAPIKey,externalClientId:i.visaClientCheckoutId,paymentRequest:{merchantRequestId:i.merchantId,currencyCode:i.currency,subtotal:i.amount},settings:{displayName:i.terminalName,websiteUrl:null,dataLevel:"FULL"},payment:{cardBrands:["VISA","MASTERCARD","AMEX","DISCOVER","ELECTRON"]}}),V.on("payment.success",function(e){t.ssl_visapayload=JSON.stringify(e),o("process",t,n)
}),V.on("payment.cancel",function(){n&&"function"==typeof n.onCancelled&&n.onCancelled()}),V.on("payment.error",function(e,t){n&&"function"==typeof n.onError&&n.onError(t)})},_local_$.getScript(i.scriptURL)}},o("ewallet/vcresource",t,i)}},this.initPayPalCheckout=function(e,t,n){u(e,!1,t,n)},this.initPayPalCredit=function(e,t,n){u(e,!0,t,n)},this.payByMasterpass=function(e,t){var n={osessionId:e.osessionId,tokenVerifier:e.oauth_verifier};o("ewallet/masterpass/v7/pay",n,t)},this.initMasterPass=function(e,t,n){if(!b){var r={callback:n},i={};i.onRestResponse=function(t){r.checkoutResponse=t,_local_$.getScript(t.scriptURL).done(function(){var n=_local_$("#"+e);_local_$("<img/>",{src:t.buttonURL}).appendTo(n),n.click(function(){l(r)})})},o("ewallet/masterpass/v7/initialize",t,i),b=!0}},this.initApplePay=function(e,t,n){if(!C)if(window.ApplePaySession&&window.ApplePaySession.canMakePayments){var r=_local_$("#"+e),i={callback:n},a={};a.onRestResponse=function(e){if(e.error)try{n.onError(e.error)}catch(t){}else i.resourceResponse=e,r.click(function(){d(i)})},o("ewallet/apresource",t,a),C=!0}else try{n.onError("ApplePay is not available")}catch(s){}},this.APMTransaction=function(e){var t={};t.onRestResponse=function(e){if(e.error)try{callback.onError(e.errorMessage)}catch(t){}else{console.log("Redirect URL="+e.fields.ssl_apm_redirect_url);var n=_local_$("<form>",{action:e.fields.ssl_apm_redirect_url,method:"post"}).appendTo("body");n.submit()}},o("apm/transaction",e,t)},this.APMReturn=function(e,t,n){var r={ssl_apm_txid:e,ssl_apm_cs:t};o("apm/return",r,n)},this.APMRefund=function(e,t){o("apm/refund",e,t)}})}();