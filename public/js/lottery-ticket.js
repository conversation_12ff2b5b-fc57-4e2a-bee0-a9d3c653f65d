function getlotteryTicketList() {

    fetch(config.hostApi+'/admin/lottery-ticket/get-ticket-list', {
            method: 'get',
            headers: {
                'Content-Type': 'application/json;charset=utf-8'
            }
        })
        .then((response) => {
            return response.json();
        })
        .then(values => {
            $('#lotteryTicketSection').DataTable({
                responsive: true,

                language: {
                    search: "",
                    searchPlaceholder: "Search"
                },
                dom: 'Bfrtip ',

                paging: true,
                data: values.map(x => {
                    return {
                        
                        id:x.id?x.id:"NA",
                       

                    };
                }),

                columns: [
                {
                    data: "id",
                    title: "ID",
                    class: "table-first-name"
                
                },
                {
                    title: "Action",
                    class: "table-first-name",
                    render : function (data, type, row){
                        return '<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter" onclick="getLotterTickById("'+data.id+'")">View Detail</button>';
                    }
                }]
            });
        });
}

function getLotterTickById(id){
console.log("function is called");
}





$(window).on("load", function () {
    getlotteryTicketList();
});


