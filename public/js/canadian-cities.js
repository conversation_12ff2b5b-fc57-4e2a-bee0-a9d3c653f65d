/**
 * List of major Canadian cities by province
 */
const canadianCities = {
  'AB': ['Calgary', 'Edmonton', 'Red Deer', 'Lethbridge', 'St. Albert', 'Medicine Hat', 'Grande Prairie', 'Airdrie', 'Spruce Grove', 'Leduc', 'Fort McMurray', 'Okotoks', 'Cochrane', 'Canmore', 'Brooks', 'Camrose', 'Beaumont', 'Cold Lake', 'Lloydminster', 'Wetaskiwin', 'Stony Plain', 'Chestermere', 'Sylvan Lake', 'Lacombe', 'Strathmore', 'Hinton', 'High River', 'Banff', 'Jasper'],
  'BC': ['Vancouver', 'Victoria', 'Surrey', '<PERSON><PERSON>', 'Richmond', 'Abbotsford', 'Coquitlam', 'Kelowna', 'Langley', 'Delta', 'Kamloops', 'Nanaimo', 'North Vancouver', 'Chilliwack', 'White Rock', 'Maple Ridge', 'Prince George', 'New Westminster', 'Port Coquitlam', 'West Vancouver', 'Vernon', 'Duncan', 'Court<PERSON>y', 'Campbell River', 'Penticton', 'Port Alberni', 'Parksville', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
  'MB': ['<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Portage la Prairie', 'Selkirk', 'Winkler', 'Dauphin', 'Morden', 'The Pas', 'Flin Flon', 'Stonewall', 'Virden', 'Altona', 'Beausejour', 'Carman', 'Neepawa', 'Swan River', 'Niverville', 'Carberry', 'Gimli', 'Minnedosa', 'Killarney', 'Melita', 'Roblin', 'Russell', 'Souris', 'Stonewall', 'Teulon', 'Winnipeg Beach'],
  'NB': ['Saint John', 'Moncton', 'Fredericton', 'Dieppe', 'Miramichi', 'Edmundston', 'Bathurst', 'Campbellton', 'Oromocto', 'Grand Falls', 'Shediac', 'Caraquet', 'Dalhousie', 'Sackville', 'St. Stephen', 'Sussex', 'Woodstock', 'Tracadie-Sheila', 'Shippagan', 'Neguac', 'Richibucto', 'Quispamsis', 'Rothesay', 'Hampton', 'St. Andrews', 'St. George', 'Bouctouche', 'Beresford', 'Riverview', 'Cap-Pelé'],
  'NL': ['St. John\'s', 'Conception Bay South', 'Mount Pearl', 'Corner Brook', 'Paradise', 'Grand Falls-Windsor', 'Gander', 'Labrador City', 'Stephenville', 'Torbay', 'Portugal Cove-St. Philip\'s', 'Marystown', 'Clarenville', 'Bay Roberts', 'Carbonear', 'Happy Valley-Goose Bay', 'Deer Lake', 'Bonavista', 'Placentia', 'Channel-Port aux Basques', 'Botwood', 'Lewisporte', 'Twillingate', 'Burin', 'Grand Bank', 'Bishop\'s Falls', 'Harbour Grace', 'Springdale', 'St. Anthony', 'Wabana'],
  'NS': ['Halifax', 'Dartmouth', 'Sydney', 'Truro', 'New Glasgow', 'Glace Bay', 'Kentville', 'Amherst', 'Bridgewater', 'Yarmouth', 'Antigonish', 'Wolfville', 'Windsor', 'Stellarton', 'Pictou', 'Port Hawkesbury', 'Lunenburg', 'Middleton', 'Digby', 'Westville', 'Berwick', 'Stewiacke', 'Springhill', 'Parrsboro', 'Oxford', 'Shelburne', 'Lockeport', 'Annapolis Royal', 'Canso', 'Louisbourg'],
  'ON': ['Toronto', 'Ottawa', 'Mississauga', 'Brampton', 'Hamilton', 'London', 'Markham', 'Vaughan', 'Kitchener', 'Windsor', 'Burlington', 'Sudbury', 'Oshawa', 'Barrie', 'Kingston', 'Cambridge', 'Guelph', 'Whitby', 'Thunder Bay', 'Waterloo', 'Brantford', 'Pickering', 'Niagara Falls', 'Peterborough', 'Sault Ste. Marie', 'Sarnia', 'Ajax', 'St. Catharines', 'Welland', 'North Bay', 'Belleville', 'Cornwall', 'Timmins', 'Newmarket', 'Stratford', 'Orillia', 'Aurora', 'Woodstock', 'St. Thomas', 'Chatham', 'Bradford', 'Orangeville', 'Owen Sound', 'Leamington', 'Bowmanville', 'Milton', 'Cobourg', 'Pembroke', 'Collingwood', 'Tillsonburg'],
  'PE': ['Charlottetown', 'Summerside', 'Stratford', 'Cornwall', 'Montague', 'Kensington', 'Souris', 'Alberton', 'Tignish', 'Georgetown', 'Borden-Carleton', 'O\'Leary', 'Cavendish', 'Hunter River', 'North Rustico', 'Crapaud', 'Victoria', 'Morell', 'Cardigan', 'Murray Harbour', 'Brackley', 'Miltonvale Park', 'Eastern Kings', 'Malpeque Bay', 'St. Peters Bay', 'Abram-Village', 'Wellington', 'Miscouche', 'Union Road', 'Hampshire'],
  'QC': ['Montreal', 'Quebec City', 'Laval', 'Gatineau', 'Longueuil', 'Sherbrooke', 'Saguenay', 'Lévis', 'Trois-Rivières', 'Terrebonne', 'Saint-Jean-sur-Richelieu', 'Repentigny', 'Brossard', 'Drummondville', 'Saint-Jérôme', 'Granby', 'Shawinigan', 'Blainville', 'Saint-Hyacinthe', 'Dollard-des-Ormeaux', 'Rimouski', 'Châteauguay', 'Mascouche', 'Sorel-Tracy', 'Victoriaville', 'Rouyn-Noranda', 'Salaberry-de-Valleyfield', 'Alma', 'Sept-Îles', 'Val-d\'Or', 'Baie-Comeau', 'Magog', 'Sainte-Julie', 'Varennes', 'Matane', 'Saint-Georges', 'Joliette', 'Thetford Mines', 'Rivière-du-Loup', 'Mirabel', 'La Tuque', 'Montmagny', 'Amos', 'Cowansville', 'Gaspé', 'Sainte-Anne-des-Monts', 'Dolbeau-Mistassini', 'Lachute', 'Chandler'],
  'SK': ['Saskatoon', 'Regina', 'Prince Albert', 'Moose Jaw', 'Swift Current', 'Yorkton', 'North Battleford', 'Estevan', 'Weyburn', 'Warman', 'Martensville', 'Humboldt', 'Meadow Lake', 'Melfort', 'Lloydminster', 'Kindersley', 'Melville', 'Nipawin', 'Tisdale', 'La Ronge', 'Canora', 'Rosetown', 'Assiniboia', 'Maple Creek', 'Outlook', 'Esterhazy', 'Biggar', 'Kamsack', 'Shaunavon', 'Watrous'],
  'NT': ['Yellowknife', 'Hay River', 'Inuvik', 'Fort Smith', 'Behchoko', 'Fort Simpson', 'Norman Wells', 'Tuktoyaktuk', 'Fort Liard', 'Fort Providence', 'Fort Resolution', 'Fort McPherson', 'Aklavik', 'Ulukhaktok', 'Sachs Harbour', 'Tsiigehtchic', 'Wrigley', 'Tulita', 'Deline', 'Colville Lake', 'Gameti', 'Wekweeti', 'Lutselk\'e', 'Enterprise', 'Kakisa', 'Jean Marie River', 'Nahanni Butte', 'Whati', 'Paulatuk', 'Sambaa K\'e'],
  'NU': ['Iqaluit', 'Rankin Inlet', 'Arviat', 'Baker Lake', 'Cambridge Bay', 'Igloolik', 'Pond Inlet', 'Pangnirtung', 'Kugluktuk', 'Cape Dorset', 'Gjoa Haven', 'Clyde River', 'Taloyoak', 'Kugaaruk', 'Coral Harbour', 'Sanikiluaq', 'Arctic Bay', 'Qikiqtarjuaq', 'Hall Beach', 'Resolute', 'Whale Cove', 'Chesterfield Inlet', 'Naujaat', 'Grise Fiord', 'Kimmirut', 'Nanisivik', 'Umingmaktok', 'Bathurst Inlet', 'Dundas Harbour', 'Nanook'],
  'YT': ['Whitehorse', 'Dawson City', 'Watson Lake', 'Haines Junction', 'Mayo', 'Faro', 'Carmacks', 'Teslin', 'Carcross', 'Ross River', 'Pelly Crossing', 'Beaver Creek', 'Destruction Bay', 'Old Crow', 'Burwash Landing', 'Stewart Crossing', 'Tagish', 'Keno City', 'Eagle Plains', 'Johnson\'s Crossing', 'Upper Liard', 'Swift River', 'Champagne', 'Silver City', 'Frances Lake', 'Klukshu', 'Aishihik', 'Jakes Corner', 'Braeburn', 'Snag']
};

// Flatten the cities array for easier searching
const allCanadianCities = Object.values(canadianCities).flat().sort();
