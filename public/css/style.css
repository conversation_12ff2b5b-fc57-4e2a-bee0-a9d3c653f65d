/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/

/* Width and height of the scrollbar */
::-webkit-scrollbar {
	width: 10px;
	height: 10px;
  }
  
  /* Background of the scrollbar track */
  ::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 5px;
  }
  
  /* Scrollbar handle */
  ::-webkit-scrollbar-thumb {
	background: #3cb1ff;
	box-shadow: 0px 0px 18px 6px black;
	border-radius: 5px;
  }
  
  /* Scrollbar handle on hover */
  ::-webkit-scrollbar-thumb:hover {
	background: #0096fa;
  }
  

body {
	color: #807474;
	font-family: "Open Sans", sans-serif;
	/* max-width: 1050px; */
	/* margin: 0 auto; */
	font-size: 20px;
}

.bodyBgImage {
    background-color: #fff8ee !important;
}

a {
	color: #807474;
	transition: 0.5s;
}

a:hover,
a:active,
a:focus {
	color: #0b6bd3;
	outline: none;
	text-decoration: none;
}

p {
	padding: 0;
	margin: 0 0 30px 0;
	font-size: medium;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: "Montserrat", sans-serif;
	/* font-weight: 800; */
	margin: 0 0 20px 0;
	margin-left: 2rem !important;
	padding: 0;
	color: #000;
	/* font-size: 20px; */
}


/* Back to top button */

.back-to-top {
	position: fixed;
	display: none;
	background: #000;
	color: #fff;
	width: 44px;
	height: 44px;
	text-align: center;
	line-height: 1;
	font-size: 16px;
	border-radius: 50%;
	right: 15px;
	bottom: 15px;
	transition: background 0.5s;
	z-index: 11;
}

.back-to-top i {
	padding-top: 12px;
	color: #fff;
}

@media (max-width: 768px) {
	.back-to-top {
		bottom: 15px;
	}
	.featurette-heading	{
		top:0px!important;
	}
	.newpos{
		position:relative!important;
		height: 60%;
	}
	.lottery-name p:nth-child(1){
		margin-left:0px!important;
	}
	.bluetext{
		padding-left:0px!important;
	}
	.lottery-name .grp-no{
		padding-left:0px!important;
	}
	.mtop61{
		top:0px!important;
	}
}


/* Prelaoder */

#preloader {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	overflow: hidden;
	background: #fff;
}

#preloader:before {
	content: "";
	position: fixed;
	top: calc(50% - 30px);
	left: calc(50% - 30px);
	border: 6px solid #f2f2f2;
	border-top: 6px solid #007bff;
	border-radius: 50%;
	width: 60px;
	height: 60px;
	-webkit-animation: animate-preloader 1s linear infinite;
	animation: animate-preloader 1s linear infinite;
}

@-webkit-keyframes animate-preloader {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes animate-preloader {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}


/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/

#header {
	/* max-width: 1050px; */
	/* height: 60px; */
	transition: all 0.5s;
	z-index: 997;
	transition: all 0.5s;
	/* padding: 3px 0; */
	background: #fff;
	box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.3);
	margin: 0 auto;
	/* border-top: 7px solid rgba(0, 123, 255, .5); */
}

/* #header.header-scrolled {
	height: 60px;
	padding: 3px 0;
} */

/* @media (max-width: 991px) {
	#header {
		height: 60px;
		padding: 3px 0;
	}
} */

#header .logo h1 {
	font-size: 36px;
	margin: 0;
	padding: 0;
	line-height: 1;
	font-weight: 400;
	letter-spacing: 3px;
	text-transform: uppercase;
}

@media (max-width: 991px) {
	#header .logo h1 {
		font-size: 28px;
		padding: 8px 0;
	}
}

#header .logo h1 a,
#header .logo h1 a:hover {
	color: #00366f;
	text-decoration: none;
}

#header .logo img {
	padding: 0;
	margin: 0px 0;
	max-height: 44px;
}


/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/


 
   nav {
    padding: 0.2rem!important;
    background-color: #17a2b8;
    /* background-color: #ff6348; */
    
}

.navbar-brand,
.navbar-nav {
    font-size: 22px!important;
     
    margin-left: 0px;
}




.nav-item {
    padding-right: 1.5rem!important;
}

.heroCard {
    opacity: 0.9;
    margin: 1rem;
}

.p-spacer {
    padding: 1rem 0rem;
}

.m-spacer {
    margin: 1rem 0rem;
}

.padding {
    margin: 0px 1rem 0rem 0rem;
}

.lotteryTotal {
    margin-bottom: 1rem;
}



  .bottom-bar{
    background-color: #17a2b8;
  }





/* Desktop Navigation Styles */
.main-nav,
.main-nav * {
	margin: 0;
	padding: 0;
	list-style: none;
}

.main-nav>ul>li {
	position: relative;
	white-space: nowrap;
	float: left;
}

.main-nav a {
	display: block;
	position: relative;
	color: #00b2f8;
	padding: 10px 15px;
	transition: 0.3s;
	font-size: 15px;
	font-family: "Montserrat", sans-serif; 
	font-weight: 500;
}

.main-nav a:hover,
.main-nav .active>a,
.main-nav li:hover>a {
	color: #FFF;
	text-decoration: none;
	background-color: #666666;
}

.main-nav .drop-down ul {
	display: block;
	position: absolute;
	left: 0;
	top: calc(100% + 30px);
	z-index: 99;
	opacity: 0;
	visibility: hidden;
	padding: 10px 0;
	background: #fff;
	box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
	transition: ease all 0.3s;
}

.main-nav .drop-down:hover>ul {
	opacity: 1;
	top: 100%;
	visibility: visible;
}

.main-nav .drop-down li {
	min-width: 180px;
	position: relative;
}

.main-nav .drop-down ul a {
	padding: 10px 20px;
	font-size: 13px;
	color: #004289;
}

.main-nav .drop-down ul a:hover,
.main-nav .drop-down ul .active>a,
.main-nav .drop-down ul li:hover>a {
	color: #007bff;
}

#baground_col{
	 background: #131645;
}
.main-nav .drop-down>a:after {
	content: "\f107";
	font-family: FontAwesome;
	padding-left: 10px;
}

.main-nav .drop-down .drop-down ul {
	top: 0;
	left: calc(100% - 30px);
}

.main-nav .drop-down .drop-down:hover>ul {
	opacity: 1;
	top: 0;
	left: 100%;
}

.main-nav .drop-down .drop-down>a {
	padding-right: 35px;
}

.main-nav .drop-down .drop-down>a:after {
	content: "\f105";
	position: absolute;
	right: 15px;
}


/* Mobile Navigation */

.mobile-nav {
	position: fixed;
	top: 0;
	bottom: 0;
	z-index: 9999;
	overflow-y: auto;
	left: -260px;
	width: 260px;
	padding-top: 18px;
	background: rgba(19, 39, 57, 0.8);
	transition: 0.4s;
}

.mobile-nav * {
	margin: 0;
	padding: 0;
	list-style: none;
}

.mobile-nav a {
	display: block;
	position: relative;
	color: #fff;
	padding: 10px 20px;
	font-weight: 500;
}

.mobile-nav a:hover,
.mobile-nav .active>a,
.mobile-nav li:hover>a {
	color: #74b5fc;
	text-decoration: none;
}

.mobile-nav .drop-down>a:after {
	content: "\f078";
	font-family: FontAwesome;
	padding-left: 10px;
	position: absolute;
	right: 15px;
}

.mobile-nav .active.drop-down>a:after {
	content: "\f077";
}

.mobile-nav .drop-down>a {
	padding-right: 35px;
}

.mobile-nav .drop-down ul {
	display: none;
	overflow: hidden;
}

.mobile-nav .drop-down li {
	padding-left: 20px;
}

.mobile-nav-toggle {
	position: fixed;
	right: 0;
	top: 0;
	z-index: 9998;
	border: 0;
	background: none;
	font-size: 24px;
	transition: all 0.4s;
	outline: none !important;
	line-height: 1;
	cursor: pointer;
	text-align: right;
}

.mobile-nav-toggle i {
	/* margin: 18px 18px 0 0; */
	margin: 10px 14px;

	/* color: #004289; */
	color: #fff;
}

.mobile-nav-overly {
	width: 100%;
	height: 100%;
	z-index: 9997;
	top: 0;
	left: 0;
	position: fixed;
	background: rgba(19, 39, 57, 0.8);
	overflow: hidden;
	display: none;
}

.mobile-nav-active {
	overflow: hidden;
}

.mobile-nav-active .mobile-nav {
	left: 0;
}

.mobile-nav-active .mobile-nav-toggle i {
	color: #fff;
}


/*--------------------------------------------------------------
# Intro Section
--------------------------------------------------------------*/

#intro {
	width: 100%;
	height: 615px;
	position: relative;
	/* background: url("../assets/img/banner.jpg") center bottom no-repeat; */
	/* background: linear-gradient(0deg, #4242426e, #4242426e), url("../assets/img/banner-img.jpg") center bottom no-repeat; */
	background: linear-gradient(0deg, #4242426e, #4242426e), url("../assets/img/lottery main pagecanstockphoto70510882.jpg") center bottom no-repeat;

	background-size: cover;
	/* padding: 490px 0 120px 0; */
	padding: 140px 0 120px;
}

@media (max-width: 991px) {
	#intro {
		padding: 140px 0 60px 0;
	}
}

@media (max-width: 574px) {
	#intro {
		padding: 235px 0 20px 0;
	}
	
}



#intro .intro-img {
	width: 50%;
	float: right;
}

@media (max-width: 991px) {
	#intro .intro-img {
		width: 80%;
		float: none;
		margin: 0 auto 25px auto;
	}
}

#intro .intro-info {
	width: 50%;
	float: left;
}

@media (max-width: 991px) {
	#intro .intro-info {
		width: 80%;
		float: none;
		margin: auto;
		text-align: center;
	}
}

@media (max-width: 767px) {
	#intro .intro-info {
		width: 100%;
	}
	.w40per{
		min-width:auto!important;
        max-width:599px!important;
		width:96%!important;
	}
}

#intro .intro-info h2 {
	color: #fff;
	margin-bottom: 40px;
	font-size: 48px;
	font-weight: 700;
}

#intro .intro-info h2 span {
	color: #74b5fc;
	text-decoration: underline;
}

@media (max-width: 767px) {
	#intro .intro-info h2 {
		font-size: 34px;
		margin-bottom: 30px;
	}
}

#intro .intro-info .btn-get-started,
#intro .intro-info .btn-services {
	font-family: "Montserrat", sans-serif;
	font-size: 14px;
	font-weight: 600;
	letter-spacing: 1px;
	display: inline-block;
	padding: 10px 32px;
	border-radius: 50px;
	transition: 0.5s;
	margin: 0 20px 20px 0;
	color: #fff;
}

#intro .intro-info .btn-get-started {
	background: #007bff;
	border: 2px solid #007bff;
	color: #fff;
}

#intro .intro-info .btn-get-started:hover {
	background: none;
	border-color: #fff;
	color: #fff;
}

#intro .intro-info .btn-services {
	border: 2px solid #fff;
}

#intro .intro-info .btn-services:hover {
	background: #007bff;
	border-color: #007bff;
	color: #fff;
}


/*--------------------------------------------------------------
# Sections
--------------------------------------------------------------*/

section {
	overflow: hidden;
}


/* Sections Header
--------------------------------*/

.section-header h3 {
	color: #807474;
	font-size: 20px;
	text-align: center;
	font-weight: 800;
	position: relative;
}

.section-header p {
	text-align: center;
	margin: auto;
	font-size: 20px;
	padding-bottom: 60px;
	color: #556877;
	width: 100%;
}

@media (max-width: 767px) {
	.section-header p {
		width: 100%;
	}
}


/* Section with background
--------------------------------*/

/* Section background - currently disabled */


/*--------------------------------------------------------------
# Breadcrumbs
--------------------------------------------------------------*/

.breadcrumbs {
	padding: 20px 0;
	background-color: #f5faff;
	min-height: 40px;
	margin-top: 80px;
}

@media (max-width: 992px) {
	.breadcrumbs {
		margin-top: 60px;
	}
}

.breadcrumbs h2 {
	font-size: 24px;
	font-weight: 300;
	margin: 0;
}

@media (max-width: 992px) {
	.breadcrumbs h2 {
		margin: 0 0 10px 0;
	}
}

.breadcrumbs ol {
	display: flex;
	flex-wrap: wrap;
	list-style: none;
	padding: 0;
	margin: 0;
	font-size: 14px;
}

.breadcrumbs ol li+li {
	padding-left: 10px;
}

.breadcrumbs ol li+li::before {
	display: inline-block;
	padding-right: 10px;
	color: #6c757d;
	content: "/";
}

@media (max-width: 768px) {
	.breadcrumbs .d-flex {
		display: block !important;
	}
	.breadcrumbs ol {
		display: block;
	}
	.breadcrumbs ol li {
		display: inline-block;
	}
}


/* About Us Section
--------------------------------*/

#about {
	/*background: #fff;*/
	padding: 60px 0;
}

#about .about-container .background {
	margin: 20px 0;
}

#about .about-container .content {
	background: #fff;
}

#about .about-container .title {
	color: #333;
	font-weight: 700;
	font-size: 32px;
}

#about .about-container p {
	line-height: 26px;
}

#about .about-container p:last-child {
	margin-bottom: 0;
}

#about .about-container .icon-box {
	background: #fff;
	background-size: cover;
	padding: 0 0 30px 0;
}

#about .about-container .icon-box .icon {
	float: left;
	background: #fff;
	width: 64px;
	height: 64px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	text-align: center;
	border-radius: 50%;
	border: 2px solid #007bff;
	transition: all 0.3s ease-in-out;
}

#about .about-container .icon-box .icon i {
	color: #007bff;
	font-size: 24px;
}

#about .about-container .icon-box:hover .icon {
	background: #007bff;
}

#about .about-container .icon-box:hover .icon i {
	color: #fff;
}

#about .about-container .icon-box .title {
	margin-left: 80px;
	font-weight: 600;
	margin-bottom: 5px;
	font-size: 18px;
}

#about .about-container .icon-box .title a {
	color: #283d50;
}

#about .about-container .icon-box .description {
	margin-left: 80px;
	line-height: 24px;
	font-size: 14px;
}

#about .about-extra {
	padding-top: 60px;
}

#about .about-extra h4 {
	font-weight: 600;
	font-size: 24px;
}


/* Services Section
--------------------------------*/

#services {
	padding: 60px 0 40px 0;
	box-shadow: inset 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
}

#services .box {
	padding: 30px;
	position: relative;
	overflow: hidden;
	border-radius: 10px;
	margin: 0 10px 40px 10px;
	background: #fff;
	box-shadow: 0 10px 29px 0 rgba(68, 88, 144, 0.1);
	transition: all 0.3s ease-in-out;
}

#services .box:hover {
	transform: translateY(-5px);
}

#services .icon {
	position: absolute;
	left: -10px;
	top: calc(50% - 32px);
}

#services .icon i {
	font-size: 64px;
	line-height: 1;
	transition: 0.5s;
}

#services .title {
	margin-left: 40px;
	font-weight: 700;
	margin-bottom: 15px;
	font-size: 18px;
}

#services .title a {
	color: #111;
}

#services .box:hover .title a {
	color: #007bff;
}

#services .description {
	font-size: 14px;
	margin-left: 40px;
	line-height: 24px;
	margin-bottom: 0;
}

#why-us {
	padding: 60px 0;
	background: #004a99;
}

#why-us .section-header h3,
#why-us .section-header p {
	color: #fff;
}

#why-us .card {
	background: #00458f;
	border-color: #00458f;
	border-radius: 10px;
	margin: 0 15px;
	padding: 15px 0;
	text-align: center;
	color: #fff;
	transition: 0.3s ease-in-out;
	height: 100%;
}

@media (max-width: 991px) {
	#why-us .card {
		margin: 0;
	}
}

#why-us .card:hover {
	background: #003b7a;
	border-color: #003b7a;
}

#why-us .card i {
	font-size: 48px;
	padding-top: 15px;
	color: #bfddfe;
}

#why-us .card h5 {
	font-size: 22px;
	font-weight: 600;
}

#why-us .card p {
	font-size: 15px;
	color: #d8eafe;
}

#why-us .card .readmore {
	color: #fff;
	font-weight: 600;
	display: inline-block;
	transition: 0.3s ease-in-out;
	border-bottom: #00458f solid 2px;
}

#why-us .card .readmore:hover {
	border-bottom: #fff solid 2px;
}

#why-us .counters {
	padding-top: 40px;
}

#why-us .counters span {
	font-family: "Montserrat", sans-serif;
	font-weight: bold;
	font-size: 48px;
	display: block;
	color: #fff;
}

#why-us .counters p {
	padding: 0;
	margin: 0 0 20px 0;
	font-family: "Montserrat", sans-serif;
	font-size: 14px;
	color: #cce5ff;
}


/* Portfolio Section
--------------------------------*/

#portfolio {
	padding: 60px 0;
	box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
}

#portfolio #portfolio-flters {
	padding: 0;
	margin: 5px 0 35px 0;
	list-style: none;
	text-align: center;
}

#portfolio #portfolio-flters li {
	cursor: pointer;
	margin: 15px 15px 15px 0;
	display: inline-block;
	padding: 6px 20px;
	font-size: 12px;
	line-height: 20px;
	color: #007bff;
	border-radius: 50px;
	text-transform: uppercase;
	background: #ecf5ff;
	margin-bottom: 5px;
	transition: all 0.3s ease-in-out;
}

#portfolio #portfolio-flters li:hover,
#portfolio #portfolio-flters li.filter-active {
	background: #007bff;
	color: #fff;
}

#portfolio #portfolio-flters li:last-child {
	margin-right: 0;
}

#portfolio .portfolio-item {
	position: relative;
	overflow: hidden;
	margin-bottom: 30px;
}

#portfolio .portfolio-item .portfolio-wrap {
	overflow: hidden;
	position: relative;
	border-radius: 6px;
	margin: 0;
}

#portfolio .portfolio-item .portfolio-wrap:hover img {
	opacity: 0.4;
	transition: 0.3s;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	text-align: center;
	opacity: 0;
	transition: 0.2s linear;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info h4 {
	font-size: 22px;
	line-height: 1px;
	font-weight: 700;
	margin-bottom: 14px;
	padding-bottom: 0;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info h4 a {
	color: #fff;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info h4 a:hover {
	color: #007bff;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info p {
	padding: 0;
	margin: 0;
	color: #e2effe;
	font-weight: 500;
	font-size: 14px;
	text-transform: uppercase;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-preview,
#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-details {
	display: inline-block;
	line-height: 1;
	text-align: center;
	width: 36px;
	height: 36px;
	background: #007bff;
	border-radius: 50%;
	margin: 10px 4px 0 4px;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-preview i,
#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-details i {
	padding-top: 6px;
	font-size: 22px;
	color: #fff;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-preview:hover,
#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-details:hover {
	background: #3395ff;
}

#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-preview:hover i,
#portfolio .portfolio-item .portfolio-wrap .portfolio-info .link-details:hover i {
	color: #fff;
}

#portfolio .portfolio-item .portfolio-wrap:hover {
	background: #003166;
}

#portfolio .portfolio-item .portfolio-wrap:hover .portfolio-info {
	opacity: 1;
}


/*--------------------------------------------------------------
# Portfolio Details
--------------------------------------------------------------*/

.portfolio-details {
	padding-top: 30px;
}

.portfolio-details .portfolio-details-container {
	position: relative;
}

.portfolio-details .portfolio-details-carousel {
	position: relative;
	z-index: 1;
}

.portfolio-details .portfolio-details-carousel .owl-nav,
.portfolio-details .portfolio-details-carousel .owl-dots {
	margin-top: 5px;
	text-align: left;
}

.portfolio-details .portfolio-details-carousel .owl-dot {
	display: inline-block;
	margin: 0 10px 0 0;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background-color: #ddd !important;
}

.portfolio-details .portfolio-details-carousel .owl-dot.active {
	background-color: #007bff !important;
}

.portfolio-details .portfolio-info {
	padding: 30px;
	position: absolute;
	right: 0;
	bottom: -70px;
	background: #fff;
	box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
	z-index: 2;
}

.portfolio-details .portfolio-info h3 {
	font-size: 22px;
	font-weight: 700;
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid #eee;
}

.portfolio-details .portfolio-info ul {
	list-style: none;
	padding: 0;
	font-size: 15px;
}

.portfolio-details .portfolio-info ul li+li {
	margin-top: 10px;
}

.portfolio-details .portfolio-description {
	padding-top: 50px;
}

.portfolio-details .portfolio-description h2 {
	width: 50%;
	font-size: 26px;
	font-weight: 700;
	margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
	padding: 0 0 0 0;
}

@media (max-width: 768px) {
	.portfolio-details .portfolio-description h2 {
		width: 100%;
	}
	.portfolio-details .portfolio-info {
		position: static;
		margin-top: 30px;
	}
}


/* Testimonials Section
--------------------------------*/

#testimonials {
	padding: 20px 0;
	/* background: url("../assets/img/homeballs.png") no-repeat right bottom; */
	/*box-shadow: inset 0px 0px 12px 0px rgba(0, 0, 0, 0.1);*/
}

#testimonials .section-header {
	margin-bottom: 10px;
}

@media (max-width: 767px) {
	#testimonials .testimonial-item {
		text-align: center;
	}
}

#testimonials .testimonial-item .testimonial-img {
	width: 70px;
	border-radius: 50%;
	border: 4px solid #8b8b8b;
	margin-top: 20px;
	margin-left: -44px;
}

blockquote {
	margin: 0em 35px;
	padding: 0.5em 10px;
	/* color:#807474; */
	color: #fff;
	font-size:20px;
}

blockquote::before {
	font-family: Arial;
	content: "\201C";
	color: #807474;
	color: #fff;
	font-size: 4em;
	position: absolute;
	left: 10px;
	top: -12px;
}

blockquote p {
	display: inline;
}

@media (max-width: 767px) {
	#testimonials .testimonial-item .testimonial-img {
		float: none;
		margin: auto;
	}
}

#testimonials .testimonial-item h3 {
	font-size: 20px;
	font-weight: 600;
	margin: -48px 0 5px 46px;
	color: #fff;
	/* color: #99d2fe; */
}

#testimonials .testimonial-item h4 {
	font-size: 14px;
	color: #999;
	margin: 0 0 15px 0;
	margin-left: 87px;
}

#testimonials .testimonial-item p {
	font-style: italic;
	margin: 0 0 0px 0px;
}

@media (min-width: 992px) {
	#testimonials .testimonial-item p {
		width: 90%;
	}
}

@media (max-width: 767px) {
	#testimonials .testimonial-item h3,
	#testimonials .testimonial-item h4,
	#testimonials .testimonial-item p {
		margin-left: 0;
	}
}

#testimonials .owl-nav,
#testimonials .owl-dots {
	/* margin-top: 5px; */
	margin-top: -45px;
	text-align: center;
}

#testimonials .owl-dot {
	display: inline-block;
	margin: 0 5px;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	/* background-color: #ddd; */
	background-color: #fff;
}

#testimonials .owl-dot.active {
	background-color: #000;
}


/* Contact Section
--------------------------------*/

#contact {
	/*box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1);*/
	padding: 30px;
	overflow: hidden;
	width: 50%;
	margin: -510px auto 20px auto;
	background: rgba(255, 255, 255, 0.8);
	position: relative;
}

#contact .section-header h3 {
	margin: 27px 0 20px 0
}

#contact .section-header {
	padding-bottom: 0px;
}

#contact .contact-about h3 {
	font-size: 36px;
	margin: 0 0 10px 0;
	padding: 0;
	line-height: 1;
	font-family: "Montserrat", sans-serif;
	font-weight: 300;
	letter-spacing: 3px;
	text-transform: uppercase;
	color: #007bff;
}

#contact .contact-about p {
	font-size: 14px;
	line-height: 24px;
	font-family: "Montserrat", sans-serif;
	color: #888;
}

#contact .social-links {
	padding-bottom: 20px;
}

#contact .social-links a {
	font-size: 18px;
	display: inline-block;
	background: #fff;
	color: #007bff;
	line-height: 1;
	padding: 8px 0;
	margin-right: 4px;
	border-radius: 50%;
	text-align: center;
	width: 36px;
	height: 36px;
	transition: 0.3s;
	border: 1px solid #007bff;
}

#contact .social-links a:hover {
	background: #007bff;
	color: #fff;
}

#contact .info {
	color: #283d50;
}

#contact .info i {
	font-size: 32px;
	color: #007bff;
	float: left;
	line-height: 1;
}

#contact .info p {
	padding: 0 0 10px 36px;
	line-height: 28px;
	font-size: 14px;
}

#contact .php-email-form .validate {
	display: none;
	color: red;
	margin: 0 0 15px 0;
	font-weight: 400;
	font-size: 13px;
}

#contact .php-email-form .error-message {
	display: none;
	color: #fff;
	background: #ed3c0d;
	text-align: left;
	padding: 15px;
	font-weight: 600;
}

#contact .php-email-form .error-message br+br {
	margin-top: 25px;
}

#contact .php-email-form .sent-message {
	display: none;
	color: #fff;
	background: #18d26e;
	text-align: center;
	padding: 15px;
	font-weight: 600;
}

#contact .php-email-form .loading {
	display: none;
	background: #fff;
	text-align: center;
	padding: 15px;
}

#contact .php-email-form .loading:before {
	content: "";
	display: inline-block;
	border-radius: 50%;
	width: 24px;
	height: 24px;
	margin: 0 10px -6px 0;
	border: 3px solid #18d26e;
	border-top-color: #eee;
	-webkit-animation: animate-loading 1s linear infinite;
	animation: animate-loading 1s linear infinite;
}

#contact .php-email-form input,
#contact .php-email-form textarea {
	border-radius: 0;
	box-shadow: none;
	font-size: 14px;
}

#contact .php-email-form input::focus,
#contact .php-email-form textarea::focus {
	background-color: #007bff;
}

#contact .php-email-form input {
	padding: 20px 15px;
}

#contact .php-email-form textarea {
	padding: 12px 15px;
}

#contact .php-email-form button[type="submit"] {
	background: #007bff;
	border: 0;
	border-radius: 20px;
	padding: 8px 30px;
	color: #fff;
	transition: 0.3s;
}

#contact .php-email-form button[type="submit"]:hover {
	background: #0067d5;
	cursor: pointer;
}

@-webkit-keyframes animate-loading {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes animate-loading {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}


/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/

#footer {
	/* background: #fff; */
	padding: 0 0 28px 0;
	color: #000;
	font-size: 14px;
}

#footer .footer-top {
	background: #e9ecef;
	padding: 15px 5px 5px 7%;
}

#footer .footer-top .footer-info {
	margin-bottom: 30px;
}

#footer .footer-top .footer-info h3 {
	font-size: 34px;
	margin: 0 0 20px 0;
	padding: 2px 0 2px 0;
	line-height: 1;
	font-family: "Montserrat", sans-serif;
	color: #fff;
	font-weight: 400;
	letter-spacing: 3px;
	text-transform: uppercase;
}

#footer .footer-top .footer-info p {
	font-size: 13px;
	line-height: 24px;
	margin-bottom: 0;
	font-family: "Montserrat", sans-serif;
	color: #000;
}

#footer .footer-top .social-links a {
	font-size: 18px;
	display: inline-block;
	background: #007bff;
	color: #fff;
	line-height: 1;
	padding: 8px 0;
	margin-right: 4px;
	border-radius: 50%;
	text-align: center;
	width: 36px;
	height: 36px;
	transition: 0.3s;
}

#footer .footer-top .social-links a:hover {
	background: #0067d5;
	color: #fff;
}

#footer .footer-top h4 {
	font-size: 14px;
	font-weight: bold;
	color: #000;
	text-transform: uppercase;
	position: relative;
	padding-bottom: 10px;
	margin: 0px;
}

#footer .footer-top .footer-links {
	margin-bottom: 5px;
}

#footer .footer-top .footer-links ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

#footer .footer-top .footer-links ul li {
	padding: 0px 0;
}

#footer .footer-top .footer-links ul li:first-child {
	padding-top: 0;
}

#footer .footer-top .footer-links ul a {
	color: #000;
	font-size: 15px;
}

#footer .footer-top .footer-links ul a:hover {
	color: #000;
}

#footer .footer-top .footer-contact {
	margin-bottom: 30px;
}

#footer .footer-top .footer-contact p {
	line-height: 26px;
}

#footer .footer-top .footer-newsletter {
	margin-bottom: 30px;
}

#footer .footer-top .footer-newsletter input[type="email"] {
	border: 0;
	padding: 6px 8px;
	width: 65%;
}

#footer .footer-top .footer-newsletter input[type="submit"] {
	background: #007bff;
	border: 0;
	width: 35%;
	padding: 6px 0;
	text-align: center;
	color: #fff;
	transition: 0.3s;
	cursor: pointer;
}

#footer .footer-top .footer-newsletter input[type="submit"]:hover {
	background: #0062cc;
}

#footer .copyright {
	text-align: center;
	padding-top: 20px;
	font-size: 15px;
	font-weight: bolder;
}

#footer .credits {
	text-align: center;
	font-size: 13px;
	color: #f1f7ff;
}

#footer .credits a {
	color: #bfddfe;
}

#footer .credits a:hover {
	color: #f1f7ff;
}

.bdr-left {
	border-left: 1px solid #cecece
}

#bg {
	background-color: #ffffff;
	/* padding: 20px 0 0; */
	overflow: hidden;
	width: 100%;
	margin: 0px auto 0px auto;
}

button[type="submit"]:focus {
	outline: 0;
	border: none;
}

.aboutus {
	text-align: center;
	width: 100%;
}

.register-btn {
	margin-top: 10px;
}

#playintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/lotteryplay-banner.png") center bottom no-repeat;
	background-size: cover;
	padding: 490px 0 120px 0;
}

#lotteriesintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/banner.jpg") center bottom no-repeat;
	background-size: cover;
	padding: 490px 0 120px 0;
}

#playintro2 {
	width: 100%;
	position: relative;
	/* background: url("../assets/img/letsplay.jpg") center bottom no-repeat; */
	background: url("../assets/img/canstockphoto57396045.jpg") center bottom no-repeat;
	background-size: cover;
	/* padding: 490px 0 120px 0; */
	padding: 355px 0 120px 0;
}

#signupintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/loginbg.png") center bottom no-repeat;
	background-size: cover;
	padding: 510px 0 180px 0;
}

#registerintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/registerbg.png") center bottom no-repeat;
	background-size: cover;
	padding: 490px 0 280px 0;
}

#lotteriesintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/lotteries-banner.jpg") center bottom no-repeat;
	background-size: cover;
	padding: 490px 0 120px 0;
}

#aboutintro {
	width: 100%;
	position: relative;
	/* background: url("../assets/img/happypeoples.png") center bottom no-repeat; */
	background: url("../assets/img/about_us_resized.png") center bottom no-repeat;
	background-size: cover;
	/* padding: 490px 0 120px 0; */
	/* padding: 410px 0 120px 0; */
	padding-bottom: calc(100vh - 90px);
}

#contactintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/contact-banner.jpg") center bottom no-repeat;
	background-size: cover;
	padding: 490px 0 120px 0;
}

#faqintro {
	width: 100%;
	position: relative;
	background: url("../assets/img/faq-banner.jpg") center bottom no-repeat;
	background-size: cover;
	padding: 334px 0 120px 0;
}

.h306 {
	height: 306px;
	overflow: hidden;
}

.lqp,
.qp {
	background-color: #fff;
	border-radius: 50px;
	padding: 5px;
	width: 60px;
	height: 60px;
	border: none;
	text-align: center;
	font-weight: 800;
	font-size: 30px;
	margin-right: 15px;
	box-shadow: 2px 2px 2px #c8c8c8;
}

/* ifYes background - currently disabled */

.p-0 {
	padding: 0px!important
}

.lottery-name p:nth-child(1) {
	font-weight: 700;
	font-size: 40px;
	position: relative;
	margin-top: -78px;
	margin-left: 188px;
	margin-bottom: 0px;
	line-height:40px;
}

.lottery-name p:nth-child(2) {
	margin-left: 0px;
}

.lottery-name small {
	background-color: #ffffff;
	font-size: 20px;
	margin-top:0px;
	font-weight:bold;
}

.lottery-name .person-no {
	background-color: #ffffff;
}

.lottery-name .grp-no {
	background-color: #ffffff;
	padding-left:0px;
	font-size: 20px;
}

nav.main-nav a.active {
	background-color: #666666!important;
	color: #fff!important;
}

.lightblue {
	background-color: #00abc6;
	border-color: #00abc6
}

.darkblue {
	background-color: #0e91a6;
	border-color: #0e91a6
}

.mt-30 {
	margin-top: 30px;
}

.blackbg {
	background-color: #fff;
	min-height: 180px;
	padding: 5px;
	width: 100%;
	position: absolute;
	left: 0px;
	right: 0px;
}

.cookie-disclaimer {
	background-color: #fff;
	color: #000;
	border-top: 7px solid rgba(0, 123, 255, .5);
	width: 100%;
	bottom: 0;
	left: 0;
	z-index: 999;
	margin: 0px;
	position: fixed;
}

.cookie-disclaimer .container {
	text-align: center;
	padding-top: 20px;
	padding-bottom: 20px;
}

.cookie-disclaimer h2 {
	color: #807474;
	font-size:20px;
}

.cookie-disclaimer .container p{
    color: #807474;
	font-size:20px;
	font-weight: bolder;
}

.cookie-disclaimer .btn-success {
	margin-right: 5px;
}

.ce-dismiss {
	position: absolute;
	font-size: 1.3em;
	right: 1em;
	top: 0em;
	color: #000;
}

.faq,
.terms,
.privacy {
	margin-top: 62px!important;
}

.cardheadernew {
	background: none!important;
	border-bottom: 7px solid #477faa!important;
	color: #477faa;
	font-weight: 800;
}

.mlr9 {
	margin: 0px 9px 9px 0px;
}

.bluebg {
	background-color: #76d4f8;
	border-radius: 8px;
	/* height: 50px */
}

.cardnew {
	border: none!important;
	background: none!important;
}

.cardbodyleft {
	background-color: #f5f5f5;
	border-radius: 12px!important;
	box-shadow: 2px 2px 2px #c8c8c8;
	margin-top: 17px;
}

.cardbodyright {
	background-color: #f8f8f8;
	border-radius: 25px!important;
	box-shadow: 0 0 2px 2px #c8c8c8;
	margin-top: 27px!important;
	font-size: 16px;
}

.bluetext {
	color: #35c2f9!important;
	position: relative;
	padding-left:0px;
	margin:0px;
}

/* Disabled newpos styles */
.newpos {
	position: absolute;
	z-index: 0;
}

input[type=checkbox],
input[type=radio] {
	position: relative;
	z-index: 10;
}

input:focus {
	outline: none;
}

.btn-primary {
	width: 115px;
	color: #fff;
	background-color: #45a5bf!important;
	border-color: #45a5bf!important;
	border: none;
	border-radius: 5px;
	margin: 0 auto;
}

.cardbodyright input.form-control {
	margin-bottom: 8px;
}

/* //.cardbodyright div b {
	//font-size: 16px;
//} */

.cardbodyright .text-muted {
	font-size: 16px;
}

.h375 {
	min-height: 467px;
	height: auto;
	margin-top: -10px!important;
	margin-bottom: 32px!important;
}

.mt165 {
	margin-top: -165px;
}

#contact .fa {
	font-size: 30px;
	position: relative;
	top: 42px;
	left: 8px;
	color: #868787;
}

hr.bdrblue {
	border: 2px solid #45a5bf;
}

#contact .form-control {
	padding: .375rem 0.75rem;
	border: none;
}

#register {
	/*box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1);*/
	padding: 30px;
	overflow: hidden;
	width: 65%;
	margin: -506px auto 25px auto;
	position: relative;
}

#register .form-control {
	padding: .375rem 0.75rem;
	border: none;
	height: 48px;
	line-height: 48px;
	font-weight: bolder;
}

.whitebg {
	float: left;
	width: 100%;
	background-color: #fff;
	border-radius: 20px;
	color: #000000;
	font-weight: bolder;
	padding: 10px;
	font-size: 15px;
	margin-bottom: 10px;
}

.mtop61 {
	top: -48px;
}

.subscribe {
	width: 80%;
	color: #000;
	background-color: rgba(255, 255, 255, 0.9);
	border: 2px solid #45a5bf;
	padding: 5px 10px;
	height: 30px;
	line-height: 20px;
	font-size: 16px;
	border-radius: 4px;
	font-weight: bold;
	box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.subscribebtn {
	background-color: #45a5bf;
	border: 1px solid #45a5bf;
	color: #fff;
	margin-left: 5px;
	height: 30px;
	line-height: 17px;
}

.subscribebtn:hover {
	color: #fff;
	background-color: #3b8ea3;
}

#lotteriesbg {
	margin: 90px 0px 0px 0px
}

.lotteriesbodyleft {
	background-color: #f5f5f5;
	border-radius: 25px!important;
	box-shadow: 2px 2px 2px #c8c8c8;
	margin-top: 17px;
	margin-right: 7px;
	margin-left: 4px;
	margin-bottom: 30px;
}

.w40per {
	width:93%;
	max-width:480px;
	min-width: 320px;
	min-height: 403px;
}

.featurette-heading {
	font-weight: 700;
	font-size: 30px;
	color: #35c2f9!important;
	position: relative;
	top: 56px;
}

.btn-learn {
	width:auto!important;
	color: #fff;
	background-color: #45a5bf!important;
	border-color: #45a5bf!important;
	border: none;
	border-radius: 5px;
	margin: 0 auto;
}

.btn-send{
	color: #fff;
    background-color: #45a5bf!important;
    border-color: #45a5bf!important;
    border: none;
}

.pdl{
	padding: .375rem 2.75rem!important;
}

.title{
	.input-group select {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: 0;
}

.input-group input[type="text"] {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}
	font-weight: 700;
    font-size: 40px;
    position: relative;
    margin-top: 10px;
    margin-left: 0px;
    margin-bottom: 0px;
    line-height: 40px;
    color: #35c2f9!important;
	/*text-align:center;*/
}
#dsphide{
	display:none;
}
.reviewbtn{
	margin-top:8px;
	padding:5px;
}

.footertext p{text-align:center; font-size: larger;}

.btn-success{background-color:#45a5bf!important;border:1px solid #45a5bf!important}
/* .btn-danger{background-color:#c8a12e!important;border:1px solid #c8a12e!important} */
.font_eighteen{font-size:18px;}
.font_sixteen{font-size:16px;color: #007bff;}
.font_sixteen a{font-size:14px;color: #007bff;}
.padding_twentyfive{padding:0 15% 10px 25%;}
.w160{width:160px;}
.margin_btm25{margin-bottom:25px;}
.clear{clear:both}
.total{display:inline-block}
.bold{font-weight:bold;}
.font_20{font-size:20px;}
.font_21{font-size:21px;}
.lead{font-size:18px!important}
.bdrradius{border-radius:40px;}

/*--------------------------------------------------------------
# YASH styles
--------------------------------------------------------------*/
#register .section-header h3 {
	color: #000;
	margin-bottom: 44px;
}
#registerintro {
	padding: 130px 0 80px;
}
#register {
	margin: auto 10%;
	width: auto;
}
#register form .btn {
	margin-top: 24px;
} 
#intro .intro-text {
	font-size: 40px;
	/* font-size: 80px; */
	text-align: center;
	color: #fff;
	/* font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; */
	font-family: auto;
	font-style: oblique;
	font-weight: 500;
	margin-top: 110px;
	margin-left: -80px;
}
.myUL li {
	margin-bottom: 12px;
	margin-top: 8px;
}

/* #################################
 Redesign changes
 ################################### */

 #header .top-bar {
	 background-color: #164a68;
}
#header {
	background-color: #7bc0c1;
	color: #fff;
	font-family: 'Assistant', sans-serif;
	font-size: 16px;
}
#header .bootom-bar {
	padding: 0 12px;

}
#header .bottom-bar a {
	display: flex;
	margin: auto 0.5rem;
	color: inherit;
	font-size: 16px;
}
nav.main-nav a.active, #header .bottom-bar i.fa:hover {
	color: rgba(22, 74, 104, 1) !important;
	background-color: inherit!important;
}
.main-nav a:hover,
.main-nav .active,
.main-nav li:hover>a {
	border-bottom: 2px solid rgba(22, 74, 104, 1);
	background-color: inherit;
}
body {
	background-color: rgb(245, 234, 219);
	font-family: 'Thasadith', sans-serif;
}
#bg ul {
	font-weight: bold;
	color: rgb(96, 96, 96);
	margin-left: 72px;
	margin-right: 72px;
}
#bg p {
	font-weight: bold;
	font-size: large;
	color: rgb(96, 96, 96);
	margin-left: 72px;
	margin-right: 72px;
}
#bg h1, #bg h2, #bg h3, #bg h4, #bg h5, #bg h6 {
	margin-left: 44px;
}
.light-pinkbg {
	background-color: rgb(255, 248, 238)!important;
}
.bg-themeblue {
	background-color: rgb(0, 151, 167) !important;
	color: #fff;
}
.bg-themedarkblue {
	background-color: #164a68!important;
	color: #fff;
}
#bg .bg-themeblue p, #bg .bg-themeblue ul, .bg-themeblue h2, #bg .bg-themedarkblue p, #bg .bg-themedarkblue ul, .bg-themedarkblue h2, .bg-themedarkblue h1, .bg-themeblue h1 {
	color: #fff;
}
#bg .img-fluid {
	max-height: 400px;
	object-fit: cover;
}
#accordion {
	margin-top: 12px;
	margin-bottom: 24px;
}
#aboutintro {
	background-position: center;
	background-origin: content-box;
}
#dsphide p {
	margin-right: 0;
	margin-left: 0;
}
.width100{
	width:100% !important;
}
@media (max-width: 768px) {
	#bg p, #bg ul {
		margin-left: 46px;
		margin-right: 46px;	
	}
	#bg h1, #bg h2, #bg h3, #bg h4, #bg h5, #bg h6 {
		margin-left: auto;
		text-align: center;
	}
	.qp {
		width: 24px;
		height: 24px;
		font-size: 12px;
	}
	#intro .intro-text {
		font-size: 40px;
		margin-top: 210px;
		text-align: center;
	}
}
@media (max-width: 500px) {
	#intro .intro-text {
		font-size: 40px;
		margin-top: 120px;
		text-align: center;
		margin-right: -55px;
	}
	#bg p, #bg ul {
		margin-left: 24px;
		margin-right: 24px;	
		font-size: 1.1rem;
	}
	blockquote {
		margin: 0;
	}
	blockquote::before  {
		left: -3px;
    top: -31px;
	}
	#testimonials .testimonial-item h3 {
		margin-top: 10px;
	}
	#contact{
		width:90% !important;
	}
}
