/* Custom styles for registration form */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-body.bodyBgImage {
  padding: 1.5rem !important;
  background-color: #fff8ee !important;
}

.form-group.mb-3 {
  margin-bottom: 1rem !important;
}

.form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
}

.form-check-input {
  margin-top: 0.25rem;
}

.btn-info {
  background-color: #17a2b8 !important;
  border-color: #17a2b8 !important;
  color: white !important;
}

.btn-lg.px-5 {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

/* Fix for country code dropdown */
select#countryCode {
  max-width: 120px !important;
  flex: 0 0 auto;
}
