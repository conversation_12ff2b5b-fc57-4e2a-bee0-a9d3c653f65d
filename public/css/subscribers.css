/* Custom styles for subscribers page */
.dataTables_wrapper {
  width: 100%;
  overflow-x: auto;
}

#subscribersTable {
  width: 100% !important;
  table-layout: fixed;
}

#subscribersTable th,
#subscribersTable td {
  white-space: normal;
  word-break: break-word;
  vertical-align: middle;
}

#subscribersTable th:nth-child(1),
#subscribersTable td:nth-child(1) {
  width: 50px !important;
  min-width: 50px !important;
  text-align: center;
}

#subscribersTable th:nth-child(2),
#subscribersTable td:nth-child(2) {
  width: 40% !important;
  min-width: 250px !important;
}

#subscribersTable th:nth-child(3),
#subscribersTable td:nth-child(3) {
  width: 30% !important;
  min-width: 180px !important;
}

#subscribersTable th:nth-child(4),
#subscribersTable td:nth-child(4) {
  width: 100px !important;
  min-width: 100px !important;
  text-align: center;
}

.export-csv {
  margin-bottom: 15px;
}

.card-body {
  padding: 20px;
}

.delete-btn {
  padding: 5px 10px;
}
