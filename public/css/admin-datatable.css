/* Custom DataTable styling for admin panel */

/* Table styling */
.custom-datatable {
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.custom-datatable .dataTable {
  width: 100% !important;
  border-collapse: separate;
  border-spacing: 0;
}

/* Header styling */
.custom-datatable .dataTable thead th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 12px 10px;
  border-bottom: 2px solid #dee2e6;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

/* Row styling */
.custom-datatable .dataTable tbody tr.data-row td {
  padding: 10px;
  vertical-align: middle;
  border-bottom: 1px solid #e9ecef;
}

.custom-datatable .dataTable tbody tr:hover {
  background-color: #f1f5f9;
}

/* Even/odd row styling */
.custom-datatable .dataTable tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* Button styling */
.custom-datatable .dt-buttons {
  margin-bottom: 15px;
}

.custom-datatable .dt-button {
  padding: 8px 16px;
  border-radius: 4px;
  margin-right: 8px;
}

/* Delete button styling */
.delete-user {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-user:hover {
  background-color: #c82333;
}

/* Pagination styling */
.custom-datatable .dataTables_paginate {
  margin-top: 15px;
}

.custom-datatable .paginate_button {
  padding: 5px 10px;
  margin: 0 3px;
  border-radius: 4px;
  cursor: pointer;
}

.custom-datatable .paginate_button.current {
  background-color: #007bff;
  color: white;
}

/* Search box styling */
.custom-datatable .dataTables_filter input {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin-left: 5px;
}

/* Info text styling */
.custom-datatable .dataTables_info {
  margin-top: 15px;
  color: #6c757d;
}

/* Responsive styling */
@media (max-width: 768px) {
  .custom-datatable {
    padding: 10px;
  }
  
  .custom-datatable .dataTable thead th {
    font-size: 11px;
    padding: 8px 5px;
  }
  
  .custom-datatable .dataTable tbody tr.data-row td {
    padding: 8px 5px;
    font-size: 12px;
  }
}
