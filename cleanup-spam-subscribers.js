const db = require('./lib/database');

async function cleanupSpamSubscribers() {
    try {
        console.log('🧹 Starting spam subscriber cleanup...');
        
        // Get all subscribers
        const [allSubscribers] = await db.pool.query('SELECT * FROM user_subscribe_list');
        console.log(`📊 Total subscribers: ${allSubscribers.length}`);
        
        // Define spam patterns
        const spamPatterns = [
            // Temporary email domains
            /@10minutemail\.com$/i,
            /@guerrillamail\.com$/i,
            /@mailinator\.com$/i,
            /@tempmail\.org$/i,
            /@throwaway\.email$/i,
            /@getnada\.com$/i,
            /@temp-mail\.org$/i,
            /@maildrop\.cc$/i,
            /@sharklasers\.com$/i,
            /@guerrillamailblock\.com$/i,
            /@pokemail\.net$/i,
            /@spam4\.me$/i,
            /@tempail\.com$/i,
            /@tempinbox\.com$/i,
            /@tempmailo\.com$/i,
            /@tempr\.email$/i,
            
            // Suspicious patterns
            /@example\.com$/i,
            /@test\.com$/i,
            /@fake\.com$/i,
            /@spam\.com$/i,
            
            // Random character patterns (likely bots)
            /^[a-z]{10,}@[a-z]{5,}\.com$/i, // Long random strings
            /^\d+@\d+\.com$/i, // Only numbers
            /^[a-z]{1,3}\d+@[a-z]{1,3}\d+\.com$/i, // Short letters + numbers
            
            // Known spam domains from your list
            /@austinpowder\.com$/i,
            /@warmntoasty\.com$/i
        ];
        
        // Find spam subscribers
        const spamSubscribers = allSubscribers.filter(subscriber => {
            const email = subscriber.email.toLowerCase();
            return spamPatterns.some(pattern => pattern.test(email));
        });
        
        console.log(`🚨 Found ${spamSubscribers.length} spam/suspicious subscribers:`);
        spamSubscribers.forEach(sub => {
            console.log(`   - ${sub.email} (ID: ${sub.id})`);
        });
        
        if (spamSubscribers.length > 0) {
            // Delete spam subscribers
            const spamIds = spamSubscribers.map(sub => sub.id);
            const placeholders = spamIds.map(() => '?').join(',');
            const deleteSql = `DELETE FROM user_subscribe_list WHERE id IN (${placeholders})`;
            
            const [deleteResult] = await db.pool.query(deleteSql, spamIds);
            console.log(`✅ Deleted ${deleteResult.affectedRows} spam subscribers`);
        }
        
        // Get remaining subscribers
        const [remainingSubscribers] = await db.pool.query('SELECT * FROM user_subscribe_list');
        const confirmedCount = remainingSubscribers.filter(sub => sub.is_confirmed === 1).length;
        const pendingCount = remainingSubscribers.filter(sub => sub.is_confirmed === 0).length;
        
        console.log('\n📈 Final Statistics:');
        console.log(`   Total remaining: ${remainingSubscribers.length}`);
        console.log(`   Confirmed: ${confirmedCount}`);
        console.log(`   Pending: ${pendingCount}`);
        
        console.log('\n🎯 Remaining subscribers:');
        remainingSubscribers.forEach(sub => {
            const status = sub.is_confirmed ? '✅ Confirmed' : '⏳ Pending';
            console.log(`   ${status}: ${sub.email}`);
        });
        
        console.log('\n🎉 Cleanup completed successfully!');
        
    } catch (error) {
        console.error('❌ Error during cleanup:', error);
    } finally {
        process.exit(0);
    }
}

// Run the cleanup
cleanupSpamSubscribers();
