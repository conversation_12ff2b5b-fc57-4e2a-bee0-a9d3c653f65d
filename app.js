require('dotenv').config();

// Set local mode by default for development
process.env.MODE = process.env.MODE || "LOCAL";

var createError = require('http-errors');
var express = require('express');
var bodyParser = require('body-parser');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
var session = require('express-session');
var https = require('https');
var fs = require('fs');

var indexRouter = require('./routes/index');
var adminRouter = require('./routes/admin');
var apiRouter = require('./routes/api');
var emailTransferRouter = require('./routes/email-transfer');
var instructionsRouter = require('./routes/instructions');

var app = express();

app.use(bodyParser.urlencoded({ extended: true })); // for parsing application/x-www-form-urlencoded

// Only load SSL certificates if not in local mode
let sslServerOptions = {};

if (process.env.MODE !== "LOCAL") {
  try {
    const key = fs.readFileSync('ssl_2024/_.letsplaygrouplottery.com_private_key.key', 'utf8');
    const cert = fs.readFileSync('ssl_2024/letsplaygrouplottery.com_ssl_certificate.cer', 'utf8');
    const ca = fs.readFileSync('ssl_2024/_.letsplaygrouplottery.com_ssl_certificate_INTERMEDIATE.cer', 'utf8');
    
    sslServerOptions = {
      key: key,
      cert: cert,
      ca: ca
    };
    
    console.log("SSL certificates loaded successfully");
  } catch (error) {
    console.error("Error loading SSL certificates:", error.message);
    console.log("Falling back to HTTP mode");
    process.env.MODE = "LOCAL";
  }
}

if (process.env.MODE !== "LOCAL") {
  app.all(/.*/, function (req, res, next) {
    const host = req.header("host");
    const isSecure = req.secure || req.headers['x-forwarded-proto'] === 'https';

    // Redirect www to non-www (since SSL cert is for letsplaygrouplottery.com)
    if (host.match(/^www\..*/i)) {
      const nonWwwHost = host.replace(/^www\./i, '');
      res.redirect(301, `https://${nonWwwHost}${req.url}`);
    } else if (!isSecure) {
      res.redirect(301, `https://${host}${req.url}`);
    } else {
      next();
    }
  });
}


app.use(session({
  cookieName: 'session',
  secret: 'eg[isfd-8yF9-7w2315df{}+Ijsli;;to8',
  resave: false,
  saveUninitialized: false,
  cookie: {
    maxAge: 1800000,  // 30 minutes
    httpOnly: true,   // Prevent XSS attacks
    secure: process.env.MODE !== "LOCAL", // HTTPS only in production
    sameSite: 'strict' // CSRF protection
  }
}));



// view engine setup
app.set('views',[path.join(__dirname, 'views'),path.join(__dirname,'views/admin')]);
app.set('view engine','pug');
//app.set('view engine','html');
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended: true}));

// Add input sanitization middleware
const security = require('./lib/security');
app.use(security.sanitizeBody);

// SECURITY HEADERS - Protection against common attacks
app.use((req, res, next) => {
  // Prevent clickjacking attacks
  res.setHeader('X-Frame-Options', 'DENY');

  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Prevent information disclosure
  res.removeHeader('X-Powered-By');

  // Content Security Policy (basic)
  res.setHeader('Content-Security-Policy', "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; img-src 'self' https: data:;");

  // Referrer Policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
});

app.use(logger('dev'));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.static(__dirname, { dotfiles: 'allow' } ));
app.use('/images', express.static(path.join(__dirname, 'public/uploads')));
// app.use(app);
app.use("/", indexRouter);
app.use("/admin/",adminRouter);
app.use("/api",apiRouter);
app.use("/email-transfer",emailTransferRouter);
app.use("/instructions",instructionsRouter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  next(createError(404));
});

// error handler
app.use(function(err, req, res, next) {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // render the error page
  res.status(err.status || 500);
  res.render('error');
});

//in env file port =5000   process.env.PORT=5000;
const PORT = process.env.PORT || 3000;
if(process.env.MODE == "LOCAL"){
  app.listen(PORT, () => {
    console.log(` Server started on PORT: ${PORT}`)
   })
}else{
  https.createServer(sslServerOptions, app).listen(PORT);
}

