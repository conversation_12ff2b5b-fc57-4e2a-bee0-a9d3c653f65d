module.exports = {
  apps: [{
    name: 'app',
    script: 'app.js',
    cwd: '/home/<USER>/errol3491-letsplaygroup_latest_updated-86613cc38e6c',
    env: {
      MODE: 'PRODUCTION',
      PORT: 3000,
      NODE_ENV: 'production',
      JWT_SECRET_KEY: 'gfg_jwt_let_play_lottery',
      TWOWAY_VERIFICATION: 'ON',
      MAILER_SEND_API_KEY: 'mlsn.7ce0afd1d7e527e0437a0ac458ab95789cd645e1e082e0e990c25fe6564fa07d',
      CLICK_SEND_API_KEY: '3601EBC0-1023-D730-3D09-12CBE49E3AD8',
      CLICK_SEND_NUMBER: '+18332405027',
      CLICK_SEND_USERNAME: '<EMAIL>',
      NODE_MAILER_USER: '<EMAIL>',
      NODE_MAILER_PASSWORD: 'letz365ch34CR$8utx96c',
      HOST_API: 'https://letsplaygrouplottery.com/',
      DB_NAME: 'errol',
      DB_PASSWORD: 'qwby:123eweD',
      DB_USERNAME: 'errol',
      DB_HOST: '*************',
      MYSQL_DATABASE: 'railway',
      MYSQL_PUBLIC_URL: 'mysql://root:<EMAIL>:3306/railway'
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/root/.pm2/logs/app-error.log',
    out_file: '/root/.pm2/logs/app-out.log',
    log_file: '/root/.pm2/logs/app-combined.log',
    time: true
  }]
};
