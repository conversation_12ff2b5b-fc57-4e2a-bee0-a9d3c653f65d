// Script to list available databases
const mysql = require('mysql2');

// Create connection without specifying a database
const connection = mysql.createConnection({
  host: '*************',
  user: 'errol',
  password: 'qwby:123eweD',
  connectTimeout: 20000,
  ssl: false
});

console.log('Attempting to connect to server...');

// Try to connect
connection.connect((err) => {
  if (err) {
    console.error('Error connecting to server:', err);
    process.exit(1);
  }
  
  console.log('Successfully connected to the server!');
  
  // List available databases
  connection.query('SHOW DATABASES', (err, results) => {
    if (err) {
      console.error('Error listing databases:', err);
      process.exit(1);
    }
    
    console.log('Available databases:');
    results.forEach(db => {
      console.log('- ' + db.Database);
    });
    
    // Close the connection
    connection.end();
  });
});
