-- Add email verification columns to user_subscribe_list table
USE errol;

-- Check if columns exist and add them if they don't
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'errol'
     AND TABLE_NAME = 'user_subscribe_list'
     AND COLUMN_NAME = 'confirmation_token') = 0,
    'ALTER TABLE user_subscribe_list ADD COLUMN confirmation_token VARCHAR(64) DEFAULT NULL COMMENT "Token for email confirmation"',
    'SELECT "confirmation_token column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'errol'
     AND TABLE_NAME = 'user_subscribe_list'
     AND COLUMN_NAME = 'is_confirmed') = 0,
    'ALTER TABLE user_subscribe_list ADD COLUMN is_confirmed TINYINT(1) DEFAULT 0 COMMENT "Whether email is confirmed (0=pending, 1=confirmed)"',
    'SELECT "is_confirmed column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'errol'
     AND TABLE_NAME = 'user_subscribe_list'
     AND COLUMN_NAME = 'confirmed_at') = 0,
    'ALTER TABLE user_subscribe_list ADD COLUMN confirmed_at TIMESTAMP NULL DEFAULT NULL COMMENT "When email was confirmed"',
    'SELECT "confirmed_at column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing subscribers to confirmed status (backward compatibility)
UPDATE user_subscribe_list
SET is_confirmed = 1, confirmed_at = created_at
WHERE confirmation_token IS NULL OR confirmation_token = '';

-- Show updated table structure
DESCRIBE user_subscribe_list;
