const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
    host: '*************',
    user: 'errol',
    password: 'qwby:123eweD',
    database: 'errol'
};

async function createAdmin() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        
        console.log('✅ Connected successfully!');
        
        // First, clear any existing admin data
        console.log('\n🗑️ Clearing existing admin data...');
        await connection.execute('DELETE FROM admin');
        
        // Insert new admin user
        console.log('\n➕ Creating admin user...');
        const [insertResult] = await connection.execute(
            'INSERT INTO admin (name, surname, emailid, password) VALUES (?, ?, ?, ?)',
            ['errol', 'fereira', '<EMAIL>', 'CdQC$M<@By[d8;96c']
        );
        
        if (insertResult.affectedRows > 0) {
            console.log('✅ Admin user created successfully!');
            
            // Verify the creation
            console.log('\n🔍 Verifying admin user:');
            const [verifyAdmin] = await connection.execute('SELECT * FROM admin');
            console.log(verifyAdmin[0]);
            
            console.log('\n🎉 SUCCESS! Admin login credentials:');
            console.log('📧 Email: <EMAIL>');
            console.log('🔑 Password: CdQC$M<@By[d8;96c');
            console.log('🌐 URL: https://letsplaygrouplottery.com/admin/login');
            
        } else {
            console.log('❌ Failed to create admin user');
        }
        
    } catch (err) {
        console.error('❌ Error:', err.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Database connection closed.');
        }
    }
}

// Run the creation
createAdmin();
