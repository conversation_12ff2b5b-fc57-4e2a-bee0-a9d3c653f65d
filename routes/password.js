var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require("../lib/database");
const auth = require('../lib/auth');
const sgMail = require('@sendgrid/mail');

const nodemailer = require('nodemailer');
const { sendMailByNodemailer } = require('../lib/mailer-send/nodeMailSender');

router.get('/', (req, res) => {
    res.render('changePassword', {
        title: 'Change Password'
    });
});

router.post('/', async (req, res) => {
    try {
        // Use parameterized query to prevent SQL injection
        const sql = 'SELECT * FROM user WHERE email_id = ?';
        const [results] = await db.pool.query(sql, [req.body.emailAddress]);
        
        if (results && results.length > 0) {
            // Creating token
            let jwtSecretKey = process.env.JWT_SECRET_KEY;
            let data = {
                userEmail: req.body.emailAddress
            }

            const token = jwt.sign({ exp: Math.floor(Date.now() / 1000) + (60 * 60), data }, jwtSecretKey);
            const passwordresetUrl = process.env.HOST_API + 'password-update?token=' + token;

            let emailstr = `
                <html>
                    <head>
                    <style>
                        .email-container {
                        font-family: Arial, sans-serif;
                        max-width: 500px;
                        margin: auto;
                        padding: 20px;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        background-color: #f9f9f9;
                        text-align: center;
                        }
                        .reset-button {
                        display: inline-block;
                        padding: 10px 20px;
                        margin: 20px 0;
                        font-size: 16px;
                        color: #fff;
                        background-color: #007bff;
                        text-decoration: none;
                        border-radius: 5px;
                        }
                        .reset-button:hover {
                        background-color: #0056b3;
                        }
                        .footer {
                        font-size: 12px;
                        color: #555;
                        margin-top: 20px;
                        }
                    </style>
                    </head>
                    <body>
                    <div class="email-container">
                        <h2>Hello, ${results[0].first_name} ${results[0].last_name}!</h2>
                        <p>You recently requested to reset your password. Click the button below to proceed:</p>
                        <a href="${passwordresetUrl}" class="reset-button">Reset Password</a>
                        <p>If the button above does not work, copy and paste the following link into your browser:</p>
                        <p><a href="${passwordresetUrl}">Click Here!</a></p>
                        <p class="footer">If you did not request this, please ignore this email.</p>
                    </div>
                    </body>
                </html>
                ​`;
                
            try {
                await sendMailByNodemailer({
                    to: req.body.emailAddress,
                    subject: "Let's play reset password!",
                    html: emailstr
                });
                res.render('updateMailMessage');
            } catch (err) {
                console.log(err);
                res.redirect('/changePassword?text=Something is wrong, kindly try after sometime.');
            }
        } else {
            res.redirect('/changePassword?text=Email not found');
        }
    } catch (error) {
        console.error('Error processing password reset:', error);
        res.redirect('/changePassword?text=Something is wrong, kindly try after sometime.');
    }
});

router.post('/password', auth, async function(req, res) {
    try {
        // Use parameterized query to prevent SQL injection
        const query = 'SELECT password FROM user WHERE id = ?';
        const [results] = await db.pool.query(query, [req.session.userid]);
        
        if (results && results.length > 0) {
            if (bcrypt.compareSync(req.body.password, results[0].password)) {
                const salt = bcrypt.genSaltSync();
                const hashpass = bcrypt.hashSync(req.body.confirmOldPassword, salt);
                
                // Use parameterized query for update
                const updateQuery = 'UPDATE user SET password = ? WHERE id = ?';
                await db.pool.query(updateQuery, [hashpass, req.session.userid]);
                
                res.json({
                    status: 200, 
                    msg: "Password successfully updated. Click OK to move to play page."
                });
            } else {
                res.json({
                    status: 300, 
                    msg: "Validation Failed!! Please check and submit correct old password."
                });
            }
        } else {
            res.json({
                status: 404, 
                msg: "User not found. Please login again."
            });
        }
    } catch (error) {
        console.error('Error updating password:', error);
        res.json({
            status: 500, 
            err: error, 
            msg: "Failed to execute. Please contact us and we'll fix it."
        });
    }
});

module.exports = router;