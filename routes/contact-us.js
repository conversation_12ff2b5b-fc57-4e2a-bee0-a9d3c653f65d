var express = require('express');
var router = express.Router();
var pool = require('../lib/database');
var auth = require('../lib/auth');
const { sendMailByNodemailer } = require('../lib/mailer-send/nodeMailSender');

/* GET Contact Us page */
router.get('/', function(req, res, next) {
    res.render('contactus', {
        title: 'Contact Us',
        isLogged: require("../lib/isLogged")(req)
    });
});

/* POST Contact Form Submission */
router.post('/submit', async function(req, res) {
    const { name, surname, email, need, message } = req.body;

    try {
        // Save to database (existing functionality)
        const query = `INSERT INTO contact_messages
            (first_name, last_name, email, inquiry_type, message)
            VALUES (?, ?, ?, ?, ?)`;

        await pool.execute(query, [name, surname, email, need, message]);

        // 📧 NEW: Send email notification to admin
        const currentDate = new Date().toLocaleString('en-US', {
            timeZone: 'America/Toronto',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const emailContent = `
            <html>
            <head>
                <style>
                    .email-container {
                        font-family: Arial, sans-serif;
                        max-width: 600px;
                        margin: auto;
                        padding: 20px;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        background-color: #f9f9f9;
                    }
                    .header {
                        background-color: #007bff;
                        color: white;
                        padding: 15px;
                        text-align: center;
                        border-radius: 8px 8px 0 0;
                        margin: -20px -20px 20px -20px;
                    }
                    .field {
                        margin: 10px 0;
                        padding: 8px;
                        background-color: white;
                        border-radius: 4px;
                    }
                    .label {
                        font-weight: bold;
                        color: #333;
                    }
                    .message-box {
                        background-color: white;
                        padding: 15px;
                        border-radius: 4px;
                        border-left: 4px solid #007bff;
                        margin: 15px 0;
                    }
                    .footer {
                        font-size: 12px;
                        color: #666;
                        text-align: center;
                        margin-top: 20px;
                        border-top: 1px solid #eee;
                        padding-top: 15px;
                    }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <div class="header">
                        <h2>🔔 New Contact Form Submission</h2>
                        <p>Let's Play Group Lottery Website</p>
                    </div>

                    <div class="field">
                        <span class="label">👤 Name:</span> ${name} ${surname}
                    </div>

                    <div class="field">
                        <span class="label">📧 Email:</span> ${email}
                    </div>

                    <div class="field">
                        <span class="label">📋 Inquiry Type:</span> ${need}
                    </div>

                    <div class="field">
                        <span class="label">📅 Submitted:</span> ${currentDate}
                    </div>

                    <div class="message-box">
                        <div class="label">💬 Message:</div>
                        <p>${message}</p>
                    </div>

                    <div class="footer">
                        <p>This email was sent automatically from your website contact form.</p>
                        <p>Website: <a href="https://letsplaygrouplottery.com">letsplaygrouplottery.com</a></p>
                    </div>
                </div>
            </body>
            </html>
        `;

        // Send email notification to admin (PRODUCTION EMAIL)
        try {
            await sendMailByNodemailer({
                to: '<EMAIL>', // PRODUCTION EMAIL
                subject: `🔔 New Contact Form Submission - ${name} ${surname}`,
                html: emailContent
            });
            console.log('✅ Contact form email notification sent successfully');
        } catch (emailError) {
            console.error('❌ Error sending contact form email:', emailError);
            // Don't fail the whole request if email fails
        }

        // Redirect to success page
        res.redirect('/contact-us/success');
    } catch (error) {
        console.error('Error saving contact message:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to save your message'
        });
    }
});

/* GET Success Page */
router.get('/success', function(req, res, next) {
    res.render('contact-success', {
        title: 'Message Sent Successfully',
        isLogged: require("../lib/isLogged")(req)
    });
});

/* GET Admin Messages List */
router.get('/admin/messages', auth, async function(req, res) {
    try {
        const query = `SELECT * FROM contact_messages ORDER BY created_at DESC`;
        const [messages] = await pool.query(query);

        res.render('admin/contact-messages', { 
            title: 'Contact Messages',
            messages: messages,
            isLogged: true
        });
    } catch (error) {
        console.error('Error fetching messages:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to fetch messages' 
        });
    }
});

/* PUT Update Message Status */
router.put('/admin/messages/:id', auth, async function(req, res) {
    const messageId = req.params.id;
    const { status } = req.body;

    try {
        const query = `UPDATE contact_messages SET status = ? WHERE id = ?`;
        await pool.execute(query, [status, messageId]);

        res.json({ 
            success: true, 
            message: 'Message status updated successfully' 
        });
    } catch (error) {
        console.error('Error updating message:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to update message status' 
        });
    }
});

module.exports = router;
