var express = require('express');
var router = express.Router();
var db = require('../../lib/database');

// get lottery master page
router.get('/', function(req, res, next) {
  res.render('lottery-master');
});

router.get('/get-ticket-list', async function(req, res, next) {
  try {
    const [results] = await db.pool.query("SELECT * FROM ticket");
    res.json(results);
  } catch (err) {
    console.error('Error fetching ticket list:', err);
    res.status(500).json({ error: 'Database error' });
  }
});
  

  module.exports = router;