var express = require('express');
var router = express.Router();
var db = require('../../lib/database');
// get company 
router.get('/', async function(req, res, next) {
    try {
        // Get active lotteries for the dropdown - use simpler approach
        const lotteryQuery = `
            SELECT DISTINCT lottery_name
            FROM lottery_master
            WHERE is_deleted = 0 OR is_deleted IS NULL
            ORDER BY lottery_name
        `;
        const [lotteries] = await db.pool.query(lotteryQuery);

        // Get available groups for paid customers
        const groupQuery = `
            SELECT DISTINCT group_no
            FROM ticket
            WHERE (payment_status = 1 OR payment_status = 'paid') AND group_no IS NOT NULL
            ORDER BY group_no
        `;
        const [groups] = await db.pool.query(groupQuery);

        res.render('users-list', {
            lotteries: lotteries,
            groups: groups
        });
    } catch (err) {
        console.error('Error rendering users list:', err);
        res.status(500).render('error', {
            message: 'Error loading users list',
            error: err
        });
    }
});

router.post('/get-user-list', async function(req, res, next) {
  const lotteryName = req.body.lotteryId; // Now receiving lottery name instead of ID
  const groupId = req.body.groupId;

  try {
    let sql = '';
    let params = [];

    if(lotteryName === null || lotteryName === '') {
      sql = `SELECT
      user.*,
      ticket.*,
      lottery_master.lottery_name,
      ticket.quickpick as ticket_quickpick,
      ticket.custom_number as ticket_custom_number
      FROM user
      JOIN ticket ON ticket.user_id = user.id
      JOIN lottery_master ON lottery_master.id = ticket.lottery_id
      WHERE ticket.payment_status = 1`;
    } else if(lotteryName !== null && groupId !== null) {
      sql = `SELECT user.*,
      ticket.*,
      lottery_master.lottery_name,
      ticket.quickpick as ticket_quickpick,
      ticket.custom_number as ticket_custom_number
      FROM user
      INNER JOIN ticket ON ticket.user_id = user.id
      INNER JOIN lottery_master ON lottery_master.id = ticket.lottery_id
      WHERE lottery_master.lottery_name = ? AND ticket.group_no = ?
      AND ticket.payment_status = 1`;
      params = [lotteryName, groupId];
    } else {
      sql = `SELECT user.*,
      ticket.*,
      lottery_master.lottery_name,
      ticket.quickpick as ticket_quickpick,
      ticket.custom_number as ticket_custom_number
      FROM user
      INNER JOIN ticket ON ticket.user_id = user.id
      INNER JOIN lottery_master ON lottery_master.id = ticket.lottery_id
      WHERE lottery_master.lottery_name = ?
      AND ticket.payment_status = 1`;
      params = [lotteryName];
    }

    const [result] = await db.pool.query(sql, params);

    // Return empty array if no results found
    if (!result || result.length === 0) {
      return res.send({userData: []});
    }

    // Process results to use correct quickpick and custom_number values
    const processedResult = result.map(row => ({
      ...row,
      quickpick: row.ticket_quickpick,
      custom_number: row.ticket_custom_number
    }));

    res.send({userData: processedResult});
  } catch (err) {
    console.error('Database error:', err);
    return res.status(500).send({error: 'Database error', message: err.message});
  }
});

  module.exports = router;