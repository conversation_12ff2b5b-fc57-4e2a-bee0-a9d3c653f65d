var express = require('express');
var router = express.Router();
var db = require('../../lib/database');
const security = require("../../lib/security");

// GET - Display all registered users
router.get('/', security.requireAdmin, async function(req, res, next) {
    try {
        // Fetch all registered users from database
        const sql = `
            SELECT
                id,
                first_name,
                last_name,
                email_id as email,
                phone_number,
                address,
                city,
                province,
                postal_code,
                profession,
                country,
                ip_address,
                data_time as registration_date,
                created_at
            FROM user
            ORDER BY created_at DESC
        `;

        const [users] = await db.query(sql);

        // Debug: Log sample users and total count
        console.log('Total users found:', users.length);
        if (users.length > 0) {
            console.log('First 3 users:', users.slice(0, 3));
        }

        // Format registration dates for display
        const formattedUsers = users.map(user => ({
            ...user,
            registration_date: user.registration_date ?
                new Date(user.registration_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : 'N/A'
        }));

        res.render('admin/all-registered-users', {
            title: 'All Registered Users',
            users: formattedUsers,
            totalUsers: users.length
        });

    } catch (err) {
        console.error('Error fetching registered users:', err);
        res.status(500).render('error', {
            message: 'Error loading registered users',
            error: err
        });
    }
});

// POST - Delete user
router.post('/delete/:id', security.requireAdmin, async function(req, res, next) {
    const userId = req.params.id;

    try {
        // Validate user ID
        if (!security.isValidId(userId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID'
            });
        }

        // Check if user exists
        const checkSql = 'SELECT id, first_name, last_name FROM user WHERE id = ?';
        const [existingUser] = await db.query(checkSql, [userId]);

        if (!existingUser || existingUser.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Delete user from database
        const deleteSql = 'DELETE FROM user WHERE id = ?';
        await db.query(deleteSql, [userId]);

        console.log(`✅ User deleted: ID ${userId} - ${existingUser[0].first_name} ${existingUser[0].last_name}`);

        res.json({
            success: true,
            message: `User ${existingUser[0].first_name} ${existingUser[0].last_name} has been deleted successfully`
        });

    } catch (err) {
        console.error('Error deleting user:', err);
        res.status(500).json({
            success: false,
            message: 'Error deleting user: ' + err.message
        });
    }
});

// GET - Search users (AJAX endpoint)
router.get('/search', security.requireAdmin, async function(req, res, next) {
    const searchTerm = req.query.q || '';

    try {
        const sql = `
            SELECT
                id,
                first_name,
                last_name,
                email_id as email,
                phone_number,
                address,
                city,
                province,
                postal_code,
                profession,
                country,
                ip_address,
                data_time as registration_date,
                created_at
            FROM user
            WHERE
                first_name LIKE ? OR
                last_name LIKE ? OR
                email_id LIKE ? OR
                phone_number LIKE ? OR
                ip_address LIKE ?
            ORDER BY created_at DESC
        `;

        const searchPattern = `%${searchTerm}%`;
        const [users] = await db.query(sql, [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern]);

        // Format registration dates
        const formattedUsers = users.map(user => ({
            ...user,
            registration_date: user.registration_date ?
                new Date(user.registration_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : 'N/A'
        }));

        res.json({
            success: true,
            users: formattedUsers,
            totalUsers: users.length
        });

    } catch (err) {
        console.error('Error searching users:', err);
        res.status(500).json({
            success: false,
            message: 'Error searching users'
        });
    }
});

module.exports = router;