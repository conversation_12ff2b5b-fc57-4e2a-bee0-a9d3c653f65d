var express = require('express');
var router = express.Router();
const db = require("../../lib/database");

// Get subscribers list
router.get('/', async function(req, res, next) {
  const sql = `SELECT *,
               CASE
                 WHEN is_confirmed = 1 THEN 'Confirmed'
                 ELSE 'Pending'
               END as status_text
               FROM user_subscribe_list
               ORDER BY is_confirmed DESC, created_at DESC`;
  
  try {
    const [subscribers] = await db.query(sql);
    
    res.render('admin/subscribers', { 
      title: 'Newsletter Subscribers',
      subscribers: subscribers
    });
  } catch (err) {
    console.error('Error fetching subscribers:', err);
    return res.render('admin/subscribers', { 
      title: 'Newsletter Subscribers',
      subscribers: [],
      error: 'Failed to fetch subscribers'
    });
  }
});

// Delete subscriber
router.get('/delete/:id', async function(req, res, next) {
  const subscriberId = req.params.id;
  const sql = 'DELETE FROM user_subscribe_list WHERE id = ?';
  
  try {
    await db.query(sql, [subscriberId]);
    res.redirect('/admin/subscribers');
  } catch (err) {
    console.error('Error deleting subscriber:', err);
    res.redirect('/admin/subscribers');
  }
});

// Delete all subscribers
router.get('/delete-all', async function(req, res, next) {
  const sql = 'DELETE FROM user_subscribe_list';
  
  try {
    await db.query(sql);
    res.redirect('/admin/subscribers');
  } catch (err) {
    console.error('Error deleting all subscribers:', err);
    res.redirect('/admin/subscribers');
  }
});

// Clean up subscribers - keep only genuine emails and delete test ones
router.get('/cleanup', async function(req, res, next) {
  // First, get all subscribers
  const selectSql = 'SELECT * FROM user_subscribe_list';
  
  try {
    const [subscribers] = await db.query(selectSql);
    
    // Identify test emails (those that don't use common email domains)
    const testEmails = subscribers.filter(subscriber => {
      const email = subscriber.email.toLowerCase();
      return !(
        email.includes('@gmail.com') || 
        email.includes('@yahoo.com') || 
        email.includes('@hotmail.com') || 
        email.includes('@outlook.com') || 
        email.includes('@icloud.com')
      );
    }).map(subscriber => subscriber.id);
    
    // If no test emails found, redirect back
    if (testEmails.length === 0) {
      return res.redirect('/admin/subscribers');
    }
    
    // Delete all test emails
    const deleteSql = 'DELETE FROM user_subscribe_list WHERE id IN (?)';
    
    await db.query(deleteSql, [testEmails]);
    res.redirect('/admin/subscribers');
  } catch (err) {
    console.error('Error during subscriber cleanup:', err);
    res.redirect('/admin/subscribers');
  }
});

module.exports = router;
