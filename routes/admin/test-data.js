const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const db = require('../../lib/database');

// Route to add test data to the database - DISABLED FOR PRODUCTION
router.get('/add-test-data-DISABLED', function(req, res) {
  return res.status(403).json({
    success: false,
    message: 'Test data route is disabled in production'
  });
  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../../test_data.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL content into individual statements
    const sqlStatements = sqlContent
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    // Execute each SQL statement
    let executedStatements = 0;
    let errors = [];
    
    // Function to execute statements sequentially using async/await
    const executeStatements = async () => {
      for (let i = 0; i < sqlStatements.length; i++) {
        const statement = sqlStatements[i];
        try {
          // Use the database pool for more reliable connections
          await db.query(statement);
          executedStatements++;
          console.log(`SQL statement ${i + 1}/${sqlStatements.length} executed successfully`);
        } catch (err) {
          console.error(`Error executing SQL statement ${i + 1}/${sqlStatements.length}:`, err);
          errors.push({
            statement: statement,
            error: err.message
          });
        }
      }
      
      // All statements processed
      return res.json({
        success: true,
        message: `Successfully executed ${executedStatements} out of ${sqlStatements.length} SQL statements`,
        errors: errors.length > 0 ? errors : null
      });
    };
    
    // Start executing statements
    executeStatements().catch(err => {
      console.error('Unexpected error during SQL execution:', err);
      res.status(500).json({
        success: false,
        message: 'Error executing SQL statements',
        error: err.message
      });
    });
    
  } catch (error) {
    console.error('Error reading or processing SQL file:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding test data',
      error: error.message
    });
  }
});

module.exports = router;
