var express = require('express');
var paymentRouter = express.Router();
var db = require('../../lib/database');
const nodemailer = require('nodemailer');
const { sendMailByNodemailer } = require('../../lib/mailer-send/nodeMailSender');

// Create a nodemailer transport using environment variables (same as other parts of the app)
const transport = nodemailer.createTransport({
  host: "smtp.ionos.com",  // Outgoing SMTP server
  port: 587,  // Use 587 with TLS
  secure: false,  // Must be false for TLS (port 587)
  auth: {
    user: process.env.NODE_MAILER_USER,
    pass: process.env.NODE_MAILER_PASSWORD
  },
  tls: {
    rejectUnauthorized: false, // Bypass SSL issues (use only for debugging)
  },
});

// Test route
paymentRouter.get('/test', function(req, res) {
  res.send("Hello");
});

// Test email route
paymentRouter.get('/test-email', async function(req, res) {
  try {
    console.log('Testing email configuration with proven nodeMailSender...');

    const result = await sendMailByNodemailer({
      to: '<EMAIL>', // Test email from the payment record
      subject: 'Test Email from Lottery System',
      html: '<h2>Test Email</h2><p>This is a test email to verify the email configuration is working.</p><p>If you receive this, the email system is functioning correctly.</p><p><strong>Sent at:</strong> ' + new Date().toISOString() + '</p>'
    });

    console.log('Test email sent successfully via nodeMailSender:', result);

    res.json({
      status: 'success',
      message: 'Test email sent successfully via proven nodeMailSender',
      result: result
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error sending test email: ' + error.message
    });
  }
});

// Main page route
paymentRouter.get('/', (req, res) => {
  res.render('confirm-payment');
});

// Get all pending payments
paymentRouter.get('/get-payments', async function(req, res, next) {
  try {
    const [result] = await db.pool.query("SELECT * FROM payment_ticket_details WHERE payment_status = 'PENDING' OR payment_status = 0");
    res.json({ result });
  } catch (err) {
    console.error('Error fetching payments:', err);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get payment details by ID
paymentRouter.post('/get-by-id/:id', async function(req, res, next) {
  try {
    console.log('Getting payment details for ID:', req.params.id);

    const paymentId = req.params.id;

    // Determine if the ID is numeric (payment_id) or string (lpg_payment_id)
    const isNumeric = /^\d+$/.test(paymentId);

    let paymentQuery, queryParams;
    if (isNumeric) {
      // If numeric, search by payment_id only
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE payment_id = ?';
      queryParams = [parseInt(paymentId)];
    } else {
      // If not numeric, search by lpg_payment_id only
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE lpg_payment_id = ?';
      queryParams = [paymentId];
    }

    // Use parameterized query for security
    const [paymentResults] = await db.pool.query(paymentQuery, queryParams);
    
    if (paymentResults.length === 0) {
      return res.status(404).json({ error: 'Payment not found' });
    }
    
    const finalResult = paymentResults[0];

    // Check if ticket_id exists and is a string before splitting
    let associated_tickets = [];
    if (finalResult.ticket_id && typeof finalResult.ticket_id === 'string') {
      associated_tickets = finalResult.ticket_id.split(",");
    } else if (finalResult.ticket_id) {
      // If ticket_id is a number, convert to string first
      associated_tickets = [finalResult.ticket_id.toString()];
    }

    const ticket_data = [];
    

    
    // Get details for each ticket
    for (let ticket of associated_tickets) {
      const ticketQuery = `
        SELECT
          ticket.*,
          lottery_master.lottery_name,
          lottery_master.lottery_price,
          lottery_master.person_per_group,
          lottery_master.image,
          lottery_master.bumper_prize,
          ticket.quickpick as ticket_quickpick,
          ticket.custom_number as ticket_custom_number
        FROM ticket
        JOIN lottery_master ON ticket.lottery_id = lottery_master.id
        WHERE ticket_id = ?
      `;
      const [ticketResults] = await db.pool.query(ticketQuery, [ticket]);
      if (ticketResults.length > 0) {
        // Use the aliased values for the response
        const ticketData = {
          ...ticketResults[0],
          quickpick: ticketResults[0].ticket_quickpick,
          custom_number: ticketResults[0].ticket_custom_number
        };
        ticket_data.push(ticketData);
      }
    }
    
    res.json({
      payment: finalResult,
      tickets: ticket_data
    });
  } catch (err) {
    console.error('Error getting payment details:', err);
    res.status(500).json({ error: 'Database error: ' + err.message });
  }
});

// Delete payment route
paymentRouter.delete('/delete/:id', async function(req, res, next) {
  try {
    console.log('Deleting payment with ID:', req.params.id);

    const paymentId = req.params.id;

    // Determine if the ID is numeric (payment_id) or string (lpg_payment_id)
    const isNumeric = /^\d+$/.test(paymentId);

    let paymentQuery, queryParams;
    if (isNumeric) {
      // If numeric, search by payment_id only
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE payment_id = ?';
      queryParams = [parseInt(paymentId)];
    } else {
      // If not numeric, search by lpg_payment_id only
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE lpg_payment_id = ?';
      queryParams = [paymentId];
    }

    // First, get the payment details to find associated tickets
    const [paymentResults] = await db.pool.query(paymentQuery, queryParams);

    if (paymentResults.length === 0) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    const payment = paymentResults[0];

    // Get associated tickets if they exist
    let associated_tickets = [];
    if (payment.ticket_id && typeof payment.ticket_id === 'string') {
      associated_tickets = payment.ticket_id.split(",");
    } else if (payment.ticket_id) {
      associated_tickets = [payment.ticket_id.toString()];
    }

    // Delete associated tickets first
    for (let ticketId of associated_tickets) {
      if (ticketId && ticketId.trim()) {
        await db.pool.query('DELETE FROM ticket WHERE ticket_id = ?', [ticketId.trim()]);
        console.log('Deleted ticket:', ticketId);
      }
    }

    // Delete the payment record using the same logic
    let deleteQuery, deleteParams;
    if (isNumeric) {
      deleteQuery = 'DELETE FROM payment_ticket_details WHERE payment_id = ?';
      deleteParams = [parseInt(paymentId)];
    } else {
      deleteQuery = 'DELETE FROM payment_ticket_details WHERE lpg_payment_id = ?';
      deleteParams = [paymentId];
    }

    await db.pool.query(deleteQuery, deleteParams);

    console.log('Payment deleted successfully:', paymentId);
    res.json({ status: 'success', message: 'Payment and associated tickets deleted successfully' });

  } catch (err) {
    console.error('Error deleting payment:', err);
    res.status(500).json({ error: 'Database error: ' + err.message });
  }
});

paymentRouter.post('/confirm', async function(req, res, next) {
  try {
    const currentDate = formatDate(new Date());
    const paymentId = req.body.id;

    // Determine if the ID is numeric (payment_id) or string (lpg_payment_id)
    const isNumeric = /^\d+$/.test(paymentId);

    let confirmQuery, queryParams;
    if (isNumeric) {
      // If numeric, update by payment_id only
      confirmQuery = 'UPDATE payment_ticket_details SET payment_status = ?, date_received = ? WHERE payment_id = ?';
      queryParams = ['COMPLETED', currentDate, parseInt(paymentId)];
    } else {
      // If not numeric, update by lpg_payment_id only
      confirmQuery = 'UPDATE payment_ticket_details SET payment_status = ?, date_received = ? WHERE lpg_payment_id = ?';
      queryParams = ['COMPLETED', currentDate, paymentId];
    }

    // Use parameterized queries for security
    await db.pool.query(confirmQuery, queryParams);
    
    const tickets = req.body.tickets.split(",");
    
    // Update each ticket status
    for (let ticket of tickets) {
      const confirmTicket = 'UPDATE ticket SET misc = ?, payment_status = ? WHERE ticket_id = ?';
      await db.pool.query(confirmTicket, ['payment confirmed', 1, ticket]);
    }
    
    await sendConfirmationEmail(req.body.id, tickets);
    res.json({ "status": "success" });
  } catch (err) {
    console.error('Error confirming payment:', err);
    res.status(500).json({ "status": "error", "data": err.message });
  }
});

// Send email route
paymentRouter.post('/send-email/:id', async function(req, res, next) {
  try {
    console.log('Sending email for payment ID:', req.params.id);

    const paymentId = req.params.id;

    // Determine if the ID is numeric (payment_id) or string (lpg_payment_id)
    const isNumeric = /^\d+$/.test(paymentId);

    let paymentQuery, queryParams;
    if (isNumeric) {
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE payment_id = ?';
      queryParams = [parseInt(paymentId)];
    } else {
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE lpg_payment_id = ?';
      queryParams = [paymentId];
    }

    // Get payment details
    const [paymentResults] = await db.pool.query(paymentQuery, queryParams);

    if (paymentResults.length === 0) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    const payment = paymentResults[0];

    // Get associated tickets
    let associated_tickets = [];
    if (payment.ticket_id && typeof payment.ticket_id === 'string') {
      associated_tickets = payment.ticket_id.split(",");
    } else if (payment.ticket_id) {
      associated_tickets = [payment.ticket_id.toString()];
    }

    if (associated_tickets.length === 0) {
      return res.status(400).json({ error: 'No tickets found for this payment' });
    }

    // Send confirmation email using existing function
    await sendConfirmationEmail(paymentId, associated_tickets);

    console.log('Email sent successfully for payment:', paymentId);
    res.json({ status: 'success', message: 'Confirmation email sent successfully' });

  } catch (err) {
    console.error('Error sending email:', err);
    res.status(500).json({ error: 'Error sending email: ' + err.message });
  }
});

async function sendConfirmationEmail(payment_id, tickets) {
  try {
    const ticketList = [];
    const ticketCount = tickets.length;

    // Determine if the ID is numeric (payment_id) or string (lpg_payment_id)
    const isNumeric = /^\d+$/.test(payment_id);

    let paymentQuery, queryParams;
    if (isNumeric) {
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE payment_id = ?';
      queryParams = [parseInt(payment_id)];
    } else {
      paymentQuery = 'SELECT * FROM payment_ticket_details WHERE lpg_payment_id = ?';
      queryParams = [payment_id];
    }

    // Get payment details
    const [paymentResults] = await db.pool.query(paymentQuery, queryParams);
    const paymentInfo = paymentResults[0];
    
    // Get ticket details
    for (let ticket of tickets) {
      const [ticketResults] = await db.pool.query(
        `SELECT
          ticket.*,
          lottery_master.lottery_name,
          lottery_master.lottery_price,
          lottery_master.person_per_group,
          lottery_master.image,
          lottery_master.bumper_prize,
          ticket.quickpick as ticket_quickpick,
          ticket.custom_number as ticket_custom_number
        FROM ticket
        JOIN lottery_master ON ticket.lottery_id = lottery_master.id
        WHERE ticket_id = ?`,
        [ticket]
      );
      if (ticketResults.length > 0) {
        // Use the aliased values for the email
        const ticketData = {
          ...ticketResults[0],
          quickpick: ticketResults[0].ticket_quickpick,
          custom_number: ticketResults[0].ticket_custom_number
        };
        ticketList.push(ticketData);
      }
    }
    
    // Create email content
    const displayPaymentId = paymentInfo.lpg_payment_id || paymentInfo.payment_id;
    const paymentString = `Hello,
      We have received your payment of $${paymentInfo.payment_amount}.00 for ${ticketCount} ticket(s) on ${formatDate(new Date())} from the email ${paymentInfo.payment_email_id} for payment id ${displayPaymentId}. Your ticket details are mentioned below.`;
    
    let ticketTable = `<table><thead><tr><th>Lottery Name</th><th>Player Name</th><th>Quick Pick/ Lucky Number</th><th>Group Number</th><th>Player #</th></tr></thead><tbody>`;
    
    for (let ticket of ticketList) {
      ticketTable += `<tr><td>${ticket.lottery_name}</td><td>${ticket.playerName}</td><td>${ticket.quickpick === 1 ? "Quick Pick" : ticket.custom_number}</td><td>${String.fromCharCode(64+parseInt(ticket.group_no))}</td><td>${ticket.person_in_group}</td></tr>`;
    }
    
    ticketTable += `</tbody></table>`;
    
    const finalHTML = `<p>${paymentString}</p><br>${ticketTable}<br><strong>For further information/ questions/ greivances, you can reach <NAME_EMAIL> or Call 1800-000-000-0000</strong><br><strong>Please note that by confirming your ticket, you acknowledge that you are in the province of Ontario and 19 yrs or older</strong>`;
    
    console.log('Sending confirmation email');
    await sendEmail("<EMAIL>", paymentInfo.payment_email_id, `Ticket Confirmation for payment ID ${displayPaymentId}`, finalHTML, true);
  } catch (err) {
    console.error('Error sending confirmation email:', err);
  }
}

function formatDate(date) {
  var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

  if (month.length < 2)
      month = '0' + month;
  if (day.length < 2)
      day = '0' + day;

  return [year, month, day].join('-');
}

async function sendEmail(from, to, subject, body, isHTML = false) {
  try {
    console.log('Sending email using proven nodeMailSender...');
    console.log('Email details:', { from, to, subject, isHTML });

    // Use the proven working email function from other parts of the app
    const result = await sendMailByNodemailer({
      to: to,
      subject: subject,
      html: isHTML ? body : `<p>${body}</p>`
    });

    console.log('Email sent successfully via nodeMailSender:', result);
    return result;
  } catch (err) {
    console.error('Error sending email via nodeMailSender:', err);
    throw err;
  }
}

module.exports = paymentRouter
