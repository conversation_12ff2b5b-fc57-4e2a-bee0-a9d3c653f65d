var express = require('express');
var router = express.Router();
var db = require("../../lib/database");
const security = require("../../lib/security"); // Security utilities
var multer = require('multer')
const {join} = require("node:path");
const {mkdirSync, existsSync} = require("node:fs");
const mime = require('mime-types'); // Import the mime-types module

// Ensure the directory exists
const uploadDir = join(__dirname, '../../public/assets/images/logo/');
if (!existsSync(uploadDir)) {
  mkdirSync(uploadDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Extract the file extension from the mime type
    const ext = mime.extension(file.mimetype) || 'png'; // Default to png if mime type is unknown
    // Sanitize the lottery name for filename (replace special characters)
    const sanitizedName = req.body.inputLotteryName.replace(/[\/\\:*?"<>|]/g, '-');
    const imageName = sanitizedName + '.' + ext;
    cb(null, imageName);
  },
});

const fileFilter = (req, file, cb) => {
  // SECURITY: Comprehensive file validation
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

  // Check MIME type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed.'), false);
  }

  // Check file extension
  const fileExt = require('path').extname(file.originalname).toLowerCase();
  if (!allowedExtensions.includes(fileExt)) {
    return cb(new Error('Invalid file extension. Only .jpg, .jpeg, .png, .gif, .webp are allowed.'), false);
  }

  // Check file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    return cb(new Error('File too large. Maximum size is 5MB.'), false);
  }

  // Validate filename (no dangerous characters)
  if (/[<>:"/\\|?*\x00-\x1f]/.test(file.originalname)) {
    return cb(new Error('Invalid filename. Contains dangerous characters.'), false);
  }

  cb(null, true);
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1, // Only 1 file at a time
    fields: 20 // Limit form fields
  }
});


// get all lotteries - SECURITY: Admin authentication required
router.get('/', security.requireAdmin, async function (req, res, next) {
  try {
    let sql = 'SELECT * FROM lottery_master';
    const [result] = await db.pool.query(sql);
    res.render('lottery', { lotteries: result });
  } catch (err) {
    console.error('Database error:', err);
    res.status(500).render('error', { message: 'Database error', error: err });
  }
});

// Helper function to handle lottery routes
async function handleLotteryRoute(req, res, searchTerm, title) {
  try {
    const sql = `SELECT * FROM lottery_master WHERE lottery_name LIKE '%${searchTerm}%'`;
    const [result] = await db.pool.query(sql);
    res.render('lottery', { lotteries: result, title: title });
  } catch (err) {
    console.error('Database error:', err);
    res.status(500).render('error', { message: 'Database error', error: err });
  }
}

// Routes for specific lottery types
router.get('/lotto-6-49', function (req, res, next) {
  handleLotteryRoute(req, res, 'Lotto 6/49', 'Lotto 6/49');
});

router.get('/lotto-max', function (req, res, next) {
  handleLotteryRoute(req, res, 'Lotto Max', 'Lotto Max');
});

router.get('/sick-kids', function (req, res, next) {
  handleLotteryRoute(req, res, 'Sick Kids', 'Sick Kids lottery');
});

router.get('/heart-stroke', function (req, res, next) {
  handleLotteryRoute(req, res, 'Heart and stroke', 'Heart and stroke lottery');
});

router.get('/canadian-cancer', function (req, res, next) {
  handleLotteryRoute(req, res, 'Canadian Cancer', 'Canadian Cancer lottery');
});

router.get('/princes-magarate', function (req, res, next) {
  handleLotteryRoute(req, res, 'Princes Magarate', 'Princes Magarate lottery');
});

router.get('/ultimate-dream', function (req, res, next) {
  handleLotteryRoute(req, res, 'Ultimate Dream', 'Ultimate Dream lottery');
});

router.get('/rotary', function (req, res, next) {
  handleLotteryRoute(req, res, 'Rotary', 'Rotary lottery');
});

router.post('/add-lottery', security.requireAdmin, (req, res, next) => {
  // Handle multer errors
  upload.single('imageFile')(req, res, (err) => {
    if (err) {
      console.error('File upload error:', err.message);
      return res.status(400).json({ error: err.message });
    }
    next();
  });
}, async function (req, res, next) {
  try {
    const {
      inputLotteryName,
      inputStartDate,
      inputEndDate,
      inputLastDateJoin,
      inputBumperPrice,
      inputGroupNumber,
      inputPersonPerGroup,
      inputCountry,
      inputState,
      inputSubscription,
      inputLotteryrice,
      isEncore,
      quickpick,
      customeNumber,
      cardColorDark,
      cardColorLight
    } = req.body;
    
    // Sanitize the lottery name for filename (replace special characters)
    const sanitizedName = inputLotteryName.replace(/[\/\\:*?"<>|]/g, '-');
    const imageName = sanitizedName + '.png';
    
    // Use parameterized query to prevent SQL injection
    const sql = 'INSERT INTO lottery_master (`lottery_name`,`start_date`,`end_date`,`date_of_joining`,`bumper_prize`,`no_of_groups`,`person_per_group`,`country`,`state`,`subscription_entry_price`,`lottery_price`,`encore`,`quickpick`,`custom_number`,`card_color_dark`,`card_color_light`,`image`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
    
    const params = [
      inputLotteryName,
      inputStartDate,
      inputLastDateJoin,
      inputEndDate,
      inputBumperPrice,
      inputGroupNumber,
      inputPersonPerGroup,
      inputCountry,
      inputState,
      inputSubscription,
      inputLotteryrice,
      isEncore,
      quickpick,
      customeNumber,
      cardColorDark || '#FFFFFF',
      cardColorLight || '#FFFFFF',
      imageName
    ];
    
    await db.pool.query(sql, params);
    res.redirect("/admin/lottery?success=Lottery added successfully");
  } catch (err) {
    console.error('Error adding lottery:', err);
    res.status(500).render('error', { message: 'Error adding lottery', error: err });
  }
});



router.post('/update-lottery', async function (req, res) {
  try {
    const {
      lotteryId,
      lotteryName,
      lotteryStartDate,
      lotteryEndDate,
      inputLastDateJoin,
      lotteryPrice,
      lotteryBumperPrice,
      lotteryGroupNumber,
      lotteryPersonPerGroup,
      lotteryCountry,
      lotteryState,
      lotterysubscriptionEntryPrice,
      isDeleted,
      isEncore,
      QuickPick,
      customerNumber,
      isActive,
      cardColorDark,
      cardColorLight
    } = req.body;
    
    // Use parameterized query to prevent SQL injection
    const sql = `UPDATE lottery_master SET
      lottery_name = ?,
      start_date = ?,
      end_date = ?,
      date_of_joining = ?,
      lottery_price = ?,
      bumper_prize = ?,
      no_of_groups = ?,
      person_per_group = ?,
      country = ?,
      state = ?,
      subscription_entry_price = ?,
      is_deleted = ?,
      encore = ?,
      quickpick = ?,
      custom_number = ?,
      is_active = ?,
      card_color_dark = ?,
      card_color_light = ?
      WHERE id = ?`;

    const params = [
      lotteryName,
      lotteryStartDate,
      lotteryEndDate,
      inputLastDateJoin,
      lotteryPrice,
      lotteryBumperPrice,
      lotteryGroupNumber,
      lotteryPersonPerGroup,
      lotteryCountry,
      lotteryState,
      lotterysubscriptionEntryPrice,
      isDeleted === 'on' ? 1 : 0,
      isEncore === 'on' ? 1 : 0,
      QuickPick === 'on' ? 1 : 0,
      customerNumber === 'on' ? 1 : 0,
      isActive === 'on' ? 1 : 0,
      cardColorDark || '#FFFFFF',
      cardColorLight || '#FFFFFF',
      lotteryId
    ];
    
    await db.pool.query(sql, params);
    res.redirect("/admin/lottery?success=Lottery updated successfully");
  } catch (err) {
    console.error('Error updating lottery:', err);
    res.status(500).render('error', { message: 'Error updating lottery', error: err });
  }
});

// Route to delete specific test lottery entries
router.get('/cleanup-test-entries', async function (req, res, next) {
  try {
    // Names of entries to delete
    const namesToDelete = [
      'jayesh mali', 'dreamer lottery', 'Errol 2', 'Errol', 
      'test1', 'test2', 'test4', '<scripr>alert(9)</script>', 'abcd', 'test'
    ];
    
    // First, get the IDs of these entries
    let placeholders = namesToDelete.map(() => '?').join(', ');
    const findSql = `SELECT id FROM lottery_master WHERE lottery_name IN (${placeholders})`;
    
    const [results] = await db.pool.query(findSql, namesToDelete);
    
    if (results.length === 0) {
      console.log('No matching test lottery entries found');
      return res.redirect('/admin/lottery');
    }
    
    // Extract the IDs
    const idsToDelete = results.map(result => result.id);
    console.log('Found IDs to delete:', idsToDelete);
    
    // Delete the entries by ID
    const deleteSql = 'DELETE FROM lottery_master WHERE id IN (?)';
    
    const [deleteResult] = await db.pool.query(deleteSql, [idsToDelete]);
    console.log('Successfully deleted', deleteResult.affectedRows, 'test lottery entries');
    
    res.redirect('/admin/lottery');
  } catch (err) {
    console.error('Error in cleanup-test-entries:', err);
    res.status(500).render('error', { message: 'Error cleaning up test entries', error: err });
  }
});

module.exports = router;