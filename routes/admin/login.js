var express = require('express');
var router = express.Router();
var db = require("../../lib/database"); // Use the connection pool instead of dataHandler
const security = require("../../lib/security"); // Security utilities


router.get('/', function (req, res, next) {
  res.render('admin-login', {
    title: 'Login'
  });
});

// Add rate limiting to login attempts (temporarily disable sanitization for testing)
router.post('/', async function (req, res, next) {
  let data = req.body;
  console.log('=== ADMIN LOGIN ATTEMPT ===');
  console.log('Full request body:', JSON.stringify(req.body, null, 2));
  console.log('Email field:', data.email);
  console.log('Password field exists:', 'password' in req.body);
  console.log('Password field value:', data.password);
  console.log('Password length:', data.password ? data.password.length : 'UNDEFINED/NULL');
  console.log('All form fields:', Object.keys(req.body));

  // Validate input
  if (!data.email || !data.password) {
    console.log('Login failed: Missing email or password');
    return res.redirect('/admin/login?error=missing_fields');
  }

  if (!security.isValidEmail(data.email)) {
    console.log('Login failed: Invalid email format');
    return res.redirect('/admin/login?error=invalid_email');
  }

  if (data.email !== '' && data.password !== '') {
    try {
      // Log the SQL query for debugging (without showing the full password)
      let debugSql = 'select * from admin where emailid = "' + data.email + '" and password = "[PASSWORD]"';
      console.log('Debug SQL:', debugSql);
      
      // First, let's see what's actually in the admin table
      console.log('🔍 Checking admin table contents...');
      const [allAdmins] = await db.execute('SELECT id, emailid, password FROM admin');
      console.log('All admin records:', allAdmins);

      // Use parameterized query with the connection pool for better security and reliability
      const sql = 'SELECT * FROM admin WHERE emailid = ? AND password = ?';
      console.log('🔍 Executing query with params:', [data.email, data.password]);
      const [rows] = await db.execute(sql, [data.email, data.password]);

      console.log('Query result:', rows);
      
      if (rows && rows.length > 0) {
        let userData = rows[0];
        sess = req.session;
        sess.email = userData.emailid;
        sess.admin = true;
        sess.lastActivity = Date.now(); // Track session activity
        console.log('Login successful for:', userData.emailid);
        res.redirect('/admin/lottery');
      } else {
        console.log('Login failed: Invalid credentials');
        // Add delay to prevent brute force attacks
        setTimeout(() => {
          res.redirect('/admin/login?error=invalid_credentials');
        }, 1000);
      }
    } catch (err) {
      console.error('Database error during login:', err);
      return res.status(500).send('Database error: ' + err.message);
    }
  } else {
    console.log('Login failed: Empty email or password');
    res.redirect('/admin/login');
  }
});

module.exports = router;