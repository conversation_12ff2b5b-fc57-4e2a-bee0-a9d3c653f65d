var express = require('express');
var router = express.Router();
var db = require('../../lib/database');
// get company 
router.get('/test', function(req, res) {
  res.send("Hello");
});
router.get('/',(req,res)=>{
	res.render('registered-users');
})
router.get('/get-users', async function(req,res,next){
    try {
      const [result] = await db.query("SELECT * FROM user;");
      res.json({result});
    } catch (err) {
      console.error(err);
      res.status(500).json({error: err.message});
    }
});

router.get('/get-by-id/:id', async (req,res)=>{
  let id = req.params.id
  try{
    // SECURITY FIX: Use parameterized query to prevent SQL injection
    // Also validate that id is a number
    if (!/^\d+$/.test(id)) {
      return res.status(400).json({error: 'Invalid user ID'});
    }
    const [result] = await db.query('SELECT * FROM user WHERE id = ?', [id]);
    res.json({result});
  }catch(err){
    console.error(err);
    res.status(500).json({error: err.message});
  }
})

router.post('/archive',async (req,res)=>{
  try{
    let id = req.body.id
    console.log(id)
    let sql = "update user set is_active = 0 where id="+req.body.id+";"
    await db.query(sql)
    res.send({status : true})
  }catch(err){
    res.send({status : false, error : err.stack})
  }
})

router.post('/reactivate',async (req,res)=>{
  try{
    let id = req.body.id
    console.log(id)
    let sql = "update user set is_active = 1 where id="+req.body.id+";"
    await db.query(sql)
    res.send({status : true})
  }catch(err){
    res.send({status : false, error : err.stack})
  }
})

router.post('/update-user', async (req, res) => {
  try {
    const formBody = req.body;
    const user_id = formBody.id;
    delete formBody.id;
    
    // Use parameterized query for security
    const selectQuery = `SELECT ${Object.keys(formBody).join(", ")} FROM user WHERE id = ?`;
    const [result] = await db.query(selectQuery, [user_id]);
    
    if (!result || result.length === 0) {
      return res.send({status: false, error: 'User not found'});
    }
    
    const oldData = result[0];
    console.log('Old user data:', oldData);
    
    // Track changes in admin_edit_log
    for (let key of Object.keys(formBody)) {
      console.log(key, oldData[key], formBody[key]);
      if (oldData[key] !== formBody[key] && (oldData[key] !== undefined && formBody[key] !== undefined)) {
        const insertLogQuery = 'INSERT INTO lottery.admin_edit_log (user_id_changed, change_time, field, old_value, new_value) VALUES (?, CURRENT_TIMESTAMP, ?, ?, ?)';
        const logParams = [user_id, key, oldData[key], formBody[key]];
        console.log('Logging change:', logParams);
        await db.query(insertLogQuery, logParams);
      }
    }
    
    // Build update query with parameterized values
    const updateFields = Object.keys(formBody).map(field => `${field} = ?`).join(", ");
    const updateValues = Object.values(formBody);
    const updateQuery = `UPDATE user SET ${updateFields} WHERE id = ?`;
    
    // Add user_id as the last parameter
    updateValues.push(user_id);
    
    await db.query(updateQuery, updateValues);
    res.send({status: true});
  } catch (err) {
    console.error('Error updating user:', err);
    res.send({status: false, error: err.message});
  }
})
  module.exports = router;
