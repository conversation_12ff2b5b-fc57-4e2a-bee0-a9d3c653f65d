var express = require('express');
var router = express.Router();
const sgMail = require('@sendgrid/mail');
const db = require("../../lib/database");

// get company 
router.get('/', function(req, res, next) {
  res.render('news-letter', { title: 'newsLetter' });
});


router.post('/', async function(req, res, next) {
  try {
    // Only send to confirmed subscribers
    let sql1 = 'SELECT * FROM user_subscribe_list WHERE is_confirmed = 1';
    const [result] = await db.pool.query(sql1);
    
    if(result.length > 0){
      sgMail.setApiKey(process.env.SENDGRID_API_KEY);
      
      // Create an array of promises for all email sends
      const emailPromises = result.map(element => {
        const msg = {
          to: element.email,
          from: process.env.SENDID,
          subject: 'Promo',
          html: req.body.senddata
        };
        
        return sgMail.send(msg);
      });
      
      // Wait for all emails to be sent
      await Promise.all(emailPromises);
      
      res.render('news-letter', { 
        title: 'newsLetter',
        success: `Newsletter sent to ${result.length} subscribers successfully!`
      });
    } else {
      res.render('news-letter', { 
        title: 'newsLetter',
        error: 'No subscribers found in the database.'
      });
    }
  } catch (error) {
    console.error('Error sending newsletter:', error);
    res.render('news-letter', { 
      title: 'newsLetter',
      error: `Error sending newsletter: ${error.message}`
    });
  }

});

  module.exports = router;