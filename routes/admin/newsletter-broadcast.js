var express = require('express');
var router = express.Router();
const db = require("../../lib/database");
const path = require('path');

// Get paid customers by lottery and group
router.get('/paid-customers', function(req, res) {
  const lotteryId = req.query.lotteryId;
  const groupId = req.query.groupId;

  const sql = `
    SELECT u.* 
    FROM user u 
    INNER JOIN ticket t ON u.id = t.user_id
    WHERE t.lottery_id = ? 
    AND t.group_id = ? 
    AND t.payment_status = 'paid'
  `;

  db.pool.query(sql, [lotteryId, groupId])
    .then(([results]) => {
      res.json(results);
    })
    .catch(err => {
      console.error('Error fetching paid customers:', err);
      res.status(500).json({ error: 'Failed to fetch paid customers' });
    });
});

// Get the newsletter broadcast page
router.get('/test-email', async function(req, res) {
  try {
    await db.sendEmail(
      null,
      'newsletter',
      null,
      '<EMAIL>',
      [req.query.email || '<EMAIL>'],
      {
        subject: 'Test Email from Lets Play Group Lottery',
        content: 'This is a test email to verify the email sending configuration.'
      },
      false
    );

    res.send('Test email sent successfully! Check your inbox.');
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).send('Error sending test email: ' + error.message);
  }
});

router.get('/', function(req, res, next) {
  res.render('admin/newsletter-broadcast', { 
    title: 'Send Newsletter' 
  });
});

// Send newsletter to all subscribers
router.post('/send', async (req, res) => {
  try {
    let { subject, message } = req.body;
    
    // Validate and format input
    if (!subject || !message) {
      return res.render('admin/newsletter-broadcast', { 
        title: 'Send Newsletter',
        error: 'Please provide both a subject line and message content',
        subject: subject || '',
        message: message || ''
      });
    }

    // Clean up subject line
    subject = subject.trim();
    if (subject.length < 3) {
      return res.render('admin/newsletter-broadcast', {
        title: 'Send Newsletter',
        error: 'Subject line must be at least 3 characters long',
        subject, message
      });
    }

    // Format message content
    message = `
      <div style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333;">
        ${message}
        <br><br>
        <p style="color: #666; font-style: italic;">Best regards,<br>Let's Play Group Lottery Team</p>
      </div>
    `;
    
    // First fetch confirmed subscribers to ensure we have them before proceeding
    const sql = 'SELECT * FROM user_subscribe_list WHERE is_confirmed = 1';
    try {
      const [subscribers] = await db.pool.query(sql);
      
      if (subscribers.length === 0) {
        return res.render('admin/newsletter-broadcast', { 
          title: 'Send Newsletter',
          error: 'No subscribers found in the database. Add subscribers first.',
          subject: subject,
          message: message
        });
      }
      
      try {
        // Send email using IONOS SMTP
        await db.sendEmail(
          null, // No image path needed
          'newsletter', // Template directory name
          null, // No file attachment
          '<EMAIL>', // From address
          subscribers.map(sub => sub.email), // To addresses
          { // Template data
            subject: subject,
            content: message
          },
          true // This is a group email
        );
        
        console.log('Successfully sent newsletter via IONOS SMTP');
        
        // Return success response
        return res.render('admin/newsletter-broadcast', { 
          title: 'Send Newsletter',
          success: `Newsletter sent successfully to ${subscribers.length} subscribers`,
          subject: '',
          message: ''
        });
      } catch (emailError) {
        console.error('Error sending newsletter:', emailError);
        
        // Return error response
        return res.render('admin/newsletter-broadcast', { 
          title: 'Send Newsletter',
          error: `Failed to send newsletter: ${emailError.message}`,
          subject: subject,
          message: message
        });
      }
    } catch (err) {
      console.error('Error fetching subscribers:', err);
      return res.render('admin/newsletter-broadcast', { 
        title: 'Send Newsletter',
        error: 'Failed to fetch subscribers: ' + err.message,
        subject: subject,
        message: message
      });
    }
  } catch (unexpectedError) {
    console.error('Unexpected error in newsletter broadcast:', unexpectedError);
    return res.render('admin/newsletter-broadcast', { 
      title: 'Send Newsletter',
      error: `An unexpected error occurred: ${unexpectedError.message}. Please try again.`,
      subject: req.body.subject || '',
      message: req.body.message || ''
    });
  }
});

module.exports = router;
