var express = require('express');
var router = express.Router();
var db = require("../../lib/database");
var multer = require('multer');
var moment = require('moment');
const security = require("../../lib/security"); // Security utilities

// Utility function to sanitize filenames
// Converts lottery names like "lotto 6/49" to "lotto6_49" for safe file naming
function sanitizeFilename(filename) {
  return filename
    .replace(/\s/g, "")           // Remove spaces
    .replace(/\//g, "_")          // Replace forward slashes with underscores
    .replace(/[^a-zA-Z0-9_-]/g, "_"); // Replace any other special characters with underscores
}

var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/')
  },
  filename: function (req, file, cb) {
    console.log(req.body);
    // Sanitize filename by removing/replacing invalid characters
    var sanitizedLotteryName = sanitizeFilename(req.body.lotteryName);
    var imageName = sanitizedLotteryName + '_' + req.body.groupName + '.png';
    cb(null, imageName)
  }
});

// SECURITY: File validation for email uploads
const fileFilter = (req, file, cb) => {
  // Allow only images for email attachments
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

  // Check MIME type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return cb(new Error('Invalid file type. Only images (JPEG, PNG, GIF, WebP) are allowed.'), false);
  }

  // Check file extension
  const fileExt = require('path').extname(file.originalname).toLowerCase();
  if (!allowedExtensions.includes(fileExt)) {
    return cb(new Error('Invalid file extension. Only .jpg, .jpeg, .png, .gif, .webp are allowed.'), false);
  }

  // Validate filename (no dangerous characters)
  if (/[<>:"/\\|?*\x00-\x1f]/.test(file.originalname)) {
    return cb(new Error('Invalid filename. Contains dangerous characters.'), false);
  }

  cb(null, true);
};

var upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1, // Only 1 file at a time
    fields: 20 // Limit form fields
  }
});

router.get('/', async function(req, res, next) {
  try {
    // Using hardcoded lottery list instead of database query
    const renderData = {};

    // Check for success/error messages
    if (req.query.success) {
      renderData.success = req.query.success;
    }
    if (req.query.error) {
      renderData.error = req.query.error;
    }

    res.render('email', renderData);
  } catch (err) {
    console.error('Error rendering email page:', err);
    res.status(500).send('Error loading email page: ' + err.message);
  }
});

router.post('/send-email', security.requireAdmin, (req, res, next) => {
  // Handle multer errors
  upload.single('imageFile')(req, res, (err) => {
    if (err) {
      console.error('File upload error:', err.message);
      return res.redirect('/admin/email?error=' + encodeURIComponent(err.message));
    }
    next();
  });
}, async function(req, res, next) {
  try {
    const fs = require('fs');
    const path = require('path');

    const uploadsPath = 'uploads/';
    let imageName = null;

    // Check if file was uploaded
    if (req.file) {
      imageName = req.file.filename;
      console.log('File uploaded:', imageName);
    } else {
      // If no file uploaded, construct expected filename and check if it exists
      // Use the same sanitization logic as multer
      const sanitizedLotteryName = sanitizeFilename(req.body.lotteryName);
      const expectedImageName = sanitizedLotteryName + '_' + req.body.groupName + '.png';
      const fullPath = path.join(uploadsPath, expectedImageName);

      if (fs.existsSync(fullPath)) {
        imageName = expectedImageName;
        console.log('Using existing file:', imageName);
      } else {
        console.log('No file uploaded and no existing file found. Proceeding without attachment.');
        imageName = null;
      }
    }

    // First, get the lottery ID from the lottery name
    const lotteryLookupSql = `SELECT id FROM lottery_master WHERE lottery_name = ? AND is_deleted = 0 ORDER BY is_active DESC, id DESC LIMIT 1`;
    const [lotteryResults] = await db.pool.query(lotteryLookupSql, [req.body.lotteryID]);

    if (lotteryResults.length === 0) {
      return res.redirect("/admin/email?error=" + encodeURIComponent(`Lottery "${req.body.lotteryID}" not found in database`));
    }

    const actualLotteryId = lotteryResults[0].id;
    console.log(`Found lottery ID ${actualLotteryId} for lottery name "${req.body.lotteryID}"`);

    // Use parameterized query for security
    const sql = `
      SELECT user.*,
      ticket.*,
      lottery_master.*,
      ticket.quickpick as ticket_quickpick,
      ticket.custom_number as ticket_custom_number
      FROM user
      INNER JOIN ticket ON ticket.user_id = user.id
      INNER JOIN lottery_master ON lottery_master.id = ticket.lottery_id
      WHERE ticket.lottery_id = ? AND ticket.group_no = ?
    `;

    const [results] = await db.pool.query(sql, [actualLotteryId, req.body.groupId]);

    if (results.length === 0) {
      return res.status(404).send('No users found for this lottery and group');
    }

    let emails = [];
    let userList = [];

    results.forEach((v) => {
      console.log(v);
      emails.push(v.email_id);
      userList.push({
        "name": v.first_name.charAt(0) + " " + v.last_name,
        "city": v.city,
        "profession": v.profession,
        "luckyNumbers": v.ticket_custom_number,
        "QuickPick": v.ticket_quickpick
      });
    });

    const data = {
      "lotteryName": req.body.lotteryName,
      "groupLabel": req.body.groupName,
      "startDate": moment(results[0].start_date).format('L'),
      "endDate": moment(results[0].end_date).format('L'),
      "userlist": userList,
      "message": req.body.message,
      "subject": `${req.body.lotteryName} - Group ${req.body.groupName} Information`
    };

    console.log('Sending email with data:', data);
    console.log('Image attachment:', imageName ? `${uploadsPath}${imageName}` : 'No attachment');

    // Use the existing nodemailer setup instead of email templates
    const { sendMailByNodemailer } = require('../../lib/mailer-send/nodeMailSender');

    // Create HTML content for the email
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Group Members Information</h2>
        <p>Dear Members,</p>
        <p>Please check the lucky numbers if you have entered on our web page is the same and personnel information.</p>
        <p>Any issue let us know before 3 weeks the lottery starts so we can correct the issues.</p>

        <div style="background-color: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px;">
          <h3>Lottery Details:</h3>
          <p><strong>Lottery Name:</strong> ${data.lotteryName}</p>
          <p><strong>Group:</strong> ${data.groupLabel}</p>
          <p><strong>Start Date:</strong> ${data.startDate}</p>
          <p><strong>End Date:</strong> ${data.endDate}</p>
        </div>

        <h3>Group ${data.groupLabel} Members:</h3>
        <ul style="list-style-type: none; padding: 0;">
          ${data.userlist.map(user => `
            <li style="background-color: #f9f9f9; margin: 5px 0; padding: 10px; border-radius: 3px;">
              <strong>${user.name}</strong> - ${user.city} - ${user.profession} -
              Lucky Numbers: ${user.luckyNumbers !== 0 ? user.luckyNumbers : 'Quick Pick'} -
              Quick Pick: ${user.QuickPick ? 'Yes' : 'No'}
            </li>
          `).join('')}
        </ul>

        ${data.message ? `<div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 5px;">
          <h4>Message:</h4>
          <p>${data.message}</p>
        </div>` : ''}

        <p style="margin-top: 30px; color: #666;">
          Best regards,<br>
          Let's Play Group Lottery Team
        </p>
      </div>
    `;

    // Send email to all recipients using direct nodemailer for better control
    const nodemailer = require('nodemailer');

    // Create transporter (same as existing setup)
    const transporter = nodemailer.createTransport({
      host: "smtp.ionos.com",
      port: 587,
      secure: false,
      auth: {
        user: process.env.NODE_MAILER_USER,
        pass: process.env.NODE_MAILER_PASSWORD
      },
      tls: {
        rejectUnauthorized: false,
      },
    });

    // Prepare attachments if file exists
    let attachments = [];
    if (imageName && uploadsPath) {
      const fs = require('fs');
      const path = require('path');
      const fullFilePath = path.join(uploadsPath, imageName);
      if (fs.existsSync(fullFilePath)) {
        attachments.push({
          filename: imageName,
          path: fullFilePath
        });
        console.log('Adding attachment:', fullFilePath);
      }
    }

    // Send email to all recipients and track results
    let successCount = 0;
    let failedEmails = [];

    for (const email of emails) {
      try {
        const mailOptions = {
          from: process.env.NODE_MAILER_USER,
          to: email,
          subject: data.subject,
          html: htmlContent,
          attachments: attachments
        };

        await transporter.sendMail(mailOptions);
        console.log(`Email sent successfully to: ${email}`);
        successCount++;
      } catch (emailError) {
        console.error(`Failed to send email to ${email}:`, emailError);
        failedEmails.push(email);
      }
    }

    // Provide detailed feedback based on results
    if (successCount === emails.length) {
      // All emails sent successfully
      res.redirect("/admin/email?success=" + encodeURIComponent(`All ${successCount} emails sent successfully!`));
    } else if (successCount > 0) {
      // Some emails sent successfully, some failed
      res.redirect("/admin/email?success=" + encodeURIComponent(`${successCount} emails sent successfully. ${failedEmails.length} failed: ${failedEmails.join(', ')}`));
    } else {
      // All emails failed
      res.redirect("/admin/email?error=" + encodeURIComponent(`Failed to send emails to all recipients: ${failedEmails.join(', ')}`));
    }
  } catch (err) {
    console.error('Error sending email:', err);
    res.redirect("/admin/email?error=" + encodeURIComponent(err.message));
  }
});
module.exports = router;