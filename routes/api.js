var express = require('express');
var router = express.Router();
var db = require("../lib/database");

var dateFormat = require('dateformat');


router.get('/get-lottery', async (req, res, next) => {
    try {
        const [results] = await db.pool.query("SELECT * FROM lottery_master WHERE is_deleted = 0");
        res.json(results);
    } catch (err) {
        console.error('Error fetching lotteries:', err);
        res.status(500).json({ error: 'Database error' });
    }
});


router.get('/get-lottery-detail', async (req, res, next) => {
    try {
        const { lotterid, lotteryname } = req.query;

        let results;
        if (lotterid) {
            // Use parameterized query to prevent SQL injection - search by ID
            [results] = await db.pool.query("SELECT * FROM lottery_master WHERE id = ?", [lotterid]);
        } else if (lotteryname) {
            // Search by lottery name - prioritize active lotteries, then get most recent
            [results] = await db.pool.query(
                "SELECT * FROM lottery_master WHERE lottery_name = ? AND is_deleted = 0 ORDER BY is_active DESC, id DESC LIMIT 1",
                [lotteryname]
            );
        } else {
            return res.status(400).json({ error: 'Either lotterid or lotteryname parameter is required' });
        }

        res.json(results);
    } catch (err) {
        console.error('Error fetching lottery details:', err);
        res.status(500).json({ error: 'Database error' });
    }
});

router.post('/send-email', async (req, res, next) => {
    try {
        let imagepath = '/root/letsplaygrouplottery/public/uploads/';
        let {lotteryId, groupId} = req.body;

        if (!lotteryId || !groupId) {
            return res.status(400).send({ msg: "lottery or group is missing" });
        }

        // Use parameterized query to prevent SQL injection
        const sql = `SELECT user.email_id, user.first_name, user.last_name,
            lottery_master.lottery_name,
            lottery_master.start_date,
            lottery_master.end_date
            FROM user
            INNER JOIN ticket ON ticket.user_id = user.id
            INNER JOIN lottery_master ON lottery_master.id = ticket.lottery_id
            WHERE ticket.lottery_id = ? AND ticket.group_no = ?`;
        
        const [results] = await db.pool.query(sql, [lotteryId, groupId]);
        
        let sendEmail = [];
        let userList = [];
        let lotteryName = '';
        let startDate = '';
        let endDate = '';

        const group = ["A", "B", "C", "D"]; // Only 4 groups allowed as per business requirements
        
        results.forEach(x => {
            lotteryName = x.lottery_name;
            startDate = x.start_date;
            endDate = x.end_date;
            sendEmail.push(x.email_id);
            userList.push(x.first_name + ' ' + x.last_name);
        });
        
        let data = {
            lotteryName: lotteryName,
            userNameList: userList,
            startDate: dateFormat(startDate, 'dd/mm/yyyy'),
            endDate: dateFormat(endDate, 'dd/mm/yyyy'),
            groupLabel: group[groupId - 1]
        };
        
        console.log({data});
        
        // Use the email sending function from database.js instead of dataHandler
        await db.sendEmail(
            imagepath, 
            'lotteryticket', 
            'balloon.png', 
            '<EMAIL>', 
            '<EMAIL>', 
            data
        );
        
        return res.status(200).send({ success: true, msg: "Email sent successfully" });
    } catch (err) {
        console.error('Error sending email:', err);
        return res.status(500).send({ success: false, msg: "Error sending email", error: err.message });
    }
});

router.get('/get-lottery-group-count/:lotteryId', async (request, response) => {
    try {
        const { lotteryId } = request.params;

        if (!lotteryId) {
            return response.status(400).send({ message: "Lottery ID is required" });
        }

        // First query to get group information if available
        const sql = `
            SELECT 
                MAX(group_no) AS currentGroup,
                COUNT(person_in_group) AS personCount,
                person_per_group as personInGroup,
                no_of_groups as groupPerLottery
            FROM
                ticket
            INNER JOIN
                lottery_master ON ticket.lottery_id = lottery_master.id
            WHERE
                id = ? 
            GROUP BY group_no 
            ORDER BY currentGroup DESC 
            LIMIT 1
        `;

        const [results] = await db.pool.query(sql, [lotteryId]);

        if (results.length === 0) {
            // No tickets found, get lottery configuration
            const configSql = `SELECT no_of_groups, person_per_group FROM lottery_master WHERE id = ?`;
            const [configResults] = await db.pool.query(configSql, [lotteryId]);

            if (configResults.length === 0) {
                return response.status(200).send({ message: "lottery not present" });
            }

            return response.status(200).send({
                "currentGroup": 1,
                "personCount": 1,
                "personInGroup": configResults[0]['person_per_group'],
                "groupPerLottery": configResults[0]['no_of_groups']
            });
        } else {
            // Tickets found, check if current group is full
            if (results[0]['personCount'] == results[0]['personInGroup']) {
                return response.status(200).send({
                    "currentGroup": results[0]['currentGroup'] + 1,
                    "personCount": 0,
                    "personInGroup": results[0]['personInGroup'],
                    "groupPerLottery": results[0]['groupPerLottery']
                });
            }

            return response.status(200).send({
                "currentGroup": results[0]['currentGroup'],
                "personCount": results[0]['personCount'],
                "personInGroup": results[0]['personInGroup'],
                "groupPerLottery": results[0]['groupPerLottery']
            });
        }
    } catch (err) {
        console.error('Error getting lottery group count:', err);
        return response.status(500).send({ message: "Database error", error: err.message });
    }
});


module.exports = router;
