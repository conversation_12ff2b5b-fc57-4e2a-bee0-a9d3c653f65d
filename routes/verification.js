require('dotenv').config();

var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require("../lib/database");
const {sendWelcomeEmail} = require("../lib/mailer-send/mailerSendWelcome");



router.get('/', (req, res, next) => {
    try {
        console.log("get verification view");
        let jwtSecretKey = process.env.JWT_SECRET_KEY;
        var decoded = jwt.verify(req.query.token, jwtSecretKey);
        res.render('loginCode');
      } catch(err) {
        console.log(err);
      }
});


router.post('/', async (req, res, next) => {
    try {
        const token = req.body.token;
        console.log(req.body);
        console.log(req.query);
        let jwtSecretKey = process.env.JWT_SECRET_KEY;
        var decoded = jwt.verify(token, jwtSecretKey);
        const userEmail = decoded.data.userEmail;
        const verificationCode = req.body.verificationCode;
      
        // Use parameterized query to prevent SQL injection
        const sql = 'SELECT * FROM user WHERE code = ? AND email_id = ?';
        const [results] = await db.pool.query(sql, [verificationCode, userEmail]);
        
        console.log('res = ', results);
        
        if (results && results.length > 0) {
            const user = results[0];
            let id = user.id;
            sess = req.session;
            sess.email = user.email_id;
            sess.userid = id;
            sess.admin = false;
            sess.user = true;
            
            if (req.body.type === 'register') {
                await sendWelcomeEmail(user.email_id, `${user.first_name} ${user.last_name}`);
            }
            
            return res.json({status: 200, msg: "Verification successfully. Click OK to move to play page."});
        } else {
            return res.json({status: 500, msg: "code incorrect"});
        }
    } catch (err) {
        console.error('Verification error:', err);
        return res.json({status: 500, msg: "something wrong"});
    }
});


module.exports = router;