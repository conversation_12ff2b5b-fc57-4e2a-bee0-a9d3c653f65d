var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require("../lib/database");

/* GET home page. */
router.get('/', function (req, res, next) {
	res.render('updatePassword');
});

router.post('/', async function(req, res, next) {
    try {
        let jwtSecretKey = process.env.JWT_SECRET_KEY;
        var decoded = jwt.verify(req.body.token, jwtSecretKey);
        const userEmail = decoded.data.userEmail;
        
        // Use parameterized query to prevent SQL injection
        const sql = 'SELECT password FROM user WHERE email_id = ?';
        const [results] = await db.pool.query(sql, [userEmail]);
        
        if (results && results.length > 0) {
            if (req.body.confirmOldPassword == req.body.oldPassword) {
                const salt = bcrypt.genSaltSync();
                const hashpass = bcrypt.hashSync(req.body.confirmOldPassword, salt);
                
                // Use parameterized query for the update
                const updateSql = 'UPDATE user SET password = ? WHERE email_id = ?';
                const [updateResult] = await db.pool.query(updateSql, [hashpass, userEmail]);
                
                if (updateResult && updateResult.affectedRows > 0) {
                    return res.json({status: 200, msg: "Password successfully updated. Click OK to move to play page."});
                } else {
                    return res.json({status: 500, msg: "Failed to update password. Please try again."});
                }
            } else {
                return res.json({status: 500, msg: "Both passwords do not match"});
            }
        } else {
            return res.json({status: 500, msg: "User not found."});
        }
    } catch (error) {
        console.error('Error updating password:', error);
        return res.json({status: 500, err: error, msg: "Failed to execute. Please contact us and we'll fix it."});
    }
});
module.exports = router;