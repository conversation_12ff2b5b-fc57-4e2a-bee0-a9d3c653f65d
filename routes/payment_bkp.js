var express = require('express');
var router = express.Router();
const https = require('https');
const querystring = require('querystring');

const database = require("../lib/database");
const { v4: uuidv4 } = require('uuid');

router.get('/', (req, res, next) => {
  console.log('get /payment');
  console.log(req.body);
});
/* GET home page. */
// router.get('/', function(req, res, next) {
	
//     console.log("post payment email gen view");
// 	console.dir(req.body,{depth:null});
// 	var emailid;
	
// 	if(req.session  && req.session.user){
// 		emailid =  req.session.email;
// 	}
	
// 	            let data =  {
//                 lotteryName: "loto649",
//                 userlist: emailid,
//                 startDate : '',
//                 endDate : '',
//                 groupLabel : 2
//                 };
//                 console.log({data});
                
//                 await db.sendEmail(null,'lotteryticket','balloon.png','<EMAIL>',emailid,data)      
               
//   res.send('email sent successfully');
// });


router.post('/', function(req, res, next) {
	// var merchantID = "001731"; //Converge 6 or 7-Digit Account ID *Not the 10-Digit Elavon Merchant ID*
	// var merchantUserID = "webpage"; //Converge User ID *MUST FLAG AS HOSTED API USER IN CONVERGE UI*
	// var merchantPIN = "H0JLBAH5SUUW6X1MNI8DAPTQV7QKE1GTW9RGZG7JQYP15LP7SKVHQP8C8A6PCYZP"; //Converge PIN (64 CHAR A/N)
	//const merchantID = "003152"; //Converge 6 or 7-Digit Account ID *Not the 10-Digit Elavon Merchant ID*
	//const merchantUserID = "webpage"; //Converge User ID *MUST FLAG AS HOSTED API USER IN CONVERGE UI*
	//const merchantPIN = "OTCWT4ORKCRCNBPC6SIRDJV5BN7RG04OE7JGCLZG28ECDG74B9IA0I3ZGJNA4SFZ"; //Converge PIN (64 CHAR A/N)

	
  	const merchantID = "2297415"; //Converge 6 or 7-Digit Account ID *Not the 10-Digit Elavon Merchant ID*
	const merchantUserID = "apiwser222376"; //Converge User ID *MUST FLAG AS HOSTED API USER IN CONVERGE UI*
	const merchantPIN = "8IO8AN6E1DFOWZBC77Z1IX2NCFKFU9FM86T37SGI5JUEOJE44TR3CCNKX9N4WM19"; //Converge PIN (64 CHAR A/N)

	//var url = "https://api.demo.convergepay.com/hosted-payments/transaction_token"; // URL to Converge demo session token server
	var url = "https://api.convergepay.com/hosted-payments/transaction_token"; // URL to Converge production session token server

  //const data = dataHandler.registerUser(con, req.body);

    console.log("post payment token gen view");
    
    console.log("req type = " + req.get('content-type'));

    console.log("req userid = " + req.session.userid);

    console.log("req emailid = " + req.session.email);
    
    console.log("req lottery type = " + req.body.ssl_first_name);
    
    console.log("req lottery id = " + req.body.ssl_last_name);	

    console.log("req lottery price = " + req.body.ssl_amount);
	
	console.log("req txid = " + req.body.txid);
    
    console.dir(req.body,{depth:null});
  
    var ip = req.header('x-forwarded-for') || req.connection.remoteAddress;
  
    console.log("users ip = " + ip);
	
	/*
var postData = querystring.stringify({
    'ssl_merchant_id : '  + merchantID,
	'ssl_user_id : ' + merchantUserID,
	'ssl_pin : ' + merchantPIN,
	'ssl_transaction_type : ccsale',
	'ssl_first_name : ' + req.body.ssl_first_name,
	'ssl_last_name :'  + req.body.ssl_last_name,
	'ssl_get_token : Y',
	'ssl_add_token : Y',
	'ssl_amount : ' + req.body.ssl_amount
}); */

var postData = 	"ssl_merchant_id=" + merchantID + 
	"&ssl_user_id=" + merchantUserID +
	"&ssl_pin=" + merchantPIN + 
	"&ssl_transaction_type=ccsale" + 
	"&ssl_first_name=" + req.body.ssl_first_name + 
	"&ssl_last_name=" + req.body.ssl_last_name + 
	"&ssl_get_token=Y" + 
	"&ssl_add_token=Y" + 
	"&ssl_amount=" + req.body.ssl_amount;

var options = {
  hostname: 'api.convergepay.com',
  port: 443,
  path: '/hosted-payments/transaction_token',
  method: 'POST',
  rejectUnauthorized: false,
  requestCert: false,
  agent: false,
  headers: {
	  'x-frame-options': 'SAMEORIGIN',
       'Content-Type': 'application/x-www-form-urlencoded',
       'Content-Length': postData.length
     }
};

var apireq = https.request(options, (apires) => {
  console.log('statusCode:', apires.statusCode);
  console.log('headers:', apires.headers);

  apires.on('data', (d) => {
    // process.stdout.write(d);
	console.log("******** converge token response = " + d);
	return res.json({status: 200, data: d.toString()});
  });
});

apireq.on('error', (e) => {
  console.error(e);
    // res.send(e);
	return res.json({status: 400, data: e});

});
console.log('postData = ', postData);
apireq.write(postData);
apireq.end();

/*
        //lottery payment details
        var sql = 'INSERT INTO `lottery` (`ticket_id`, `user_id`, `group_id`, `lottery_name`, `encore`,' 
        + '`quick_pick`, `custom_number`, `date_join`, `price`, `lottery_end_date`, `lottery_payment`, `payment_status`,'
        + ' `date`, `winning_status`, `winning_price`, `entry_added_id`) ' + 
        ' VALUES (("' + req.body.ticketId + '","' + req.body.userId+ '","' + '","' + req.body.groupId+ '","' 
        + '","' + req.body.userId+ '","' + + '","' + req.body.userId+ '","' + + '","' + req.body.userId+ '","' 
        + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' 
        + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' 
        + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' + '","' + req.body.userId+ '","' ;      
       
        /* Converted to use database.js with async/await
        await db.query(sql, [param1, param2, ...]);
        */
          if (err) return console.error(err);
          console.log("lottry payment inserted");

          });*/

  // res.sendfile('public' + '/payment_checkout.html');
  // res.render('payment', { title: 'Review & Payment' });
  // return res.json({status: 400, data: "Error calling url"});
});

router.post('/success', async function(req, res, next){
  // res.send(req.body);
  let order_details = [];
  // let temp_index = 0;
  let max_index = req.body.lotteryId.length;
  console.log('max_index =', max_index);

  for(let temp_index=0; temp_index<max_index;temp_index++)  {
    console.log('loop ', temp_index);
    let t = await setTicket1(req.body, temp_index);
    order_details.push(t);
  } 
  const sqlP = `INSERT INTO lottery.payment_ticket_details (payment_amount, date_received, payment_method, payment_email_id, tx_id,payment_status) VALUES (?, ?, ?, ?, ?, ?)`;
  const params = [req.body.amount, formatDate(new Date()), req.body.card_type, req.body.user_email, req.body.merchanttxnid, 'Approved'];
  await database.query(sqlP, params);
  res.render('success', { title: 'Ticket Booked', data: {"body": req.body, "order_details": order_details} });
});

router.post('/error', async function(req, res) {
  const sqlE = `INSERT INTO lottery.payment_ticket_details (payment_amount, date_received, payment_method, payment_email_id, tx_id, payment_message, payment_status) VALUES (?, ?, ?, ?, ?, ?, ?)`;
  const params = [req.body.amt, formatDate(new Date()), `${req.body.cardType} - ${req.body.cardNo}`, req.body.uEmail, req.body.txn_id, req.body.msg, req.body.stat];
  await database.query(sqlE, params);
});

// router.post('/sendmail',(req, res) => {
//   //send mail from here
//   console.log('sendmail = ',req.body);
// });

 async function setTicket1(data, temp_index) {
  let group_no=0, person_in_group=0;
  let quickpickflg = (data.chooseNo[temp_index].includes(","))?0:1;
  
  //  if(temp_index<max_index) {
  const sqlQ = `SELECT MAX(group_no) AS currentGroup, COUNT(person_in_group) AS personCount, person_per_group as personInGroup, no_of_groups as groupPerLottery FROM lottery.ticket INNER JOIN lottery_master ON ticket.lottery_id = lottery_master.id WHERE id = ? group by group_no ORDER BY currentGroup DESC LIMIT 1`;
    const [soln] = await database.query(sqlQ, [data.lotteryId[temp_index]]);
    console.log('qeury result = ',soln);
    if(soln && soln != null && soln.length > 0){
      if(soln[0].personCount < soln[0].personInGroup){
        group_no = soln[0].currentGroup;
        person_in_group = soln[0].personCount + 1;
      }else{
        group_no = soln[0].currentGroup + 1;
        person_in_group = 1;			
      person_in_group = 1;			
        person_in_group = 1;			
      }
    }
    let myUuid = uuidv4();
    const insertSql = 'INSERT INTO `ticket` (`user_id`, `lottery_id`, `group_no`, `quickpick`, `playerName`, `person_in_group`, `created_by`, `created_date`, `misc`, `custom_number`, `payment_tx_id`, `uuid`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
    const insertParams = [
      data.user_id,
      data.lotteryId[temp_index],
      group_no,
      quickpickflg,
      data.playerName[temp_index],
      person_in_group,
      data.user_email,
      new Date(),
      'confirmed',
      data.chooseNo[temp_index],
      data.merchanttxnid,
      myUuid
    ];
    console.log("Insert params = ", insertParams);
    const [tp] = await database.query(insertSql, insertParams);
    return {
      "lottery_id": data.lotteryId[temp_index],
      "lottery_name": data.lotteryName[temp_index],
      "group_no": group_no,
      "quickpick": quickpickflg,
      "custom_number": data.chooseNo[temp_index],
      "playerName": data.playerName[temp_index],
      "person_in_group": person_in_group,
      "created_date" : new Date(),
      "ticket_id": tp.insertId,
      "uuid": myUuid
    };
    // console.log("tp = ", tp);
    
    // inserted item's id 

    // console.log("inserted id = ", tp.insertId);

 }

 function formatDate(date) {
  var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join('-');
}


module.exports = router;