var express = require('express');
var router = express.Router();
var db = require("../lib/database");
const { v4: uuidv4 } = require('uuid');

// Test route to check if email-transfer is working
router.get('/test', function(req, res) {
  res.json({ message: 'Email transfer route is working!' });
});

// Confirm order for email transfer
router.post('/confirm', async function(req, res, next) {
  console.log('Email transfer confirm route called');
  console.log('Request body:', req.body);

  // Validate required fields
  if (!req.body.lotteryId || !Array.isArray(req.body.lotteryId) || req.body.lotteryId.length === 0) {
    console.error('Invalid lottery data:', req.body.lotteryId);
    return res.status(400).json({
      success: false,
      message: 'Invalid lottery data'
    });
  }

  try {
    let order_details = [];
    let max_index = req.body.lotteryId.length;

    // Create tickets for each lottery
    for(let temp_index = 0; temp_index < max_index; temp_index++) {
      console.log(`Processing ticket ${temp_index + 1} of ${max_index}`);
      let t = await setTicket1(req.body, temp_index);
      order_details.push(t);
    }
    
    // Get ticket IDs for this payment (so admin can see the tickets)
    const ticketIds = order_details.map(ticket => ticket.ticket_id).join(',');

    // Create payment record with pending status (including lpg_payment_id for email transfers)
    const sqlP = `INSERT INTO payment_ticket_details
      (lpg_payment_id, payment_amount, date_received, payment_method, payment_email_id, tx_id, payment_message, payment_status, date_added, ticket_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const params = [
      req.body.payment_id, // Store the LPG payment ID
      req.body.price[0],
      formatDate(new Date()),
      'Email Transfer',
      req.body.user_email,
      '152215', // Use same numeric format as payment.js
      'Pending Email Transfer - Payment ID: ' + req.body.payment_id,
      'PENDING',
      formatDate(new Date()),
      ticketIds // Store ticket IDs so admin can see them
    ];

    console.log('Inserting payment record with params:', params);
    const [paymentResult] = await db.pool.query(sqlP, params);
    console.log('Payment record inserted successfully:', paymentResult.insertId);

    res.json({
      success: true,
      payment_id: req.body.payment_id,
      order_details: order_details,
      payment_record_id: paymentResult.insertId
    });
  } catch (err) {
    console.error('Error processing email transfer order:', err);
    res.status(500).json({ 
      success: false,
      message: 'Error processing order', 
      error: err.message 
    });
  }
});

// Helper function to create tickets (same as payment.js)
async function setTicket1(data, temp_index) {
  try {
    let group_no = 0, person_in_group = 0;

    // Improved logic to determine quickpick flag
    let quickpickflg = 1; // Default to Quick Pick
    const chooseNoValue = data.chooseNo[temp_index];

    if(chooseNoValue && typeof chooseNoValue === 'string') {
      if(chooseNoValue.includes(",")) {
        // Contains comma, likely custom numbers like "1,2,3,4,5,6"
        quickpickflg = 0;
      } else if(chooseNoValue === "Quick Pick") {
        // Explicitly Quick Pick
        quickpickflg = 1;
      } else if(chooseNoValue === "N/A") {
        // No number selection for this lottery type
        quickpickflg = 1;
      }
    }

    // Use parameterized query to prevent SQL injection (fixed to match working admin routes)
    const sqlQ = `SELECT MAX(group_no) AS currentGroup, COUNT(person_in_group) AS personCount,
      person_per_group as personInGroup, no_of_groups as groupPerLottery
      FROM ticket INNER JOIN lottery_master ON ticket.lottery_id = lottery_master.id
      WHERE lottery_master.id = ? group by group_no ORDER BY currentGroup DESC LIMIT 1`;

    const [results] = await db.pool.query(sqlQ, [data.lotteryId[temp_index]]);

    if(results && results.length > 0) {
      if(results[0].personCount < results[0].personInGroup) {
        group_no = results[0].currentGroup;
        person_in_group = results[0].personCount + 1;
      } else {
        // Only allow groups 1-4 (A, B, C, D)
        const nextGroup = results[0].currentGroup + 1;
        if(nextGroup <= 4) {
          group_no = nextGroup;
          person_in_group = 1;
        } else {
          // If all 4 groups are full, start a new cycle from group 1
          group_no = 1;
          person_in_group = 1;
        }
      }
    } else {
      // Default values if no results
      group_no = 1;
      person_in_group = 1;
    }

    const myUuid = uuidv4();

    // Use parameterized query for insert (matching payment.js format)
    const insertSql = `INSERT INTO ticket
      (user_id, lottery_id, group_no, quickpick, playerName, person_in_group, created_by, created_date, misc, custom_number, payment_tx_id, uuid)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const insertParams = [
      data.user_id,
      data.lotteryId[temp_index],
      group_no,
      quickpickflg,
      data.playerName[temp_index],
      person_in_group,
      data.user_email,
      formatDate(new Date()),
      'pending payment',
      data.chooseNo[temp_index],
      '1525252388', // Use numeric value for payment_tx_id (integer column)
      myUuid
    ];

    const [result] = await db.pool.query(insertSql, insertParams);

    return {
      "lottery_id": data.lotteryId[temp_index],
      "lottery_name": data.lotteryName[temp_index],
      "group_no": group_no,
      "quickpick": quickpickflg,
      "custom_number": data.chooseNo[temp_index],
      "playerName": data.playerName[temp_index],
      "person_in_group": person_in_group,
      "created_date": new Date(),
      "ticket_id": result.insertId,
      "uuid": myUuid
    };
  } catch (err) {
    console.error('Error in setTicket1:', err);
    throw err;
  }
}

function formatDate(date) {
  var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join('-');
}

module.exports = router;
