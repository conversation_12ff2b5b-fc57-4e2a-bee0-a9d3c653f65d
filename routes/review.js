var express = require('express');
var router = express.Router();

var db = require("../lib/database");

/* GET home page. */
router.get('/', function(req, res, next) {
  res.render('payment', { title: 'Review', isLogged: true });
});


router.post('/', async function(req, res, next) {
  try {
    var all_tickets = [];

    // First, get lottery configurations from database
    const lotteryConfigs = {};
    for(let lottery of req.body.lotteryNames) {
      const lotteryId = lottery.split('_')[1];
      const [lotteryResults] = await db.pool.query(
        'SELECT quickpick, custom_number, lottery_name FROM lottery_master WHERE id = ?',
        [lotteryId]
      );
      if(lotteryResults.length > 0) {
        lotteryConfigs[lotteryId] = lotteryResults[0];
      }
    }

    req.body.lotteryNames.forEach((lottery, indx) => {
      var lotteryName = lottery.split('_')[0];
      var lotteryId = lottery.split('_')[1];
      var no_of_tickets = parseInt(req.body['tickets_no'+lotteryId]);
      let ticketPrice = parseFloat(lottery.split('_')[2]);
      const lotteryConfig = lotteryConfigs[lotteryId];

      for(let i=1; i<=no_of_tickets; i++) {
        let playerName = req.body['player_name_'+lotteryId+'_tn_'+i];
        let isQuickPick = false, chooseNo = "N/A";

        // Check if this lottery supports number selection
        if(lotteryConfig && (lotteryConfig.quickpick == 1 || lotteryConfig.custom_number == 1)) {
          const radioValue = req.body['quickorchoose_'+ lotteryId+ '_tn_' + i];
          isQuickPick = (radioValue == 'quickpick');

          if(!isQuickPick && radioValue == 'chooseno') {
            // User selected custom numbers
            chooseNo = "";
            var len = (lotteryName == 'lotto 649') ? 6 : 7;
            for(let j=1; j<=len; j++) {
              const numberValue = req.body['cop'+j+'_'+lotteryId + '_tn_' + i];
              if(numberValue) {
                chooseNo += numberValue + ',';
              }
            }
            chooseNo = chooseNo ? chooseNo.slice(0,-1) : "N/A";
          } else if(isQuickPick) {
            chooseNo = "Quick Pick";
          }
        }

        all_tickets.push({
          lotteryId: lotteryId,
          lotteryName: lotteryName,
          playerName: playerName,
          isQuickPick: isQuickPick,
          chooseNo: chooseNo,
          price: ticketPrice,
          regFee: 4
        });
      }
    });
    
    var pay_det = {
      card_type: req.body.card_type,
      card_no: req.body.card,
      exp_date: req.body.exp,
      cvv: req.body.cvv,
      amount: req.body.lotterypricetotal
    };
    
    // Use parameterized query to prevent SQL injection
    const user_sql = 'SELECT * FROM user WHERE id = ?';
    const [results] = await db.pool.query(user_sql, [req.session.userid]);
    
    if (!results || results.length === 0) {
      return res.status(404).render('error', { message: 'User not found', error: { status: 404 } });
    }
    
    res.render('myreview', {
      title: 'Review', 
      tickets: all_tickets, 
      user: results[0], 
      pay_det: pay_det, 
      isLogged: true,
      sqAppKey: process.env.SANDBOX_TEST_APP_KEY,
      sqLocationId: process.env.TEST_LOCATION_ID
    });
  } catch (err) {
    console.error('Error in review process:', err);
    res.status(500).render('error', { message: 'Error processing review', error: err });
  }
});


module.exports = router;