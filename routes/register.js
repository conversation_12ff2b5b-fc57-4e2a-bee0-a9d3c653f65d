var express = require('express');
var router = express.Router();
const { requireCanadianIP } = require("../lib/geolocation"); // IP geolocation blocking

/* GET home page. */
router.get('/', requireCanadianIP, function(req, res, next) {
  // Log successful Canadian access
  if (req.userLocation) {
    console.log(`✅ Canadian user accessing register: ${req.userLocation.city}, ${req.userLocation.region}`);
  }

  res.render('register', { title: 'Register' });
});

module.exports = router;
