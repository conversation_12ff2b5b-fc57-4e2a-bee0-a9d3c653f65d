var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
// const sgMail = require('@sendgrid/mail');
require('dotenv').config();
var api = require('../node_modules/clicksend/api.js');
var smsApi = new api.SMSApi(process.env.CLICK_SEND_USERNAME, process.env.CLICK_SEND_API_KEY);
var smsMessage = new api.SmsMessage();

var db = require("../lib/database");
const { sendOtpEmail } = require("../lib/mailer-send/mailerSendOTP");
const { sendMailByNodemailer } = require('../lib/mailer-send/nodeMailSender.js');
const security = require("../lib/security"); // Security utilities
const { requireCanadianIP } = require("../lib/geolocation"); // IP geolocation blocking

/* GET home page. */
router.get('/', requireCanadianIP, function (req, res, next) {
    console.log("get Registration view");

    // Log successful Canadian access
    if (req.userLocation) {
        console.log(`✅ Canadian user accessing registration: ${req.userLocation.city}, ${req.userLocation.region}`);
    }

    res.render('register', { title: 'Registration', isLogged: false });
});

// Add strong rate limiting and input sanitization to registration
router.post('/', security.rateLimit(2, 30 * 60 * 1000), security.sanitizeBody, async function (req, res, next) {
    try {
        // Enhanced validation
        const phone = req.body.countryCode + req.body.phone;

        // Validate required fields
        if (!req.body.firstName || !req.body.lastName || !req.body.email || !req.body.phone ||
            !req.body.province || !req.body.password || !req.body.under18) {
            return res.redirect('/registration?text=Please fill in all required fields.');
        }

        // Validate email format
        if (!security.isValidEmail(req.body.email)) {
            return res.redirect('/registration?text=Please enter a valid email address.');
        }

        // Validate phone format
        if (!security.isValidPhone(phone)) {
            return res.redirect('/registration?text=Please enter a valid phone number.');
        }

        // Validate password strength (basic)
        if (req.body.password.length < 8) {
            return res.redirect('/registration?text=Password must be at least 8 characters long.');
        }

        // Validate password match
        if (req.body.password !== req.body.cpassword) {
            return res.redirect('/registration?text=Passwords do not match');
        }

        // Basic validation for obviously fake data
        const firstName = req.body.firstName.trim();
        const lastName = req.body.lastName.trim();
        const email = req.body.email.trim().toLowerCase();

        // Block only clearly fake patterns (very conservative)
        if (
            // Names with numbers (clearly fake)
            /[0-9]/.test(firstName) || /[0-9]/.test(lastName) ||
            // Extremely long names (over 20 chars - clearly random)
            firstName.length > 20 || lastName.length > 20 ||
            // Test email patterns
            email.includes('test@') || email.includes('fake@') || email.includes('dummy@')
        ) {
            console.log(`Blocked obviously fake registration: ${firstName} ${lastName} - ${email}`);
            return res.redirect('/registration?text=Please use valid name and email information.');
        }
        console.log("post Registration view");
        console.log("req type = " + req.get('content-type'));
        console.log("req firstName = " + req.body.firstName);
        console.log("req firstName = " + req.body.email);
        console.log("index page post " + JSON.stringify(req.body));
        console.dir(req.body, { depth: null });

        // Check if email already exists - use parameterized query
        const sqlCheckEmail = 'SELECT * FROM user WHERE email_id = ?';
        const [emailExists] = await db.pool.query(sqlCheckEmail, [req.body.email]);
        
        if (emailExists && emailExists.length > 0) {
            return res.redirect('/registration?text=Email address already exist, kindly use a new one.&firstName=' + 
                req.body.firstName + '&lastName=' + req.body.lastName + '&email=' + req.body.email + 
                '&countryCode=' + req.body.countryCode + '&phone=' + req.body.phone + '&city=' + 
                req.body.city + '&province=' + req.body.province + "&postal=" + req.body.postal);
        }
        
        // Check if phone already exists - use parameterized query
        const sqlCheckPhone = 'SELECT * FROM user WHERE phone_number = ?';
        const [phoneExists] = await db.pool.query(sqlCheckPhone, [phone]);
        
        if (phoneExists && phoneExists.length > 0) {
            return res.redirect('/registration?text=Phone number already exist.&firstName=' + 
                req.body.firstName + '&lastName=' + req.body.lastName + '&email=' + req.body.email + 
                '&countryCode=' + req.body.countryCode + '&phone=' + req.body.phone + '&city=' + 
                req.body.city + '&province=' + req.body.province + "&postal=" + req.body.postal);
        }
        
        // Get IP address
        const ip = req.header('x-forwarded-for') || req.connection.remoteAddress;
        
        // Hash password
        const salt = bcrypt.genSaltSync();
        const hashpass = bcrypt.hashSync(req.body.password, salt);
        
        // Insert new user - use parameterized query
        const insertSql = `
            INSERT INTO user (
                first_name, last_name, email_id, ip_address, phone_number,
                city, province, postal_code, tcppc, password, data_time, country
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const [insertResult] = await db.pool.query(insertSql, [
            req.body.firstName,
            req.body.lastName,
            req.body.email,
            ip,
            phone,
            req.body.city,
            req.body.province,
            req.body.postal,
            'accepted', // tcppc field for terms acceptance
            hashpass,
            new Date(),
            'Canada'
        ]);
        
        // Set session variables
        sess = req.session;
        sess.email = req.body.email;
        sess.userid = insertResult.insertId;
        sess.admin = false;
        sess.user = true;
        
        // Generate JWT token
        let jwtSecretKey = process.env.JWT_SECRET_KEY;
        let data = { userEmail: req.body.email };
        console.log('userEmail', data.userEmail);
        
        const token = jwt.sign({
            exp: Math.floor(Date.now() / 1000) + (60 * 60),
            data
        }, jwtSecretKey);
        
        // Generate verification code
        const code = Math.floor((Math.random() * 1000000) + 1);
        console.log("--------------------------------");
        console.log("Verification code ---- ", code);
        console.log("--------------------------------");
        
        // Update user with verification code - use parameterized query
        const updateSql = 'UPDATE user SET code = ? WHERE email_id = ?';
        await db.pool.query(updateSql, [code, req.body.email]);
        
        // Send SMS verification
        smsMessage.from = process.env.CLICK_SEND_NUMBER;
        smsMessage.to = phone; // Use the full phone number with country code
        smsMessage.body = `Your Verification code for LetsPlayLottery is ${code}.`;
        var smsCollection = new api.SmsMessageCollection();
        smsCollection.messages = [smsMessage];
        
        if (process.env.TWOWAY_VERIFICATION != "OFF") {
            try {
                const response = await smsApi.smsSendPost(smsCollection);
                console.log(response.body);
            } catch (err) {
                console.error("SMS sending error:", err.body);
            }
        }
        
        // Send email verification
        let emailstr = `<html>
            <head>
            <style>
                .email-container {
                font-family: Arial, sans-serif;
                max-width: 500px;
                margin: auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #f9f9f9;
                text-align: center;
                }
                .otp {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
                margin: 10px 0;
                }
                .footer {
                font-size: 12px;
                color: #555;
                margin-top: 20px;
                }
            </style>
            </head>
            <body>
            <div class="email-container">
                <h2>Hello, ${req.body.firstName} ${req.body.lastName}!</h2>
                <p>Your One-Time Password (OTP) for verification is:</p>
                <p class="otp">${code}</p>
                <p class="footer">If you did not request this OTP, please ignore this email.</p>
            </div>
            </body>
        </html>`;
        
        try {
            const emailResult = await sendMailByNodemailer({
                to: req.body.email,
                subject: "Let's play verification code!",
                html: emailstr
            });
            console.log("Email sent:", emailResult);
        } catch (err) {
            console.error("Email sending error:", err);
        }
        
        // Redirect to verification page
        return res.redirect("/verification?type=register&token=" + token);
        
    } catch (err) {
        console.error("Registration error:", err);
        let errorMessage = 'An error occurred during registration.';
        if (err.code === 'ER_DUP_ENTRY') {
            if (err.message.includes('email_id')) {
                errorMessage = 'Email address already exists.';
            } else if (err.message.includes('phone_number')) {
                errorMessage = 'Phone number already exists.';
            }
        }
        return res.redirect('/registration?text=' + encodeURIComponent(errorMessage) + '&firstName=' + 
            encodeURIComponent(req.body.firstName) + '&lastName=' + encodeURIComponent(req.body.lastName) + 
            '&email=' + encodeURIComponent(req.body.email) + '&countryCode=' + encodeURIComponent(req.body.countryCode) + 
            '&phone=' + encodeURIComponent(req.body.phone) + '&city=' + encodeURIComponent(req.body.city) + 
            req.body.city + '&province=' + req.body.province + "&postal=" + req.body.postal);
    }
});

module.exports = router;