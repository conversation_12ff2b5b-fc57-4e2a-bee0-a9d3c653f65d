var express = require('express');
var router = express.Router();
var mysql = require('mysql');
var db = require("../lib/database");

/* GET home page. */
router.get('/', async function(req, res, next) {
  try {
    if(req.session && req.session.user) {
      // Use parameterized query to prevent SQL injection
      const sql = 'SELECT * FROM lottery_master WHERE is_active = 1 AND is_deleted = 0';
      const [results] = await db.pool.query(sql);
      
      res.render('play', { 
        title: 'Play Lottery',
        lotteries: results, 
        isLogged: require("../lib/isLogged")(req) 
      });
    } else {
      console.log('Send to static play lottery page - user not logged in');
      res.render('play-static', { 
        title: 'Play Lottery', 
        isLogged: require("../lib/isLogged")(req)
      });
    }
  } catch (err) {
    console.error('Error fetching lotteries:', err);
    res.status(500).render('error', { 
      message: 'Error loading lottery data', 
      error: err 
    });
  }
});

router.post('/', function(req, res, next) {
  
  //const data = dataHandler.registerUser(con, req.body);
  
  res.render('play', { title: 'Play Lottery' });
});

module.exports = router;
