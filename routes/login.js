require('dotenv').config();
var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const db = require("../lib/database");

var api = require('../node_modules/clicksend/api.js');

const { sendMailByNodemailer } = require('../lib/mailer-send/nodeMailSender.js');

var smsApi = new api.SMSApi(process.env.CLICK_SEND_USERNAME, process.env.CLICK_SEND_API_KEY);


var smsMessage = new api.SmsMessage();

smsMessage.from = "+18332405027";
smsMessage.to = "+919595992021";
smsMessage.body = "test message";

// test api
// var smsApi = new api.SMSApi(process.env.CLICK_SEND_USERNAME, process.env.CLICK_SEND_API_KEY);

var smsCollection = new api.SmsMessageCollection();

smsCollection.messages = [smsMessage];


//

router.get("/testsms",(req,res)=>{
	
	res.send("hii")
});

/* GET home page. */
router.get('/', function (req, res, next) {
	res.render('signup', {
		title: 'Login', 
		isLogged: false
	});
});


router.post('/', function (req, res, next) {
	console.dir(req.body, {
		depth: null
	})

	var count = 0;


	//con.connect(function(err) {
	//	if (err) return console.error(err);
	console.log("Connected!: "+req.body);
	// SECURITY FIX: Use parameterized query to prevent SQL injection
	var sql = 'SELECT id, password, first_name, last_name, phone_number FROM user WHERE email_id = ?';
	db.pool.query(sql, [req.body.email]).then(([result]) => {
		if (result.length > 0) {
			const user = result[0];
			const passwordMatch = bcrypt.compareSync(req.body.password, user.password);
			if (passwordMatch) {
				let jwtSecretKey = process.env.JWT_SECRET_KEY;
				let data = {
					userEmail: req.body.email
				}
				
				const token = jwt.sign({ exp: Math.floor(Date.now() / 1000) + (60 * 60),data}, jwtSecretKey);
				const code =  Math.floor((Math.random() * 1000000) + 1);

				console.log("--------------------------------");
				console.log("Verification code ---- ",code);
				console.log("--------------------------------");

				// SECURITY FIX: Use parameterized query to prevent SQL injection
				let sql1 = 'UPDATE user SET code = ? WHERE email_id = ?';
				db.pool.query(sql1, [code, req.body.email]).then(([result]) => {
					// if (err) return console.error(err);
					if(result){
						smsMessage.from = process.env.CLICK_SEND_NUMBER;
						smsMessage.to = user.phone_number;
						smsMessage.body = `Your Verification code for LetsPlayLottery is ${code}.`;
						var smsCollection = new api.SmsMessageCollection();
						smsCollection.messages = [smsMessage];
						if(process.env.TWOWAY_VERIFICATION!="OFF"){
							
							smsApi.smsSendPost(smsCollection).then(function(response) {
								console.log("OTP Successful");
								console.log(response.body);
							}).catch(function(err){
								console.error(err.body);
							});

							// console.log("Sending Email");
	
						}
						let emailstr = `<html>
											<head>
											<style>
												.email-container {
												font-family: Arial, sans-serif;
												max-width: 500px;
												margin: auto;
												padding: 20px;
												border: 1px solid #ddd;
												border-radius: 8px;
												background-color: #f9f9f9;
												text-align: center;
												}
												.otp {
												font-size: 24px;
												font-weight: bold;
												color: #007bff;
												margin: 10px 0;
												}
												.footer {
												font-size: 12px;
												color: #555;
												margin-top: 20px;
												}
											</style>
											</head>
											<body>
											<div class="email-container">
												<h2>Hello, ${user.first_name} ${user.last_name}!</h2>
												<p>Your One-Time Password (OTP) for verification is:</p>
												<p class="otp">${code}</p>
												<p class="footer">If you did not request this OTP, please ignore this email.</p>
											</div>
											</body>
										</html>`;
						sendMailByNodemailer({
							to:req.body.email,
							subject:"Let's play verification code!",
							html: emailstr
						}).then(res=>{
							console.log(res);
						}).catch(err=>{
							console.log(err);
							
						})

						res.redirect("/verification?type=login&token=" + token);

					}
				});
	
			} else {
				res.render('signup', {
					title: 'Login', 
					isLogged: false,
					error_msg:"Password Incorrect",
				});
				// return	res.json({status: 500, msg: "Password Incorrect"});

			}
		} else {
			res.render('signup', {
				title: 'Login', 
				isLogged: false,
				error_msg:"User not  Found",
			});
			// return	res.json({status: 500, msg: "user not  Found"});
		}

	});




});


module.exports = router;
