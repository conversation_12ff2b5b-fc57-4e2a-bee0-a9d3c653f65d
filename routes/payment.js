var express = require('express');
var router = express.Router();
const https = require('https');
const querystring = require('querystring');
const crypto = require('crypto');
require('dotenv').config()
const { paymentsApi, locationsApi } = require('../utils/square-client');

var db = require("../lib/database");
const { v4: uuidv4 } = require('uuid');

router.post('/', async function (req, res) {
  const locationResponse = await locationsApi.retrieveLocation(process.env.TEST_LOCATION_ID);
  const currency = locationResponse.result.location.currency;
  const idempotencyKey = crypto.randomUUID();

  const token = req.body.token;

  // Charge the customer's card
  const requestBody = {
    idempotencyKey,
    sourceId: token,
    amountMoney: {
      amount: req.body.amount,
      currency
    }
  };

  try {
    const { result: { payment } } = await paymentsApi.createPayment(requestBody);

    const result = JSON.stringify(payment, (key, value) => {
      return typeof value === "bigint" ? parseInt(value) : value;
    }, 4);

    res.json({
      result
    });
  } catch (error) {
    res.json(error.result);
  }
});

router.post('/success', async function(req, res, next) {
  try {
    let order_details = [];
    let max_index = req.body.lotteryId.length;

    for(let temp_index=0; temp_index<max_index; temp_index++) {
      let t = await setTicket1(req.body, temp_index);
      order_details.push(t);
    } 
    
    // Use parameterized query to prevent SQL injection
    const sqlP = `INSERT INTO lottery.payment_ticket_details 
      (payment_amount, date_received, payment_method, payment_email_id, tx_id, payment_message, payment_status, date_added) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const params = [
      req.body.price[0],
      formatDate(new Date()),
      'Card',
      req.body.user_email,
      '152215',
      'Payment Success',
      'COMPLETED',
      formatDate(new Date())
    ];
    
    await db.pool.query(sqlP, params);
    
    res.render('success', { 
      title: 'Ticket Booked', 
      data: {"body": req.body, "order_details": order_details} 
    });
  } catch (err) {
    console.error('Error processing payment success:', err);
    res.status(500).render('error', { 
      message: 'Error processing payment', 
      error: err 
    });
  }
});

async function setTicket1(data, temp_index) {
  try {
    let group_no = 0, person_in_group = 0;

    // Improved logic to determine quickpick flag
    let quickpickflg = 1; // Default to Quick Pick
    const chooseNoValue = data.chooseNo[temp_index];

    if(chooseNoValue && typeof chooseNoValue === 'string') {
      if(chooseNoValue.includes(",")) {
        // Contains comma, likely custom numbers like "1,2,3,4,5,6"
        quickpickflg = 0;
      } else if(chooseNoValue === "Quick Pick") {
        // Explicitly Quick Pick
        quickpickflg = 1;
      } else if(chooseNoValue === "N/A") {
        // No number selection for this lottery type
        quickpickflg = 1;
      }
    }
    
    // Use parameterized query to prevent SQL injection
    const sqlQ = `SELECT MAX(group_no) AS currentGroup, COUNT(person_in_group) AS personCount, 
      person_per_group as personInGroup, no_of_groups as groupPerLottery 
      FROM lottery.ticket INNER JOIN lottery_master ON ticket.lottery_id = lottery_master.id 
      WHERE id = ? group by group_no ORDER BY currentGroup DESC LIMIT 1`;
    
    const [results] = await db.pool.query(sqlQ, [data.lotteryId[temp_index]]);
    
    if(results && results.length > 0) {
      if(results[0].personCount < results[0].personInGroup) {
        group_no = results[0].currentGroup;
        person_in_group = results[0].personCount + 1;
      } else {
        // Only allow groups 1-4 (A, B, C, D)
        const nextGroup = results[0].currentGroup + 1;
        if(nextGroup <= 4) {
          group_no = nextGroup;
          person_in_group = 1;
        } else {
          // If all 4 groups are full, start a new cycle from group 1
          group_no = 1;
          person_in_group = 1;
        }
      }
    } else {
      // Default values if no results
      group_no = 1;
      person_in_group = 1;
    }
    
    const myUuid = uuidv4();
    
    // Use parameterized query for insert
    const insertSql = `INSERT INTO ticket 
      (user_id, lottery_id, group_no, quickpick, playerName, person_in_group, created_by, created_date, misc, custom_number, payment_tx_id, uuid) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const insertParams = [
      data.user_id,
      data.lotteryId[temp_index],
      group_no,
      quickpickflg,
      data.playerName[temp_index],
      person_in_group,
      data.user_email,
      formatDate(new Date()),
      'confirmed',
      data.chooseNo[temp_index],
      '1525252388',
      myUuid
    ];
    
    const [result] = await db.pool.query(insertSql, insertParams);
    
    return {
      "lottery_id": data.lotteryId[temp_index],
      "lottery_name": data.lotteryName[temp_index],
      "group_no": group_no,
      "quickpick": quickpickflg,
      "custom_number": data.chooseNo[temp_index],
      "playerName": data.playerName[temp_index],
      "person_in_group": person_in_group,
      "created_date": new Date(),
      "ticket_id": result.insertId,
      "uuid": myUuid
    };
  } catch (err) {
    console.error('Error in setTicket1:', err);
    throw err;
  }
}

function formatDate(date) {
  var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join('-');
}

module.exports = router;