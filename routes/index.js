var express = require("express");
var router = express.Router();
// var nodemailer = require('nodemailer');
const { allResolved } = require("q");
var auth = require('../lib/auth');
var db = require("../lib/database");
const { route } = require("./lotteries");
const { sendMailByNodemailer } = require("../lib/mailer-send/nodeMailSender");


require("util").inspect.defaultOptions.depth = null;


router.use("/login", require("./login"));
router.use("/verification", require("./verification"));
router.use("/changePassword", require("./password"));
router.use("/password-update", require("./updatePassword"));
router.use("/registration", require("./registration"));
router.use("/lotteries", require("./lotteries"));
router.use("/about", require("./about"));
//for demo
router.use("/demo", require("./demo")); //Added by me
router.use("/faq", require("./faq"));
router.use("/contact-us", require("./contact-us"));
router.use("/play",require("./play"));
router.use("/review",auth,require("./review"));
router.use("/payment",auth,require("./payment"));
router.use("/privacy-policy", require("./privacy-policy"));
router.use("/terms-conditions", require("./terms-conditions"));




/* GET home page. */
router.get("/", function(req, res, next) {
    res.render("index", { title: "Express", isLogged: require("../lib/isLogged")(req) });
});
router.get("/testMail", function (req, res, next) {
  sendMailByNodemailer({
    to: "<EMAIL>",
    subject: "Let's play verification code!",
    html: "test mail"
  }).then(r => {
    console.log(r);
    res.send(r);
  }).catch(e=>{
    res.send(e)
  })
})

/* POST home page. */
router.post("/", function(req, res, next) {

	
	console.log("index page post " + JSON.stringify(req.body));
	
	console.dir(req.body,{depth:null})

  /*  transporter.sendMail(mailOptions, function(error, info){
        if (error) {
          console.log(error);
        } else {
          console.log('Email sent: ' + info.response);
        }
    });*/

    res.render("index", { title: "Express" });
});

router.get('/logout', function(req, res) {
  req.session.destroy();
  res.redirect('/')
});

// Rate limiting storage (in production, use Redis)
const subscriptionAttempts = new Map();

router.post('/subscribe', async function(req, res) {
  try {
    const email = req.body.subEmail;
    const honeypot = req.body.website; // Honeypot field

    // Get IP address for rate limiting
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;

    // HONEYPOT CHECK - if filled, it's likely a bot (but be more lenient)
    if (honeypot && honeypot.trim() !== '' && honeypot.trim().length > 0) {
      console.log(`Honeypot triggered from IP: ${ip}, value: "${honeypot}"`);
      // Only block if it's clearly spam (not just auto-fill)
      if (honeypot.trim().length > 5 || /http|www|spam|bot/i.test(honeypot)) {
        console.log(`Blocking obvious spam from IP: ${ip}`);
        return res.redirect('/subscribe/success'); // Don't reveal it's blocked
      } else {
        console.log(`Allowing potential auto-fill from IP: ${ip}`);
      }
    }

    // RATE LIMITING - max 3 attempts per IP per hour
    const currentTime = Date.now();
    const hourAgo = currentTime - (60 * 60 * 1000);

    if (!subscriptionAttempts.has(ip)) {
      subscriptionAttempts.set(ip, []);
    }

    const ipAttempts = subscriptionAttempts.get(ip).filter(time => time > hourAgo);

    if (ipAttempts.length >= 3) {
      console.log(`Rate limit exceeded for IP: ${ip}`);
      return res.redirect('/?error=rate_limit_exceeded');
    }

    // Add current attempt
    ipAttempts.push(currentTime);
    subscriptionAttempts.set(ip, ipAttempts);

    // Validate email
    if (!email || !email.trim()) {
      return res.redirect('/?error=email_required');
    }

    // Enhanced email validation
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!emailRegex.test(email.trim())) {
      return res.redirect('/?error=invalid_email');
    }

    // COMPREHENSIVE SPAM DOMAIN BLOCKING - RE-ENABLED WITH BETTER LOGIC
    const emailDomain = email.trim().toLowerCase().split('@')[1];
    console.log(`Processing subscription for email: ${email}, domain: ${emailDomain}`);

    const spamDomains = [
      // Temporary email services (high confidence spam)
      '10minutemail.com', 'guerrillamail.com', 'mailinator.com', 'tempmail.org',
      'throwaway.email', 'getnada.com', 'temp-mail.org', 'maildrop.cc',
      'sharklasers.com', 'guerrillamailblock.com', 'pokemail.net', 'spam4.me',
      'tempail.com', 'tempinbox.com', 'tempmailo.com', 'tempr.email',
      // Known spam domains from your data
      'austinpowder.com', 'warmntoasty.com'
    ];

    // Check exact domain match for known spam domains
    if (spamDomains.includes(emailDomain)) {
      console.log(`Blocked known spam domain: ${emailDomain} from IP: ${ip}`);
      return res.redirect('/subscribe/success'); // Don't reveal it's blocked
    }

    // Check for highly suspicious patterns (be more conservative)
    const suspiciousPatterns = [
      /^[a-z]{15,}\.com$/, // Very long random strings (15+ chars)
      /^\d{5,}\.com$/, // Long number-only domains
      /^[a-z]{1,2}\d{5,}\.com$/, // Short letters + long numbers
    ];

    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(emailDomain));
    if (isSuspicious) {
      console.log(`Blocked suspicious domain pattern: ${emailDomain} from IP: ${ip}`);
      return res.redirect('/subscribe/success'); // Don't reveal it's blocked
    }

    // TEMPORARY: Add directly as confirmed for testing
    // Check if email already exists
    const checkSql = 'SELECT * FROM user_subscribe_list WHERE email = ?';
    const [existingSubscriber] = await db.pool.query(checkSql, [email.trim().toLowerCase()]);

    if (existingSubscriber.length > 0) {
      console.log(`Email already exists: ${email}`);
      return res.redirect('/subscribe/success');
    }

    // Insert as CONFIRMED subscription (temporary for testing)
    const sql = `INSERT INTO user_subscribe_list
                 (email, ip_address, is_confirmed, confirmed_at, created_at)
                 VALUES (?, ?, 1, NOW(), NOW())`;
    const [result] = await db.pool.query(sql, [email.trim().toLowerCase(), ip]);

    if (result.affectedRows > 0) {
      console.log(`Subscriber added directly as confirmed: ${email}, IP: ${ip}`);
      res.redirect('/subscribe/success');
    } else {
      console.log(`Failed to add subscriber: ${email}`);
      res.redirect('/?error=subscription_failed');
    }

  } catch (err) {
    console.error('Subscription error:', err);
    res.redirect('/?error=subscription_failed');
  }
});

// Email confirmation function
async function sendConfirmationEmail(email, token) {
  try {
    const confirmationUrl = `${process.env.BASE_URL || 'https://letsplaygrouplottery.com'}/subscribe/confirm?token=${token}`;

    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c5aa0;">Confirm Your Newsletter Subscription</h2>
        <p>Thank you for subscribing to Let's Play Group Lottery newsletter!</p>
        <p>To complete your subscription and start receiving updates about our lottery groups, please click the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${confirmationUrl}" style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Confirm Subscription
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${confirmationUrl}</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          If you didn't subscribe to our newsletter, please ignore this email.
          <br>This confirmation link will expire in 24 hours.
        </p>
      </div>
    `;

    // Send email using your existing email system
    const db = require('../lib/database');
    await db.sendEmail(
      null, // No image path
      'newsletter', // Template directory
      null, // No file attachment
      '<EMAIL>', // From address
      [email], // To address
      {
        subject: 'Confirm Your Newsletter Subscription - Let\'s Play Group Lottery',
        content: emailContent
      },
      false // Not a group email
    );

    console.log(`Confirmation email sent to: ${email}`);
  } catch (error) {
    console.error('Error sending confirmation email:', error);
    throw error;
  }
}

// Subscription pending page
router.get('/subscribe/pending', function(req, res) {
  res.render('subscribe-pending', {
    title: 'Subscription Pending - Let\'s Play Group Lottery',
    message: 'Please check your email and click the confirmation link to complete your subscription.'
  });
});

// Email confirmation route
router.get('/subscribe/confirm', async function(req, res) {
  try {
    const token = req.query.token;

    if (!token) {
      return res.redirect('/?error=invalid_confirmation_link');
    }

    // Find subscriber with this token
    const sql = 'SELECT * FROM user_subscribe_list WHERE confirmation_token = ? AND is_confirmed = 0';
    const [subscribers] = await db.pool.query(sql, [token]);

    if (subscribers.length === 0) {
      return res.redirect('/?error=invalid_or_expired_link');
    }

    const subscriber = subscribers[0];

    // Check if token is expired (24 hours)
    const tokenAge = Date.now() - new Date(subscriber.created_at).getTime();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    if (tokenAge > twentyFourHours) {
      // Delete expired pending subscription
      await db.pool.query('DELETE FROM user_subscribe_list WHERE id = ?', [subscriber.id]);
      return res.redirect('/?error=confirmation_link_expired');
    }

    // Confirm the subscription
    const updateSql = 'UPDATE user_subscribe_list SET is_confirmed = 1, confirmed_at = NOW() WHERE id = ?';
    await db.pool.query(updateSql, [subscriber.id]);

    console.log(`Email confirmed: ${subscriber.email}`);
    res.redirect('/subscribe/success');

  } catch (err) {
    console.error('Email confirmation error:', err);
    res.redirect('/?error=confirmation_failed');
  }
});

// Subscription success page
router.get('/subscribe/success', function(req, res) {
  res.render('subscribe-success', { title: 'Subscription Successful - Let\'s Play Group Lottery' });
});

router.get('/unsubscribe', async function(req, res) {
  try {
    console.log('Unsubscribe request for email:', req.query.email);
    
    // Use parameterized query for security
    const sql = 'DELETE FROM user_subscribe_list WHERE email = ?';
    const [result] = await db.query(sql, [req.query.email]);
    
    console.log('Unsubscribe result:', result);
    
    // Render success page regardless of whether email was found
    // This is a security best practice to not reveal if an email exists
    res.render("unsubscribe", { 
      title: "Unsubscribe Successful", 
      message: "You have been successfully unsubscribed from our newsletter."
    });
  } catch (err) {
    console.error('Error during unsubscribe:', err);
    res.render("unsubscribe", { 
      title: "Unsubscribe", 
      message: "There was an issue processing your request. Please try again later."
    });
  }
});


module.exports = router;
