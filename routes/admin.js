var express = require("express");
var router = express.Router();
var adminAuth = require('../lib/auth');

router.get('/',function(req,res,next){
    return res.redirect('/admin/login');
})
router.use('/login',require('./admin/login'));
// router.use('/dashboard',auth,require('./admin/dashboard'));
router.use('/lottery',adminAuth,require('./admin/lottery'));
router.use('/user-list',require('./admin/user'));
//router.use('/lottery-status',auth,require('./admin/lottery-status'));
router.use('/payment',adminAuth,require('./admin/confirm-payment'));
router.use('/email',adminAuth,require('./admin/email'));
router.use('/subscribers',adminAuth,require('./admin/subscribers'));
router.use('/newsletter-broadcast',adminAuth,require('./admin/newsletter-broadcast'));  // Main newsletter route
router.use('/all-registered-users',adminAuth,require('./admin/all-registered-users')); // All registered users management
router.use('/test-data',require('./admin/test-data')); // Route for adding test data

module.exports = router;
