var express = require('express');
var router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require("../lib/database");
const sgMail = require('@sendgrid/mail');
require('dotenv').config();
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const client = require('twilio')(accountSid, authToken);
var api = require('../node_modules/clicksend/api.js');
var smsApi = new api.SMSApi(process.env.CLICK_SEND_USERNAME, process.env.CLICK_SEND_API_KEY);
var smsMessage = new api.SmsMessage();

/* GET home page. */
router.get('/', function (req, res, next) {
	res.render('signup', {
		title: 'Login', 
		isLogged: false
	});
});


router.post('/', async function (req, res, next) {
	console.dir(req.body, {
		depth: null
	});

	try {
		// Use parameterized query to prevent SQL injection
		const sql = 'SELECT id, password, phone_number, is_active FROM user WHERE email_id = ?';
		const [result] = await db.pool.query(sql, [req.body.email]);
		
		if (!result || result.length === 0) {
			return res.json({status: 404, msg: "User not found"});
		}
		
		const user = result[0];
		console.log('res = ', user);
		
		if(user.is_active == 0){
			return res.json({status: 500, msg: "User is not active. Please contact <NAME_EMAIL> to activate it."});
		}
		
		if (bcrypt.compareSync(req.body.password, user.password)) {

				let jwtSecretKey = process.env.JWT_SECRET_KEY;
				let data = {
					userEmail: req.body.email
				}
				let phone_number = user.phone_number;
				const token = jwt.sign({ exp: Math.floor(Date.now() / 1000) + (60 * 60), data}, jwtSecretKey);
				const code = Math.floor((Math.random() * 1000000) + 1);
				
				console.log("--------------------------------");
				console.log("Verification code ---- ", code);
				console.log("--------------------------------");

				// Use parameterized query to prevent SQL injection
				const sql1 = 'UPDATE user SET code = ? WHERE email_id = ?';
				const [updateResult] = await db.pool.query(sql1, [code, req.body.email]);
				
				if(updateResult) {
					smsMessage.from = process.env.CLICK_SEND_NUMBER;
					smsMessage.to = phone_number;
					smsMessage.body = `Your Verification code for LetsPlayLottery is ${code}.`;
					var smsCollection = new api.SmsMessageCollection();
					smsCollection.messages = [smsMessage];
					
					if(process.env.TWOWAY_VERIFICATION !== "OFF") {
						try {
							const response = await smsApi.smsSendPost(smsCollection);
							console.log("OTP Successful");
							console.log(response.body);
						} catch (err) {
							console.error(err.body);
						}
					}
					sgMail.setApiKey(process.env.SENDGRID_API_KEY);
					const msg = {
					  to: req.body.email,
					  from: '<EMAIL>',
					  subject: 'Verification Code',
					  text: 'Your Verification code for LetsPlayLottery is '+code,
					  html: '<strong>Your Verification code for LetsPlayLottery is '+code+'</strong>',
					};
					
					try {
						await sgMail.send(msg);
						console.log('Email sent');
					} catch (error) {
						console.error(error);
					}
					
					res.redirect("/verification?type=login&token="+token);
				}
			} else {
				res.render('signup', {
					title: 'Login', 
					isLogged: false,
					error_msg:"Password Incorrect",
					email:req.body.email
				});
			}
		} else {
			res.render('signup', {
				title: 'Login', 
				isLogged: false,
				error_msg:"User not found",
				email:req.body.email
			});
		}
	} catch (err) {
		console.error('Login error:', err);
		res.status(500).render('error', {
			message: 'Error during login',
			error: err
		});
	}
});


module.exports = router;