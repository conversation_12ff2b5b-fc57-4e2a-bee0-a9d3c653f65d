const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
    host: '*************',
    user: 'errol',
    password: 'qwby:123eweD',
    database: 'errol'
};

// Canadian cities and provinces for validation
const canadianCities = [
    // Major Canadian cities
    'toronto', 'montreal', 'vancouver', 'calgary', 'edmonton', 'ottawa', 'winnipeg',
    'quebec city', 'hamilton', 'kitchener', 'london', 'victoria', 'halifax', 'oshawa',
    'windsor', 'saskatoon', 'regina', 'sherbrooke', 'kelowna', 'barrie', 'abbotsford',
    'kingston', 'sudbury', 'saguenay', 'thunder bay', 'kamloops', 'nanaimo', 'brantford',
    'red deer', 'lethbridge', 'saint john', 'moncton', 'sarnia', 'fredericton', 'chilliwack',
    'medicine hat', 'vernon', 'north bay', 'prince george', 'peterborough', 'belleville',
    'sault ste marie', 'cornwall', 'chatham', 'georgetown', 'st catharines', 'niagara falls',
    'welland', 'waterloo', 'guelph', 'cambridge', 'burlington', 'oakville', 'mississauga',
    'brampton', 'markham', 'vaughan', 'richmond hill', 'newmarket', 'aurora', 'whitby',
    'pickering', 'ajax', 'milton', 'halton hills', 'georgetown', 'acton', 'oakville'
];

const canadianProvinces = [
    'ontario', 'quebec', 'british columbia', 'alberta', 'manitoba', 'saskatchewan',
    'nova scotia', 'new brunswick', 'newfoundland and labrador', 'prince edward island',
    'northwest territories', 'nunavut', 'yukon', 'on', 'qc', 'bc', 'ab', 'mb', 'sk',
    'ns', 'nb', 'nl', 'pe', 'nt', 'nu', 'yt'
];

async function cleanupDatabase() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Connected successfully!');
        
        // Get initial count
        const [initialCount] = await connection.execute('SELECT COUNT(*) as total FROM user');
        console.log(`📊 Initial user count: ${initialCount[0].total}`);
        
        // Show breakdown before cleanup
        console.log('\n📋 Current user breakdown by city:');
        const [cityBreakdown] = await connection.execute(`
            SELECT city, COUNT(*) as count 
            FROM user 
            GROUP BY city 
            ORDER BY count DESC 
            LIMIT 10
        `);
        cityBreakdown.forEach(row => {
            console.log(`   ${row.city || '[EMPTY]'}: ${row.count} users`);
        });
        
        console.log('\n🧹 Starting cleanup process...');
        
        // 1. Delete users from Mountain View (obvious spam - 2135 users)
        console.log('\n🗑️  Step 1: Deleting Mountain View spam users...');
        const [mountainViewResult] = await connection.execute(`
            DELETE FROM user WHERE city = 'Mountain View'
        `);
        console.log(`   ✅ Deleted ${mountainViewResult.affectedRows} Mountain View users`);
        
        // 2. Delete users with fake/test cities
        console.log('\n🗑️  Step 2: Deleting fake city users...');
        const [fakeCityResult] = await connection.execute(`
            DELETE FROM user WHERE 
                city IN ('123', 'test', 'usa', 'klfnejkfe', 'sjhwbs', 'MEW') OR
                city LIKE '%<script%' OR
                city LIKE '%alert%' OR
                city = '' OR
                city IS NULL
        `);
        console.log(`   ✅ Deleted ${fakeCityResult.affectedRows} fake city users`);
        
        // 3. Delete users with fake/test emails
        console.log('\n🗑️  Step 3: Deleting fake email users...');
        const [fakeEmailResult] = await connection.execute(`
            DELETE FROM user WHERE 
                email_id LIKE '%@email.tst' OR
                email_id LIKE '%test@test%' OR
                email_id LIKE '%sample@%' OR
                email_id LIKE '%@dedatre.com' OR
                email_id LIKE '%@dmsdmg.com' OR
                email_id = '' OR
                email_id IS NULL OR
                email_id NOT LIKE '%@%.%'
        `);
        console.log(`   ✅ Deleted ${fakeEmailResult.affectedRows} fake email users`);
        
        // 4. Delete users from non-Canadian cities (keep only Canadian cities)
        console.log('\n🗑️  Step 4: Deleting non-Canadian users...');
        
        // Get all remaining cities to check
        const [remainingCities] = await connection.execute(`
            SELECT DISTINCT city, COUNT(*) as count 
            FROM user 
            GROUP BY city 
            ORDER BY count DESC
        `);
        
        let nonCanadianCities = [];
        remainingCities.forEach(row => {
            const city = (row.city || '').toLowerCase().trim();
            const isCanadian = canadianCities.includes(city) || 
                             canadianProvinces.includes(city) ||
                             city.includes('canada') ||
                             city.includes('canadian');
            
            if (!isCanadian && city !== '') {
                nonCanadianCities.push(row.city);
                console.log(`   🌍 Non-Canadian city found: ${row.city} (${row.count} users)`);
            }
        });
        
        if (nonCanadianCities.length > 0) {
            const placeholders = nonCanadianCities.map(() => '?').join(',');
            const [nonCanadianResult] = await connection.execute(
                `DELETE FROM user WHERE city IN (${placeholders})`,
                nonCanadianCities
            );
            console.log(`   ✅ Deleted ${nonCanadianResult.affectedRows} non-Canadian users`);
        }
        
        // 5. Delete duplicate users (same email)
        console.log('\n🗑️  Step 5: Removing duplicate users...');
        const [duplicateResult] = await connection.execute(`
            DELETE u1 FROM user u1
            INNER JOIN user u2 
            WHERE u1.id > u2.id AND u1.email_id = u2.email_id
        `);
        console.log(`   ✅ Deleted ${duplicateResult.affectedRows} duplicate users`);
        
        // 6. Delete users with incomplete data
        console.log('\n🗑️  Step 6: Deleting incomplete registrations...');
        const [incompleteResult] = await connection.execute(`
            DELETE FROM user WHERE 
                first_name = '' OR first_name IS NULL OR
                last_name = '' OR last_name IS NULL OR
                LENGTH(first_name) < 2 OR
                LENGTH(last_name) < 2
        `);
        console.log(`   ✅ Deleted ${incompleteResult.affectedRows} incomplete users`);
        
        // Get final count and show remaining users
        const [finalCount] = await connection.execute('SELECT COUNT(*) as total FROM user');
        console.log(`\n📊 Final user count: ${finalCount[0].total}`);
        console.log(`🎯 Cleaned up ${initialCount[0].total - finalCount[0].total} users!`);
        
        // Show remaining users
        console.log('\n✅ Remaining legitimate users:');
        const [remainingUsers] = await connection.execute(`
            SELECT id, first_name, last_name, email_id, city, created_at
            FROM user 
            ORDER BY created_at DESC
        `);
        
        if (remainingUsers.length === 0) {
            console.log('   ⚠️  No users remaining after cleanup');
        } else {
            remainingUsers.forEach(user => {
                console.log(`   ${user.id}: ${user.first_name} ${user.last_name} (${user.email_id}) - ${user.city}`);
            });
        }
        
        // Show final city breakdown
        console.log('\n📋 Final city breakdown:');
        const [finalCityBreakdown] = await connection.execute(`
            SELECT city, COUNT(*) as count 
            FROM user 
            GROUP BY city 
            ORDER BY count DESC
        `);
        
        if (finalCityBreakdown.length === 0) {
            console.log('   No cities remaining');
        } else {
            finalCityBreakdown.forEach(row => {
                console.log(`   ${row.city}: ${row.count} users`);
            });
        }
        
        console.log('\n🎉 Database cleanup completed successfully!');
        
    } catch (err) {
        console.error('❌ Error during cleanup:', err.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Database connection closed.');
        }
    }
}

// Run the cleanup
cleanupDatabase();
