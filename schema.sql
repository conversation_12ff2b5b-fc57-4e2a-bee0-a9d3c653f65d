-- Create database tables
CREATE TABLE IF NOT EXISTS `admin` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `surname` varchar(100) NOT NULL,
  `emailid` varchar(100) NOT NULL,
  `password` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `admin_log` (
  `message` varchar(300) NOT NULL,
  `date` date NOT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `lottery_master` (
  `id` int NOT NULL AUTO_INCREMENT,
  `lottery_name` varchar(45) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `draw_date` date DEFAULT NULL,
  `bumper_prize` decimal(15,2) DEFAULT NULL,
  `no_of_groups` int DEFAULT NULL,
  `person_per_group` int DEFAULT NULL,
  `country` varchar(45) DEFAULT NULL,
  `state` varchar(45) DEFAULT NULL,
  `is_active` tinyint DEFAULT '1',
  `is_deleted` tinyint DEFAULT '0',
  `created_by` varchar(45) DEFAULT NULL,
  `created_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_by` varchar(45) DEFAULT NULL,
  `modified_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `encore` tinyint DEFAULT NULL,
  `quickpick` tinyint DEFAULT '0',
  `custom_number` tinyint DEFAULT '0',
  `winning_prize` decimal(15,2) DEFAULT NULL,
  `winning_status` varchar(45) DEFAULT NULL,
  `lottery_price` decimal(10,2) DEFAULT NULL,
  `subscription_entry_price` decimal(10,2) DEFAULT NULL,
  `subscription_annual_price` decimal(10,2) DEFAULT NULL,
  `image` varchar(200) DEFAULT NULL,
  `date_of_joining` timestamp NULL DEFAULT NULL,
  `card_color_dark` varchar(45) DEFAULT NULL,
  `card_color_light` varchar(45) DEFAULT NULL,
  `card_color` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
