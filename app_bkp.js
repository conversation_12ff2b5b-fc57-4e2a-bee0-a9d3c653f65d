var createError = require('http-errors');
var express = require('express');
var bodyParser = require('body-parser');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
var session = require('express-session');
// var mysqlAdmin = require('node-mysql-admin');
var fs = require('fs');
const http = require('http')
const https = require('https')

/*
const mysql = require('mysql');
const con = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'lottery',
});
*/


var indexRouter = require('./routes/index');
var adminRouter = require('./routes/admin');
var apiRouter = require('./routes/api');

var app = express();

// Certificate for Domain 1
//const privateKey1 = fs.readFileSync('/etc/letsencrypt/live/letsplaygrouplottery.com/privkey.pem', 'utf8');
//const certificate1 = fs.readFileSync('/etc/letsencrypt/live/letsplaygrouplottery.com/cert.pem', 'utf8');
//const ca1 = fs.readFileSync('/etc/letsencrypt/live/letsplaygrouplottery.com/chain.pem', 'utf8');

const privateKey1 = fs.readFileSync('ssl/_.letsplaygrouplottery.com_private_key.key', 'utf8');
const certificate1 = fs.readFileSync('ssl/letsplaygrouplottery.com_ssl_certificate.cer', 'utf8');
const ca1 = fs.readFileSync('ssl/_.letsplaygrouplottery.com_ssl_certificate_INTERMEDIATE.cer', 'utf8');
const credentials1 = {
	key: privateKey1,
	cert: certificate1,
	ca: ca1
};

app.use(session({
  cookieName: 'session',
  secret: 'eg[isfd-8yF9-7w2315df{}+Ijsli;;to8',
  resave:false,
  saveUninitialized:false,
  cookie: { maxAge: 1800000  }
}));

/*
con.connect((err) => {
  if(err){
    console.log('Error connecting to Db');
    return;
  }
  console.log('Connection established1');
});
*/

//con.end((err) => {
  // The connection is terminated gracefully
  // Ensures all previously enqueued queries are still
  // before sending a COM_QUIT packet to the MySQL server.
//});

// app.use(app); // Admin

//app.engine('html', require('ejs').renderFile);

// view engine setup
app.set('views',[path.join(__dirname, 'views'),path.join(__dirname,'views/admin')]);
app.set('view engine','pug');
//app.set('view engine','html');
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended: true}));

app.use(logger('dev'));
//app.use(express.json());
//app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.static(__dirname, { dotfiles: 'allow' } ));
app.use('/images', express.static(path.join(__dirname, 'public/uploads')));
// app.use(app);
app.use("/", indexRouter);
app.use("/admin/",adminRouter);
app.use("/api",apiRouter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  next(createError(404));
});

// error handler
app.use(function(err, req, res, next) {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // render the error page
  res.status(err.status || 500);
  res.render('error');
});
require('dotenv').config();
const PORT = process.env.PORT|| 3000;
//app.listen(PORT, () => {
 
// console.log(` Server started on PORT: ${PORT}`)
//})

https.createServer(credentials1, app).listen(PORT);

// module.exports = app;
